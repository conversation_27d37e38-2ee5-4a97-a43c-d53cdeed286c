package com.lernen.cloud.sms.handler;

import java.util.List;

import com.lernen.cloud.core.api.sms.SMSPayloadWrapper;
import com.lernen.cloud.core.api.sms.msg91.UserSMSPayload;

/**
 * This is class to generate sms payload to be sent
 * 
 * <AUTHOR>
 *
 */
public interface ISMSPayloadBuilder {

	/**
	 * Sample payload that will be sent. It has to be ensured that character count
	 * does not change in final SMS payload with respect to preview payload. This
	 * payload is to determine the number of credits would be deducted based on SMS
	 * content
	 * 
	 * @return
	 */
	public SMSPayloadWrapper getSMSPreviewPayload();

	/**
	 * Any action that has to be performed before actually sending out the sms. This
	 * step comes after validating SMS availability etc
	 * 
	 * @return
	 */
	public boolean executeSMSAction();

	/**
	 * Flag to specify whether preview payload is final payload that has to be sent
	 * to users. If this is true, getFinalUserSMSPayload will be irrelevant
	 * 
	 * @return
	 */
	public boolean previewPayloadIsFinal();

	/**
	 * Final SMS Payload that has to be sent
	 * 
	 * @return
	 */
	public List<UserSMSPayload> getFinalUserSMSPayload();
}
