package com.lernen.cloud.sms.handler;

import com.embrate.cloud.core.api.service.communication.CommunicationServiceTransactionType;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.attendance.preference.StaffAttendancePreferences;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceRegisterData;
import com.lernen.cloud.core.api.attendance.staff.v3.StaffAttendanceStatusInput;
import com.lernen.cloud.core.api.attendance.staff.v3.StaffMarkAttendancePayload;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.notification.NotificationType;
import com.lernen.cloud.core.api.sms.SMSContentPayload;
import com.lernen.cloud.core.api.sms.SMSPayloadWrapper;
import com.lernen.cloud.core.api.sms.SMSResponse;
import com.lernen.cloud.core.api.sms.msg91.UserSMSPayload;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.attendance.staff.StaffAttendanceManager;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.attendance.staff.StaffAttendanceUtils;
import com.lernen.cloud.sms.content.builder.StaffAttendanceSMSContentBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class StaffAttendanceStatusSMSHandler {
    private static final Logger logger = LogManager.getLogger(StaffAttendanceStatusSMSHandler.class);
    private static final String STAFF_ATTENDANCE_STATUS_DETAILS = "staff_attendance_status_details";
    private static final Gson GSON = new Gson();
    private final StaffAttendanceSMSContentBuilder staffAttendanceSMSContentBuilder;
    private final SMSManager smsManager;
    private final UserPreferenceSettings userPreferenceSettings;
    private final UserPermissionManager userPermissionManager;
    private final StaffManager staffManager;
    private final StaffAttendanceManager staffAttendanceManager;
    private final UserManager userManager;

    public StaffAttendanceStatusSMSHandler(StaffAttendanceSMSContentBuilder staffAttendanceSMSContentBuilder, SMSManager smsManager,
                                     UserPreferenceSettings userPreferenceSettings, UserPermissionManager userPermissionManager,
                                     StaffManager staffManager, StaffAttendanceManager staffAttendanceManager, UserManager userManager) {
        this.staffAttendanceSMSContentBuilder = staffAttendanceSMSContentBuilder;
        this.smsManager = smsManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.userPermissionManager = userPermissionManager;
        this.staffManager = staffManager;
        this.staffAttendanceManager = staffAttendanceManager;
        this.userManager = userManager;
    }

    public List<SMSResponse> bulkSend(int instituteId, StaffMarkAttendancePayload staffAttendancePayload, UUID userId) {
        if (instituteId <= 0 || staffAttendancePayload == null || staffAttendancePayload.getAttendanceDate() <= 0
                || CollectionUtils.isEmpty(staffAttendancePayload.getStaffAttendanceStatusInputs())) {
            logger.error("Invalid institute {} or no staffAttendances to send notifications {} ", instituteId, staffAttendancePayload);
            return null;
        }
        int attendanceDate = staffAttendancePayload.getAttendanceDate();

        Map<UUID, StaffAttendanceStatus> staffIdAttendanceStatusMap = new HashMap<>();
        for(StaffAttendanceStatusInput staffAttendanceInput : staffAttendancePayload.getStaffAttendanceStatusInputs()) {
            if(staffAttendanceInput == null || staffAttendanceInput.getStaffId() == null) {
                continue;
            }
//            Time time = staffAttendanceInput.getTimeOfAction();
            StaffAttendanceStatus staffAttendanceStatus = staffAttendanceInput.getStaffAttendanceStatus();
            if(staffAttendanceStatus == null) {
                continue;
            }
            staffIdAttendanceStatusMap.put(staffAttendanceInput.getStaffId(), staffAttendanceStatus);
        }

        if(CollectionUtils.isEmpty(staffIdAttendanceStatusMap.entrySet())) {
            logger.warn("No staff found to send notifications");
            return null;
        }

        List<SMSResponse> smsResponseList = new ArrayList<>();
        for(Map.Entry<UUID, StaffAttendanceStatus> staffIdAttendanceStatusEntry : staffIdAttendanceStatusMap.entrySet()) {
            UUID staffId = staffIdAttendanceStatusEntry.getKey();
            StaffAttendanceStatus staffAttendanceStatus = staffIdAttendanceStatusEntry.getValue();
            SMSResponse smsResponse = bulkSend(instituteId, attendanceDate, staffAttendanceStatus, new HashSet<>(Collections.singletonList(staffId)), userId);
            if(smsResponse != null) {
                smsResponseList.add(smsResponse);
            }
        }

        return smsResponseList;
    }


    private SMSResponse bulkSend(int instituteId, int attendanceDateTime, StaffAttendanceStatus staffAttendanceStatus, Set<UUID> staffIds, UUID userId) {

        if (instituteId <= 0) {
            logger.error("invalid institute id.");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
        }


        if (CollectionUtils.isEmpty(staffIds)) {
            logger.error("invalid payload attendance payload to send sms");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid attendance payload to send sms"));
        }

        //Basic sms checks
        final MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
        if (!metaDataPreferences.isSmsServiceEnabled()) {
            logger.info("SMS service is not enabled for institute {}", instituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "SMS service is not enabled for your institute. Please contact support team for enabling this feature."));
        }
        if (StringUtils.isBlank(metaDataPreferences.getInstituteNameInSMS())) {
            logger.error("Institute name is not setup for sms sending {}", instituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Institute name is not setup for sms sending"));
        }

        //Staff Attendance sms checks
        StaffAttendancePreferences staffAttendancePreferences = userPreferenceSettings.getStaffAttendancePreferences(instituteId);
        Set<String> userNames = staffAttendancePreferences.getAdminStaffAttendanceSMSUserNames();
        //If no admin username is added to send notification, then return back.
        if (CollectionUtils.isEmpty(userNames)) {
            logger.info("Not sending the notification as staff attendance notification is not enable for institute {}", instituteId);
            return null;
        }
        if (staffAttendancePreferences.getAdminStaffAttendanceSMSTemplateId() == null) {
            logger.info("Staff attendance sms template is not set for institute {}", instituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Staff attendance sms template is not set for your institute. Please contact support team to set template."));
        }


        //Today date check for attendance
        int currentDate = DateUtils.now();
        if(DateUtils.getDayStart(attendanceDateTime, DateUtils.DEFAULT_TIMEZONE) != DateUtils.getDayStart(currentDate, DateUtils.DEFAULT_TIMEZONE)){
            logger.warn("Not sending the notification as notification date {} is not the current date {}", attendanceDateTime, currentDate);
            return null;
        }

        List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList = staffAttendanceManager.getStaffAttendanceRegister(
                instituteId, attendanceDateTime, staffIds);

        if(CollectionUtils.isEmpty(staffAttendanceRegisterDataList)) {
            logger.error("Invalid data found to send notification for institute {}", instituteId);
            return null;
        }

        List<User> userNameList = userManager.getUsersByUserNames(userNames);
        Set<UUID> adminStaffIds = new HashSet<>();
        for(User user : userNameList) {
            adminStaffIds.add(user.getUuid());
        }
        List<Staff> adminStaffList = staffManager.getStaffs(instituteId, adminStaffIds);
        if(CollectionUtils.isEmpty(adminStaffList)) {
            logger.error("Invalid staff found to send sms for institute {}", instituteId);
            return null;
        }

        List<Staff> staffList = staffManager.getStaffs(instituteId, staffIds);
        Map<UUID, Staff> staffMap = getStaffMap(staffList);
        if(staffMap == null || CollectionUtils.isEmpty(staffMap.entrySet())) {
            logger.warn("No staff found to send notifications");
            return null;
        }

        /**
         * combing data based on staffAttendanceType as they will have same content
         */
        Map<Integer, Map<StaffAttendanceStatus, List<StaffLite>>> dateStaffAttendanceTypeStaffListMap = StaffAttendanceUtils.getDateStaffAttendanceStatusStaffListMap(
                staffAttendanceRegisterDataList, staffMap);

        if(CollectionUtils.isEmpty(dateStaffAttendanceTypeStaffListMap.entrySet())) {
            logger.warn("No details found to send notifications");
            return null;
        }

        List<StaffAttendanceSMSDetails> staffAttendanceSMSDetailsList = new ArrayList<>();
        for(Map.Entry<Integer, Map<StaffAttendanceStatus, List<StaffLite>>> dateStaffAttendanceTypeStaffListEntry : dateStaffAttendanceTypeStaffListMap.entrySet()) {
            currentDate = DateUtils.getDayStartWithDefaultZone(DateUtils.now());
            int date = dateStaffAttendanceTypeStaffListEntry.getKey();
            if(currentDate != date) {
                continue;
            }
            Map<StaffAttendanceStatus, List<StaffLite>> staffAttendanceTypeListMap = dateStaffAttendanceTypeStaffListEntry.getValue();
            if(staffAttendanceTypeListMap == null || CollectionUtils.isEmpty(staffAttendanceTypeListMap.entrySet())) {
                continue;
            }
            for (Map.Entry<StaffAttendanceStatus, List<StaffLite>> staffAttendanceTypeListEntry : staffAttendanceTypeListMap.entrySet()) {
                if (staffAttendanceTypeListEntry == null || staffAttendanceTypeListEntry.getKey() == null ||
                        CollectionUtils.isEmpty(staffAttendanceTypeListEntry.getValue())) {
                    continue;
                }

                /**
                 * currently sending sms to only admin of institute
                 */
                for(Staff adminStaff : adminStaffList) {
                    for (StaffLite staffLite : staffAttendanceTypeListEntry.getValue()) {
                        staffAttendanceSMSDetailsList.add(new StaffAttendanceSMSDetails(
                                instituteId, staffLite.getStaffId(), staffLite.getName(),
                                staffLite.getStaffInstituteId(), adminStaff.getStaffBasicInfo().getPrimaryContactNumber(),
                                staffAttendanceTypeListEntry.getKey(), attendanceDateTime));
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(staffAttendanceSMSDetailsList)) {
            logger.error("No data found to send sms");
            return null;
        }

        return bulkSend(instituteId, staffAttendancePreferences.getAdminStaffAttendanceSMSTemplateId(),
                metaDataPreferences.getInstituteNameInSMS(), staffAttendanceSMSDetailsList, userId);
    }

    private Map<UUID, Staff> getStaffMap(List<Staff> staffList) {
        if(CollectionUtils.isEmpty(staffList)) {
            return null;
        }
        Map<UUID, Staff> staffMap = new HashMap<>();
        for(Staff staff : staffList) {
            if(staff.getStaffStatus() != StaffStatus.ONBOARD) {
                continue;
            }
            staffMap.put(staff.getStaffId(), staff);
        }
        return staffMap;
    }

    public SMSResponse bulkSend(int instituteId, UUID templateId, String instituteNameInSMS, List<StaffAttendanceSMSDetails> staffAttendanceSMSDetailsList, UUID userId) {

        if(userId == null) {
            List<User> systemUsers = userManager.getAllAdminUser(instituteId);
            if (CollectionUtils.isEmpty(systemUsers)) {
                logger.error("No system user found for institute {}", instituteId);
                return null;
            }
            // Selecting any system user
            userId = systemUsers.get(0).getUuid();
        }

        int today = DateUtils.now();
        String todayDate = DateUtils.getFormattedDate(today, DateUtils.DEFAULT_DATE_TIME_FORMAT, DateUtils.DEFAULT_TIMEZONE);
        String batchName = "Staff Attendance SMS - " + todayDate;
        UUID batchId = UUID.randomUUID();

        SMSResponse finalSMSResponse = smsManager.sendSMSAsync(instituteId,
                getSMSPayloadBuilder(batchId, batchName, templateId, instituteNameInSMS, staffAttendanceSMSDetailsList, NotificationType.ADMIN_STAFF_ATTENDANCE,
                        CommunicationServiceTransactionType.ADMIN_STAFF_ATTENDANCE_STATUS), userId);

        if (!finalSMSResponse.isSuccess()) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, finalSMSResponse.getFailureReason()));
        }

        return finalSMSResponse;
    }

    private ISMSPayloadBuilder getSMSPayloadBuilder(UUID batchId, String batchName, UUID templateId, String instituteNameInSMS, List<StaffAttendanceSMSDetails> staffAttendanceSMSDetailsList,
                                                    NotificationType notificationType, CommunicationServiceTransactionType communicationServiceTransactionType) {
        return new ISMSPayloadBuilder() {

            @Override
            public boolean previewPayloadIsFinal() {
                return true;
            }

            @Override
            public SMSPayloadWrapper getSMSPreviewPayload() {
                return bulkStaffSend(batchId, batchName, templateId, instituteNameInSMS, staffAttendanceSMSDetailsList,
                        notificationType, communicationServiceTransactionType);
            }

            @Override
            public List<UserSMSPayload> getFinalUserSMSPayload() {
                return null;
            }

            @Override
            public boolean executeSMSAction() {
                return true;
            }
        };
    }

    private SMSPayloadWrapper bulkStaffSend(UUID batchId, String batchName, UUID templateId, String instituteNameInSMS, List<StaffAttendanceSMSDetails> staffAttendanceSMSDetailsList,
                                            NotificationType notificationType, CommunicationServiceTransactionType communicationServiceTransactionType) {

        return getSMSPayloadWrapper(batchId, batchName, templateId, instituteNameInSMS, staffAttendanceSMSDetailsList,
                notificationType, communicationServiceTransactionType);
    }

    private SMSPayloadWrapper getSMSPayloadWrapper(UUID batchId, String batchName, UUID templateId, String instituteNameInSMS, List<StaffAttendanceSMSDetails> staffAttendanceSMSDetailsList, NotificationType notificationType,
                                                   CommunicationServiceTransactionType communicationServiceTransactionType) {
        if (CollectionUtils.isEmpty(staffAttendanceSMSDetailsList)) {
            logger.error("Invalid staff attendance details");
            return SMSPayloadWrapper.failurePayload("Invalid staff attendance details");
        }

        List<UserSMSPayload> userSMSPayloadList = new ArrayList<UserSMSPayload>();
        for (StaffAttendanceSMSDetails staffAttendanceSMSDetails : staffAttendanceSMSDetailsList) {
            Map<String, Object> metaData = new HashMap<>();
            SMSContentPayload textMessage = staffAttendanceSMSContentBuilder.generateTextContent(staffAttendanceSMSDetails, templateId, instituteNameInSMS);
            if (textMessage == null) {
                continue;
            }

            String primaryContactNumber = staffAttendanceSMSDetails.getPrimaryContactNumber();
            if (StringUtils.isBlank(primaryContactNumber)) {
                logger.error("Contact number not present for staff {}. Skipping staff attendance SMS.",
                        staffAttendanceSMSDetails.getStaffId());
                continue;
            }

            metaData.put(STAFF_ATTENDANCE_STATUS_DETAILS, GSON.toJson(staffAttendanceSMSDetails));

            userSMSPayloadList.add(new UserSMSPayload(staffAttendanceSMSDetails.getStaffId(),
                    Arrays.asList(primaryContactNumber), textMessage.getContent(), metaData, null, textMessage.getDltTemplateId()));
        }
        return new SMSPayloadWrapper(null, UserType.STAFF, notificationType, communicationServiceTransactionType,
                userSMSPayloadList, batchId, batchName);
    }

    public class StaffAttendanceSMSDetails {

        private final int instituteId;
        private final UUID staffId;
        private final String staffName;
        private final String staffInstituteId;
        private final String primaryContactNumber;
        private final StaffAttendanceStatus staffAttendanceStatus;

        private final int attendanceDateTime;

        public StaffAttendanceSMSDetails(int instituteId, UUID staffId, String staffName, String staffInstituteId, String primaryContactNumber, StaffAttendanceStatus staffAttendanceStatus, int attendanceDateTime) {
            this.instituteId = instituteId;
            this.staffId = staffId;
            this.staffName = staffName;
            this.staffInstituteId = staffInstituteId;
            this.primaryContactNumber = primaryContactNumber;
            this.staffAttendanceStatus = staffAttendanceStatus;
            this.attendanceDateTime = attendanceDateTime;
        }

        public int getInstituteId() {
            return instituteId;
        }

        public UUID getStaffId() {
            return staffId;
        }

        public String getStaffName() {
            return staffName;
        }

        public String getStaffInstituteId() {
            return staffInstituteId;
        }

        public String getPrimaryContactNumber() {
            return primaryContactNumber;
        }

        public StaffAttendanceStatus getStaffAttendanceStatus() {
            return staffAttendanceStatus;
        }

        public int getAttendanceDateTime() {
            return attendanceDateTime;
        }

        @Override
        public String toString() {
            return "StaffAttendanceSMSDetails{" +
                    "instituteId=" + instituteId +
                    ", staffId=" + staffId +
                    ", staffName='" + staffName + '\'' +
                    ", staffInstituteId='" + staffInstituteId + '\'' +
                    ", primaryContactNumber='" + primaryContactNumber + '\'' +
                    ", staffAttendanceStatus=" + staffAttendanceStatus +
                    ", attendanceDateTime=" + attendanceDateTime +
                    '}';
        }
    }
}
