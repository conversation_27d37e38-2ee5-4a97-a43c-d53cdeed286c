package com.lernen.cloud.sms;

import com.embrate.cloud.core.api.attendance.AttendanceInputType;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceInput;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendancePayloadV2;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.fees.payment.reminders.FeePaymentReminderPayload;
import com.lernen.cloud.core.api.institute.Time;
import com.lernen.cloud.core.api.notification.ExcelNotificationPayload;
import com.lernen.cloud.core.api.sms.SMSResponse;
import com.lernen.cloud.core.lib.attendance.staff.StaffAttendanceManager;
import com.lernen.cloud.sms.handler.DueFeesSMSHandler;
import com.lernen.cloud.sms.handler.ExcelSMSHandler;
import com.lernen.cloud.sms.handler.StaffAttendanceSMSHandler;
import org.springframework.context.ApplicationContext;

import com.lernen.cloud.core.api.notification.NotificationStatusResponse;
import com.lernen.cloud.core.api.sms.SMSSendDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.PhoneNumberUtils;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;
import com.lernen.cloud.sms.report.handler.PCExpertSMSStatusUpdaterService;
import com.lernen.cloud.sms.report.handler.WebPaySMSStatusUpdaterService;
import com.lernen.cloud.sms.service.PCExpertSMSSender;
import com.lernen.cloud.sms.service.WebPaySMSSender;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * Hello world!
 *
 */
public class App {
	public static void main(String[] args) {

		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("sms.xml");
//		final StaffAttendanceManager staffAttendanceManager = context.getBean(StaffAttendanceManager.class);
//		final StaffAttendanceSMSHandler staffAttendanceSMSHandler = context.getBean(StaffAttendanceSMSHandler.class);

//		Integer attendanceDate = **********;
//		List<StaffAttendanceInput> staffAttendanceInputList = new ArrayList<>();
//		UUID staffId = UUID.fromString("b320814c-b269-4ef7-8a04-a7eade96a4b7");
//		Time timeOfAction = new Time(10, 0, 0);
//		staffAttendanceInputList.add(new StaffAttendanceInput(staffId, timeOfAction));
//		StaffAttendancePayloadV2 staffAttendancePayload = new StaffAttendancePayloadV2(attendanceDate, staffAttendanceInputList);
//		final boolean success = staffAttendanceManager.saveAPIStaffAttendanceDetails(110, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"),
//				staffAttendancePayload);

		final ExcelSMSHandler excelSMSHandler = context.getBean(ExcelSMSHandler.class);
		ExcelNotificationPayload excelNotificationPayload = new ExcelNotificationPayload(
				10185, 180, "WBH5PK-1740647273763.xls", UUID.fromString("ea8041f3-607a-4b85-a312-e206c4fc438d"),
				DeliveryMode.SMS, "Testing"
		);
		final SMSResponse smsResponse = excelSMSHandler.bulkSend(10185, UUID.fromString("ea8041f3-607a-4b85-a312-e206c4fc438d"),
				excelNotificationPayload);

		System.out.println(smsResponse.toString());
//		if(success) {
//			staffAttendanceSMSHandler.bulkSend(110, staffAttendancePayload, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
//		}

//		System.out.println("Hello World!");
//		String e164PhoneNumber = PhoneNumberUtils.getE164FormattedNumber("************", "IN");
//		System.out.println(e164PhoneNumber);
//		e164PhoneNumber = PhoneNumberUtils.getE164FormattedNumber("8800616855", "IN");
//		System.out.println(e164PhoneNumber);
//		e164PhoneNumber = PhoneNumberUtils.getE164FormattedNumber("+************", "IN");
//		System.out.println(e164PhoneNumber);
//		e164PhoneNumber = PhoneNumberUtils.getE164FormattedNumber("08800616855", "IN");
//		System.out.println(e164PhoneNumber);
//
//		long v = DateUtils.getTimestampFromDate("2020-04-17T20:19:43.897", User.DFAULT_TIMEZONE,
//				"yyyy-MM-dd'T'HH:mm:ss.SSS") * 1000l;
//		System.out.println(v);

//		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("sms.xml");
//		final DueFeesSMSHandler dueFeesSMSHandler = context.getBean(DueFeesSMSHandler.class);
//
//		FeePaymentReminderPayload feePaymentReminderPayload = new FeePaymentReminderPayload();
//		feePaymentReminderPayload.setInstituteId(10030);
//		feePaymentReminderPayload.setAcademicSessionId(32);
//		feePaymentReminderPayload.setDeliveryMode(DeliveryMode.SMS);
//		feePaymentReminderPayload.setDueDate((int)System.currentTimeMillis());
//		feePaymentReminderPayload.setComputeFine(false);
//		feePaymentReminderPayload.setBatchName("Batch 1");
//		feePaymentReminderPayload.setRequiredStanardsCSV(null);
//		feePaymentReminderPayload.setStudentIds(new ArrayList<>(Arrays.asList(UUID.fromString("d0e0ac6b-1cc6-463c-ba61-c8d152e212a0"))));
//		feePaymentReminderPayload.setAudioTemplateId(null);
//
//		SMSResponse smsResponse = dueFeesSMSHandler.bulkSend(10030, feePaymentReminderPayload, UUID.fromString(
//				"4cb09c60-eabc-4192-a9d4-e357cf773db3"), true);
//
//		System.out.println("smsResponse : " + smsResponse);

//		int instituteId, FeePaymentReminderPayload feePaymentReminderPayload, UUID userId, boolean useTemplate

//		final WebPaySMSSender webPaySMSSender = context.getBean(WebPaySMSSender.class);
//		SMSSendDetails smsSendDetails = webPaySMSSender.sendSMS("+************", "Yup Testing message");
//		System.out.println(smsSendDetails);

//		final WebPaySMSStatusUpdaterService webPaySMSStatusUpdaterService = context
//				.getBean(WebPaySMSStatusUpdaterService.class);
//		NotificationStatusResponse notificationStatusResponse = webPaySMSStatusUpdaterService
//				.getNotificationStatus("a300-be2-12b8");
//		System.out.println(notificationStatusResponse);
	}
}
