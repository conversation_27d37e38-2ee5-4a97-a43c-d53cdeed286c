package com.lernen.cloud.sms.handler;

import com.embrate.cloud.core.api.service.communication.CommunicationServiceActionResponse;
import com.embrate.cloud.core.api.service.communication.UserCommunicationServicePayload;
import com.embrate.cloud.core.lib.service.communication.ICommunicationServiceHandler;
import com.lernen.cloud.core.api.exception.SMSRunTimeException;
import com.lernen.cloud.core.api.sms.SMSContentPayload;
import com.lernen.cloud.core.api.sms.SMSSendDetails;
import com.lernen.cloud.core.utils.PhoneNumberUtils;
import com.lernen.cloud.sms.service.ISMSSender;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.lernen.cloud.core.utils.PhoneNumberUtils.DEFAULT_COUNTRY;

/**
 * <AUTHOR>
 */
public class SMSServiceHand<PERSON> implements ICommunicationServiceHandler {
    private static final Logger logger = LogManager.getLogger(SMSServiceHandler.class);

    private final ISMSSender smsSender;

    public SMSServiceHandler(ISMSSender smsSender) {
        this.smsSender = smsSender;
    }

    @Override
    public  <T extends UserCommunicationServicePayload> CommunicationServiceActionResponse executeServiceAction(int instituteId, String destinationChannelId, T payload) {
        try {
            if (StringUtils.isBlank(payload.getMessagePayload())) {
                logger.error("Empty sms content for instituteId {}. Skipping sms!", instituteId);
                return null;
            }

            final String e164PhoneNumber = PhoneNumberUtils.getE164FormattedNumber(destinationChannelId, DEFAULT_COUNTRY);
            if (StringUtils.isBlank(e164PhoneNumber)) {
                logger.error("Invalid mobile number {} for user {} for message {}", e164PhoneNumber, payload.getMessagePayload());
                return null;
            }
            SMSSendDetails smsSendDetails = smsSender.sendSMS(instituteId, e164PhoneNumber, new SMSContentPayload(payload.getMessagePayload(), payload.getDltTemplateId()));
            if(smsSendDetails == null){
                return null;
            }
            return new CommunicationServiceActionResponse(smsSendDetails.getSmsUniqueId(), smsSendDetails.getE164PhoneNumber(), null, null);
        } catch (SMSRunTimeException e) {
            logger.error("Unable to send SMS to on number {}", destinationChannelId, e);
        } catch (Exception e) {
            logger.error("Unknown error occurred while sending SMS on number {}", destinationChannelId, e);
        }
        return null;

    }
}
