package com.lernen.cloud.core.lib.attendance.student;

import com.embrate.cloud.core.api.calendar.holiday.StaticHoliday;
import com.lernen.cloud.core.api.attendance.*;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.holiday.HolidayCalendarUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;

import java.util.*;

import static com.lernen.cloud.core.utils.DateUtils.DEFAULT_TIMEZONE;
import static com.lernen.cloud.core.utils.DateUtils.MONTH_YEAR_DATE_FORMAT;

/**
 * <AUTHOR>
 */
public abstract class StudentAttendanceCalculator {

    abstract AttendanceDateStatus getDateAttendanceStatus(int dayStart, int maxAttendanceShift, List<AttendanceTypeStatus> statusList, List<StaticHoliday> dayHolidays);

    abstract StudentAttendanceDurationSummary computeMonthAttendanceSummary(List<AttendanceDateStatus> dateStatusList);

    public StudentSessionAttendanceDetails getSessionAttendanceDetails(int instituteId, UUID studentId,
                                                                       AcademicSession academicSession,
                                                                       List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList,
                                                                       List<StaticHoliday> holidays) {
        TimeZone timeZone = DEFAULT_TIMEZONE;
        int currentDayStart = DateUtils.getDayStart(DateUtils.now(), timeZone);
        int firstDayStart = DateUtils.getDayStart(academicSession.getSessionStartTime(), timeZone);
        int lastDayStart = DateUtils.getDayStart(Math.min(currentDayStart, academicSession.getSessionEndTime()), timeZone);

        Map<Integer, List<StudentAttendanceRegisterPayload>> studentAttendanceMap = new HashMap<>();
        int maxAttendanceShift = populateAttendanceMapAndGetMaxShiftCount(studentAttendanceRegisterPayloadList, studentAttendanceMap);
        // Get holiday map for student
        Map<Integer, List<StaticHoliday>> holidayMap = HolidayCalendarUtils.getDayWiseHolidayMap(holidays, firstDayStart, lastDayStart);
        Map<String, List<AttendanceDateStatus>> monthAttendanceDateStatusMap = new LinkedHashMap<>();
        int iteratorDay = firstDayStart;
        AttendanceDateStatus currentDateAttendanceStatus = null;
        while (iteratorDay <= lastDayStart) {
            int dayStart = iteratorDay;
            String monthYear = DateUtils.getFormattedDate(dayStart, MONTH_YEAR_DATE_FORMAT, timeZone);
            List<StaticHoliday> dayHolidays = null;
            if (holidayMap.containsKey(dayStart)) {
                dayHolidays = holidayMap.get(dayStart);
            }
            List<AttendanceTypeStatus> statusList = new ArrayList<>();
            if (studentAttendanceMap.containsKey(dayStart)) {
                // compute final status
                List<StudentAttendanceRegisterPayload> dayAttendances = studentAttendanceMap.get(dayStart);
                if (CollectionUtils.isNotEmpty(dayAttendances)) {
                    for (StudentAttendanceRegisterPayload studentAttendanceRegisterPayload : dayAttendances) {
                        statusList.add(new AttendanceTypeStatus(studentAttendanceRegisterPayload.getAttendanceTypeId(), null, studentAttendanceRegisterPayload.getAttendanceStatus()));
                    }
                }
            }

            if (!monthAttendanceDateStatusMap.containsKey(monthYear)) {
                monthAttendanceDateStatusMap.put(monthYear, new ArrayList<>());
            }
            AttendanceDateStatus attendanceDateStatus = getDateAttendanceStatus(dayStart, maxAttendanceShift, statusList, dayHolidays);
            if (currentDayStart == dayStart) {
                currentDateAttendanceStatus = attendanceDateStatus;
            }
            monthAttendanceDateStatusMap.get(monthYear).add(attendanceDateStatus);
            iteratorDay = DateUtils.getNextDayStart(iteratorDay, timeZone);
        }

        List<StudentMonthAttendanceSummary> studentMonthAttendanceSummaryList = new ArrayList<>();
        for (Map.Entry<String, List<AttendanceDateStatus>> entry : monthAttendanceDateStatusMap.entrySet()) {
            DateTime dateTime = DateUtils.getDefaultZoneDateTime(entry.getValue().get(0).getAttendanceDay());
            studentMonthAttendanceSummaryList.add(new StudentMonthAttendanceSummary(dateTime.getMonthOfYear(), dateTime.getYear(), maxAttendanceShift, entry.getValue(), computeMonthAttendanceSummary(entry.getValue())));
        }

        return new StudentSessionAttendanceDetails(currentDateAttendanceStatus, academicSession.getSessionStartTime(), DateUtils.getDayStart(academicSession.getSessionEndTime(), timeZone), lastDayStart, studentMonthAttendanceSummaryList, getSessionSummary(studentMonthAttendanceSummaryList));
    }


    private StudentAttendanceDurationSummary getSessionSummary(List<StudentMonthAttendanceSummary> monthAttendanceSummaryList) {
        if (CollectionUtils.isEmpty(monthAttendanceSummaryList)) {
            return new StudentAttendanceDurationSummary();
        }

        double totalDays = 0;
        double totalWorkingDays = 0;
        double holidays = 0;
        double presentDays = 0;
        double absentDays = 0;
        double leaveDays = 0;
        double halfDays = 0;
        double unmarkedDays = 0;
        double netPresentDays = 0;
        double presentDaysPercentage;

        for (StudentMonthAttendanceSummary studentMonthAttendanceSummary : monthAttendanceSummaryList) {
            StudentAttendanceDurationSummary monthSummary = studentMonthAttendanceSummary.getAttendanceDurationSummary();
            totalDays += monthSummary.getTotalDays();
            totalWorkingDays += monthSummary.getTotalWorkingDays();
            holidays += monthSummary.getTotalHolidays();
            presentDays += monthSummary.getTotalPresentDays();
            absentDays += monthSummary.getTotalAbsentDays();
            leaveDays += monthSummary.getTotalLeaveDays();
            halfDays += monthSummary.getTotalHalfDays();
            unmarkedDays += monthSummary.getTotalUnMarkedDays();
            netPresentDays += monthSummary.getNetPresentDays();
        }
        presentDaysPercentage = Double.compare(totalWorkingDays, 0d) <= 0 ? 0d : netPresentDays / totalWorkingDays;

        return new StudentAttendanceDurationSummary(totalDays, holidays, totalWorkingDays, presentDays, absentDays, leaveDays, halfDays, unmarkedDays, netPresentDays, presentDaysPercentage);
    }

    private static int populateAttendanceMapAndGetMaxShiftCount(List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList, Map<Integer, List<StudentAttendanceRegisterPayload>> studentAttendanceMap) {
        int maxAttendanceShift = 1;
        if (CollectionUtils.isEmpty(studentAttendanceRegisterPayloadList)) {
            return maxAttendanceShift;
        }

        for (StudentAttendanceRegisterPayload studentAttendanceRegisterPayload : studentAttendanceRegisterPayloadList) {
            int attendanceDate = studentAttendanceRegisterPayload.getAttendanceDateTS();
            if (!studentAttendanceMap.containsKey(attendanceDate)) {
                studentAttendanceMap.put(attendanceDate, new ArrayList<>());
            }
            studentAttendanceMap.get(attendanceDate).add(studentAttendanceRegisterPayload);
            if (studentAttendanceMap.get(attendanceDate).size() > maxAttendanceShift) {
                maxAttendanceShift = studentAttendanceMap.get(attendanceDate).size();
            }
        }

        return maxAttendanceShift;
    }


    public StudentDateAttendanceDetails getStudentDateAttendanceDetails(int instituteId, int academicSessionId,
                int date, Integer attendanceType, Map<UUID, StudentAttendanceDetails> studentAttendanceDetailsMap,
                Map<UUID, List<StaticHoliday>> studentHolidayMap, int maxAttendanceShift, List<AttendanceType> attendanceTypeList) {
        TimeZone timeZone = DEFAULT_TIMEZONE;
        int dayStart = DateUtils.getDayStart(date, timeZone);
        int dayEnd = DateUtils.getDayEnd(date, timeZone);

        List<AttendanceDateStatus> attendanceDateStatusList = new ArrayList<>();
        Map<String, ClassAttendanceDataBuilder> classAttendanceAggregatedDataMap = new HashMap<>();
        for(Map.Entry<UUID, StudentAttendanceDetails> entry : studentAttendanceDetailsMap.entrySet()) {
            Map<Integer, List<StudentAttendanceRegisterPayload>> studentAttendanceMap = new HashMap<>();
            UUID studentId = entry.getKey();
            StudentAttendanceDetails studentAttendanceDetails = entry.getValue();
            String standardId = studentAttendanceDetails.getStandardId();
            Integer sectionId = studentAttendanceDetails.getSectionId();
            String standardName = studentAttendanceDetails.getStandardName();
            int level = studentAttendanceDetails.getLevel();
            String standardSectionIds = standardId + (sectionId == null || sectionId <= 0 ? "" : ":" + sectionId);
            populateAttendanceMapAndGetMaxShiftCount(studentAttendanceDetails.getStudentAttendanceRegisterPayloadList(), studentAttendanceMap);
            // Get holiday map for student
            Map<Integer, List<StaticHoliday>> holidayMap = new HashMap<>();
            if(studentHolidayMap != null && !CollectionUtils.isEmpty(studentHolidayMap.get(studentId))) {
                holidayMap = HolidayCalendarUtils.getDayWiseHolidayMap(studentHolidayMap.get(studentId), dayStart, dayEnd);
            }
            List<StaticHoliday> dayHolidays = null;
            if (holidayMap.containsKey(dayStart)) {
                dayHolidays = holidayMap.get(dayStart);
            }
            List<AttendanceTypeStatus> statusList = new ArrayList<>();
            // compute final status
            List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList1 = studentAttendanceMap.get(dayStart);
            if (CollectionUtils.isNotEmpty(studentAttendanceRegisterPayloadList1)) {
                for (StudentAttendanceRegisterPayload studentAttendanceRegisterPayload : studentAttendanceRegisterPayloadList1) {
                    statusList.add(new AttendanceTypeStatus(studentAttendanceRegisterPayload.getAttendanceTypeId(), null, studentAttendanceRegisterPayload.getAttendanceStatus()));
                }
            }
            AttendanceDateStatus attendanceDateStatus = getDateAttendanceStatus(dayStart, maxAttendanceShift, statusList, dayHolidays);
            attendanceDateStatusList.add(attendanceDateStatus);
            if(!classAttendanceAggregatedDataMap.containsKey(standardSectionIds)) {
                classAttendanceAggregatedDataMap.put(standardSectionIds,
                        new ClassAttendanceDataBuilder(instituteId, standardId, sectionId, standardName,
                                level, date, attendanceType, new ArrayList<>()));
            }
            ClassAttendanceDataBuilder classAttendanceDataBuilder = classAttendanceAggregatedDataMap.get(standardSectionIds);
            List<Pair<AttendanceStudentData, AttendanceDateStatus>> attendanceDateStatusPairList = classAttendanceDataBuilder.getAttendanceDateStatusList();
            attendanceDateStatusPairList.add(new ImmutablePair<>(new AttendanceStudentData(studentAttendanceDetails.getAdmissionNumber(), studentAttendanceDetails.getStudentName(), studentAttendanceDetails.getRollNumber(),
                    studentAttendanceDetails.getPrimaryContactNumber()), attendanceDateStatus));
        }

        if(CollectionUtils.isEmpty(classAttendanceAggregatedDataMap.entrySet())) {
            return getStudentDateAttendanceDetails(instituteId, academicSessionId,
                    DateUtils.getDayStart(date, timeZone), attendanceType, null, attendanceTypeList);
        }

        List<ClassAttendanceAggregatedData> classAttendanceAggregatedDataList = new ArrayList<>();
        for(ClassAttendanceDataBuilder classAttendanceDataBuilder : classAttendanceAggregatedDataMap.values()){
            classAttendanceAggregatedDataList.add(getClassAttendanceAggregatedData(classAttendanceDataBuilder));
        }

        Collections.sort(classAttendanceAggregatedDataList, new Comparator<ClassAttendanceAggregatedData>() {
            @Override
            public int compare(ClassAttendanceAggregatedData s1, ClassAttendanceAggregatedData s2) {
                return s1.getLevel() < s2.getLevel() ? -1 : 1;
            }
        });

        return getStudentDateAttendanceDetails(instituteId, academicSessionId,
                DateUtils.getDayStart(date, timeZone), attendanceType, classAttendanceAggregatedDataList, attendanceTypeList);
    }

    private StudentDateAttendanceDetails getStudentDateAttendanceDetails(int instituteId, int academicSessionId, int attendanceDay,
                                                                         Integer attendanceType,
                                                                         List<ClassAttendanceAggregatedData> classAttendanceAggregatedDataList,
                                                                         List<AttendanceType> attendanceTypeList) {

        double unmarkedDays = 0d;
        double totalHoliday = 0d;
        double totalHalfDayAttendance = 0d;
        double totalLeaveAttendance = 0d;
        double totalAbsentAttendance = 0d;
        double totalPresentAttendance = 0d;
        double totalAttendance = 0d;

        if (CollectionUtils.isNotEmpty(classAttendanceAggregatedDataList)) {
            for (ClassAttendanceAggregatedData classAttendanceAggregatedData : classAttendanceAggregatedDataList) {
                unmarkedDays += classAttendanceAggregatedData.getUnmarkedDays();
                totalHoliday += classAttendanceAggregatedData.getTotalHoliday();
                totalHalfDayAttendance += classAttendanceAggregatedData.getTotalHalfDayAttendance();
                totalLeaveAttendance += classAttendanceAggregatedData.getTotalLeaveAttendance();
                totalAbsentAttendance += classAttendanceAggregatedData.getTotalAbsentAttendance();
                totalPresentAttendance += classAttendanceAggregatedData.getTotalPresentAttendance();
                totalAttendance += classAttendanceAggregatedData.getTotalAttendance();
            }
        }
        double totalPresentPercentage = (totalPresentAttendance/totalAttendance)*100;

        return new StudentDateAttendanceDetails(instituteId, academicSessionId,
                attendanceDay, attendanceType, totalAttendance, totalPresentAttendance, totalAbsentAttendance,
                totalLeaveAttendance, totalHalfDayAttendance, totalHoliday, unmarkedDays,
                classAttendanceAggregatedDataList, attendanceTypeList, totalPresentPercentage);
    }

    private ClassAttendanceAggregatedData getClassAttendanceAggregatedData(ClassAttendanceDataBuilder classAttendanceDataBuilder) {
        if (classAttendanceDataBuilder == null) {
            return null;
        }


        double unmarkedDays = 0d;
        double totalHoliday = 0d;
        double totalHalfDayAttendance = 0d;
        double totalLeaveAttendance = 0d;
        double totalAbsentAttendance = 0d;
        double totalPresentAttendance = 0d;
        double totalAttendance = 0d;
        Map<FinalAttendanceStatus, List<AttendanceStudentData>> attendanceStatusStudentMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(classAttendanceDataBuilder.getAttendanceDateStatusList())) {
            for (Pair<AttendanceStudentData, AttendanceDateStatus> entry : classAttendanceDataBuilder.getAttendanceDateStatusList()) {
                if (entry == null) {
                    continue;
                }
                AttendanceStudentData attendanceStudentData = entry.getKey();
                AttendanceDateStatus attendanceDateStatus = entry.getValue();
                List<AttendanceStatusData> attendanceStatusDataList = attendanceDateStatus.getFinalStatusList();
                if (CollectionUtils.isEmpty(attendanceStatusDataList)) {
                    continue;
                }
                for (AttendanceStatusData attendanceStatusData : attendanceStatusDataList) {
                    if (attendanceStatusData.getStatus() == null) {
                        continue;
                    }
                    FinalAttendanceStatus finalAttendanceStatus = attendanceStatusData.getStatus();
                    if (!attendanceStatusStudentMap.containsKey(finalAttendanceStatus)) {
                        attendanceStatusStudentMap.put(finalAttendanceStatus, new ArrayList<>());
                    }

                    attendanceStatusStudentMap.get(finalAttendanceStatus).add(attendanceStudentData);

                    totalAttendance += attendanceStatusData.getWeightage();
                    switch (finalAttendanceStatus) {
                        case UNMARKED:
                            unmarkedDays += attendanceStatusData.getWeightage();
                            break;
                        case HOLIDAY:
                            totalHoliday += attendanceStatusData.getWeightage();
                            break;
                        case HALF_DAY:
                            totalHalfDayAttendance += attendanceStatusData.getWeightage();
                            break;
                        case LEAVE:
                            totalLeaveAttendance += attendanceStatusData.getWeightage();
                            break;
                        case ABSENT:
                            totalAbsentAttendance += attendanceStatusData.getWeightage();
                            break;
                        case PRESENT:
                            totalPresentAttendance += attendanceStatusData.getWeightage();
                            break;
                    }
                }
            }
        }
        return new ClassAttendanceAggregatedData(classAttendanceDataBuilder.getInstituteId(), classAttendanceDataBuilder.getStandardId(), classAttendanceDataBuilder.getSectionId(),
                classAttendanceDataBuilder.getStandardName(), classAttendanceDataBuilder.getLevel(), classAttendanceDataBuilder.getAttendanceDay(), classAttendanceDataBuilder.getAttendanceType(),
                totalAttendance, totalPresentAttendance, totalAbsentAttendance, totalLeaveAttendance, totalHalfDayAttendance, totalHoliday, unmarkedDays, attendanceStatusStudentMap);
    }
}
