package com.lernen.cloud.core.lib.inventory.reports;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TreeMap;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.inventory.InventoryUserType;
import com.lernen.cloud.core.api.inventory.ProductDetails;
import com.lernen.cloud.core.api.inventory.PurchasedProductSummary;
import com.lernen.cloud.core.api.inventory.TransactionRow;
import com.lernen.cloud.core.api.inventory.TransactionSummary;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.report.ReportType;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.inventory.ProductReportData;
import com.lernen.cloud.core.lib.inventory.StoreInventoryManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.reports.ReportGenerator;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.dao.tier.inventory.ProductTransactionsDao;

public class ReportGenerationManager extends ReportGenerator {
	
	private static final Logger logger = LogManager.getLogger(ReportGenerationManager.class);

	private final ProductTransactionsDao productTransactionsDao;
	
	private final StoreInventoryManager storeInventoryManager;
	
	private final UserManager userManager;

	private static final String[] INVENTORY_REPORT_COLUMNS = { "Product Name", "Opening Stock", "CLosing Stock",
			"Product Purchase Qty", "Product Sale Qty", "Purchase Amount", "Sell Amount", "Profit/Loss %" };

	private static final String[] PURCHASE_REPORT_COLUMN = { "Product Name", "Product Purchase Qty",
			"Purchase Amount" };

	private static final String[] SALE_REPORT_COLUMN = { "Product Name", "Product Sale Qty", "Sell Amount" };
	
	private static final String[] STOCK_REPORT_COLUMN = { "SKU", "Product Name", "Category", "Brand", "Available Quantity", 
			"Selling Price", "Discount", "Description" };
	
	private static final String[] SALE_TRANSACTION_REPORT_COLUMN = { "Transaction Number", "Customer Name", "Sold By", "Date", 
			"Payment Mode", "Total Units", "SubTotal", "Discount", "Tax", "Additional Cost", "Additional Discount", "Total(INR)",
			"Paid from wallet", "Credit Amount", "Paid Amount" };
	
	private static final String[] USER_DAY_LEVEL_COLLECTION = { "S.No", "Name", "Username", "Transaction Date",
			"Transaction Amount", "Discount", "Credit Given" };

	private static final String FROM_DATE = "From Date";
	private static final String TO_DATE = "To Date";
	private static final String DATE = "Date";
	private static final String REPORT_TYPE = "Report Type";
	private static final String TOTAL = "Total";
	private static final String EMPTY_TEXT = "";
	private static final String SALES_TRANSACTION = "Sales Transaction";
	private static final String USER_DAY_LEVEL_COLLECTIONS = "User Day Level Collections";

	public ReportGenerationManager(ProductTransactionsDao productTransactionsDao,
			StoreInventoryManager storeInventoryManager, UserManager userManager) {
		this.productTransactionsDao = productTransactionsDao;
		this.storeInventoryManager = storeInventoryManager;
		this.userManager = userManager;
	}

	private void setFromDateToDateFileRow(Sheet sheet, String fromDate, String toDate, String reportType) {

		final Row reportTypeRow = sheet.createRow(0);
		reportTypeRow.createCell(0).setCellValue(REPORT_TYPE);
		reportTypeRow.createCell(1).setCellValue(reportType);

		final Row fromDateRow = sheet.createRow(1);
		fromDateRow.createCell(0).setCellValue(FROM_DATE);
		fromDateRow.createCell(1).setCellValue(fromDate);

		final Row toDateRow = sheet.createRow(2);
		toDateRow.createCell(0).setCellValue(TO_DATE);
		toDateRow.createCell(1).setCellValue(toDate);

	}

	private String getDateStr(int timeInSec) {
		final Date date = new Date(timeInSec * 1000L);
		final DateFormat format = new SimpleDateFormat("dd/MMM/yyyy HH:mm:ss");
		format.setTimeZone(User.DFAULT_TIMEZONE);
		final String formattedDate = format.format(date);
		return formattedDate;
	}

	public ReportOutput generateReport(int instituteId, ReportType reportType, int start, int end,
			String inventoryTransactionType, UUID userId) {

		switch (reportType) {
		case INVENTORY:
			return generateInventoryReport(instituteId, start, end);
		case PURCHASE:
			return generatePurchaseReport(instituteId, start, end);
		case SALES:
			return generateSaleReport(instituteId, start, end);
		case STOCK:
			return generateStockReport(instituteId);
		case TRANSACTION:
			return generateTransactionReport(instituteId, start, end, inventoryTransactionType);
		case USER_LEVEL_DAY_WISE_REPORT:
			return generateUserWiseDayLevelCollectionReport(instituteId, start, end, inventoryTransactionType, userId);
		default:
			break;
		}
		return null;
	}

	public ReportOutput generateInventoryReport(int instituteId, int start, int end) {
		final List<TransactionRow> transactionRowList = productTransactionsDao.getInventorySummaryonDates(instituteId,
				start, end);

		final Map<UUID, ProductReportData> skuVsProductReportRowMap = getSkuVsProductReportRowMap(transactionRowList);

		final Workbook workbook = new XSSFWorkbook(); // new HSSFWorkbook() for
		// generating `.xlsx` file

		/*
		 * CreationHelper helps us create instances of various things like
		 * DataFormat, Hyperlink, RichTextString etc, in a format (HSSF, XSSF)
		 * independent way
		 */
		// CreationHelper createHelper = workbook.getCreationHelper();

		// Create a Font for styling header cells
		final Font headerFont = workbook.createFont();
		headerFont.setBold(true);
		headerFont.setFontHeightInPoints((short) 14);
		headerFont.setColor(IndexedColors.BLACK.getIndex());

		// Create a CellStyle with the font
		final CellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);

		// Create a Sheet
		final Sheet sheet = workbook.createSheet("Sale");

		final String fromDate = getDateStr(start);
		final String toDate = getDateStr(end);

		setFromDateToDateFileRow(sheet, fromDate, toDate, ReportType.INVENTORY.name());
		// Create a Row
		final Row headerRow = sheet.createRow(4);

		// Create cells
		for (int i = 0; i < INVENTORY_REPORT_COLUMNS.length; i++) {
			final Cell cell = headerRow.createCell(i);
			cell.setCellValue(INVENTORY_REPORT_COLUMNS[i]);
			cell.setCellStyle(headerCellStyle);
		}

		if (skuVsProductReportRowMap != null && !skuVsProductReportRowMap.isEmpty()) {
			// Create Other rows and cells with inventory data
			int rowNum = 5;

			final Iterator<Map.Entry<UUID, ProductReportData>> skuVsProductReportRowMapItr = skuVsProductReportRowMap
					.entrySet().iterator();
			while (skuVsProductReportRowMapItr.hasNext()) {
				final Map.Entry<UUID, ProductReportData> skuVsProductReportRowMapEntry = skuVsProductReportRowMapItr
						.next();
				final ProductReportData productReportData = skuVsProductReportRowMapEntry.getValue();
				final Row row = sheet.createRow(rowNum++);
				int colNum = 0;
				row.createCell(colNum++).setCellValue(productReportData.getProductName());
				row.createCell(colNum++).setCellValue(productReportData.getOpeningStock());
				row.createCell(colNum++).setCellValue(productReportData.getClosingStock());
				row.createCell(colNum++).setCellValue(productReportData.getProductPurchasedQty());
				row.createCell(colNum++).setCellValue(productReportData.getProductSaleQty());
				row.createCell(colNum++).setCellValue(productReportData.getPurchaseAmount());
				row.createCell(colNum++).setCellValue(productReportData.getSaleAmount());
				row.createCell(colNum++).setCellValue(productReportData.getProfitOrLoss());
			}
		}
		// Resize all columns to fit the content size
		for (int i = 0; i < INVENTORY_REPORT_COLUMNS.length; i++) {
			sheet.autoSizeColumn(i);
		}
		final ByteArrayOutputStream bos = new ByteArrayOutputStream();
		try {
			workbook.write(bos);
			workbook.close();
			return new ReportOutput("Inventory.xlsx", bos);
		} catch (final Exception e) {
			logger.error("Error while generating inventory report", e);
		}
		return null;
	}

	public ReportOutput generatePurchaseReport(int instituteId, int start, int end) {
		final List<TransactionRow> transactionRowList = productTransactionsDao
				.getPurchaseOrSaleSummaryonDates(instituteId, start, end, InventoryTransactionType.PURCHASE);

		final Map<UUID, ProductReportData> skuVsProductReportRowMap = getSkuVsProductReportRowMap(transactionRowList);

		final Workbook workbook = new XSSFWorkbook(); // new HSSFWorkbook() for
		// generating `.xlsx` file

		/*
		 * CreationHelper helps us create instances of various things like
		 * DataFormat, Hyperlink, RichTextString etc, in a format (HSSF, XSSF)
		 * independent way
		 */
		// CreationHelper createHelper = workbook.getCreationHelper();

		// Create a Font for styling header cells
		final Font headerFont = workbook.createFont();
		headerFont.setBold(true);
		headerFont.setFontHeightInPoints((short) 14);
		headerFont.setColor(IndexedColors.BLACK.getIndex());

		// Create a CellStyle with the font
		final CellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);

		// Create a Sheet
		final Sheet sheet = workbook.createSheet("Sale");
		final String fromDate = getDateStr(start);
		final String toDate = getDateStr(end);

		setFromDateToDateFileRow(sheet, fromDate, toDate, ReportType.PURCHASE.name());
		// Create a Row
		final Row headerRow = sheet.createRow(4);

		// Create cells
		for (int i = 0; i < PURCHASE_REPORT_COLUMN.length; i++) {
			final Cell cell = headerRow.createCell(i);
			cell.setCellValue(PURCHASE_REPORT_COLUMN[i]);
			cell.setCellStyle(headerCellStyle);
		}

		if (skuVsProductReportRowMap != null && !skuVsProductReportRowMap.isEmpty()) {
			// Create Other rows and cells with inventory data
			int rowNum = 5;

			final Iterator<Map.Entry<UUID, ProductReportData>> skuVsProductReportRowMapItr = skuVsProductReportRowMap
					.entrySet().iterator();
			while (skuVsProductReportRowMapItr.hasNext()) {
				final Map.Entry<UUID, ProductReportData> skuVsProductReportRowMapEntry = skuVsProductReportRowMapItr
						.next();
				final ProductReportData productReportData = skuVsProductReportRowMapEntry.getValue();
				final Row row = sheet.createRow(rowNum++);
				int colNum = 0;
				row.createCell(colNum++).setCellValue(productReportData.getProductName());
				row.createCell(colNum++).setCellValue(productReportData.getProductPurchasedQty());
				row.createCell(colNum++).setCellValue(productReportData.getPurchaseAmount());
			}
		}
		// Resize all columns to fit the content size
		for (int i = 0; i < PURCHASE_REPORT_COLUMN.length; i++) {
			sheet.autoSizeColumn(i);
		}

		final ByteArrayOutputStream bos = new ByteArrayOutputStream();
		try {
			workbook.write(bos);
			workbook.close();
			return new ReportOutput("PurchaseReport.xlsx", bos);
		} catch (final Exception e) {
			logger.error("Error while generating purchase report", e);
		}

		return null;
	}

	public ReportOutput generateSaleReport(int instituteId, int start, int end) {
		final List<TransactionRow> transactionRowList = productTransactionsDao
				.getPurchaseOrSaleSummaryonDates(instituteId, start, end, InventoryTransactionType.SALE);

		final Map<UUID, ProductReportData> skuVsProductReportRowMap = getSkuVsProductReportRowMap(transactionRowList);

		final Workbook workbook = new XSSFWorkbook(); // new HSSFWorkbook() for
		// generating `.xlsx` file

		/*
		 * CreationHelper helps us create instances of various things like
		 * DataFormat, Hyperlink, RichTextString etc, in a format (HSSF, XSSF)
		 * independent way
		 */
		// CreationHelper createHelper = workbook.getCreationHelper();

		// Create a Font for styling header cells
		final Font headerFont = workbook.createFont();
		headerFont.setBold(true);
		headerFont.setFontHeightInPoints((short) 14);
		headerFont.setColor(IndexedColors.BLACK.getIndex());

		// Create a CellStyle with the font
		final CellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);

		// Create a Sheet
		final Sheet sheet = workbook.createSheet("Sale");

		final String fromDate = getDateStr(start);
		final String toDate = getDateStr(end);

		setFromDateToDateFileRow(sheet, fromDate, toDate, ReportType.SALES.name());
		// Create a Row
		final Row headerRow = sheet.createRow(4);

		// Create cells
		for (int i = 0; i < SALE_REPORT_COLUMN.length; i++) {
			final Cell cell = headerRow.createCell(i);
			cell.setCellValue(SALE_REPORT_COLUMN[i]);
			cell.setCellStyle(headerCellStyle);
		}

		if (skuVsProductReportRowMap != null && !skuVsProductReportRowMap.isEmpty()) {
			// Create Other rows and cells with inventory data
			int rowNum = 5;

			final Iterator<Map.Entry<UUID, ProductReportData>> skuVsProductReportRowMapItr = skuVsProductReportRowMap
					.entrySet().iterator();
			while (skuVsProductReportRowMapItr.hasNext()) {
				final Map.Entry<UUID, ProductReportData> skuVsProductReportRowMapEntry = skuVsProductReportRowMapItr
						.next();
				final ProductReportData productReportData = skuVsProductReportRowMapEntry.getValue();
				final Row row = sheet.createRow(rowNum++);
				int colNum = 0;
				row.createCell(colNum++).setCellValue(productReportData.getProductName());
				row.createCell(colNum++).setCellValue(productReportData.getProductSaleQty());
				row.createCell(colNum++).setCellValue(productReportData.getSaleAmount());
			}
		}
		// Resize all columns to fit the content size
		for (int i = 0; i < SALE_REPORT_COLUMN.length; i++) {
			sheet.autoSizeColumn(i);
		}
		final ByteArrayOutputStream bos = new ByteArrayOutputStream();
		try {
			workbook.write(bos);
			workbook.close();
			return new ReportOutput("SalesReport.xlsx", bos);
		} catch (final Exception e) {
			logger.error("Error while generating sales report", e);
		}

		return null;
	}
	
	private ReportOutput generateStockReport(int instituteId) {

		try {
			List<ProductDetails> productDetailsList = storeInventoryManager.searchProducts(instituteId, "", 0, 0);
			
			final Workbook workbook = new XSSFWorkbook();
	
			final String reportName = "StockReport.xlsx";			
			
			final Sheet sheet = workbook.createSheet("Stock");
			
			final Font headerFont = workbook.createFont();
			headerFont.setBold(true);
			headerFont.setFontHeightInPoints((short) 16);
			headerFont.setColor(IndexedColors.BLACK.getIndex());

			// Create a CellStyle with the font
			final CellStyle headerCellStyle = workbook.createCellStyle();
			headerCellStyle.setFont(headerFont);
			
			final Row reportTypeRow = sheet.createRow(0);
			reportTypeRow.createCell(0).setCellValue(REPORT_TYPE);
			reportTypeRow.createCell(1).setCellValue(ReportType.STOCK.name());
	
			final Row fromDateRow = sheet.createRow(1);
			fromDateRow.createCell(0).setCellValue(DATE);
			fromDateRow.createCell(1).setCellValue(getDateStr(DateUtils.now()));
			
			// Create a Row
			final Row headerRow = sheet.createRow(4);
	
			// Create cells
			for (int i = 0; i < STOCK_REPORT_COLUMN.length; i++) {
				final Cell cell = headerRow.createCell(i);
				cell.setCellValue(STOCK_REPORT_COLUMN[i]);
				cell.setCellStyle(headerCellStyle);
			}
			
			if(CollectionUtils.isEmpty(productDetailsList)) {
				logger.info("Empty report {}", reportName);
				return getReportOutput(workbook, reportName);
			}
	
			int rowNum = 5;
			for(ProductDetails productDetails : productDetailsList) {
				final Row row = sheet.createRow(rowNum++);
				int colNum = 0;

				row.createCell(colNum++).setCellValue(productDetails.getSkuId().toString());
				row.createCell(colNum++).setCellValue(productDetails.getProductName());
				row.createCell(colNum++).setCellValue(productDetails.getCategory() == null ? "" : productDetails.getCategory().getCategoryName());
				row.createCell(colNum++).setCellValue(productDetails.getBrand() == null ? "" : productDetails.getBrand().getBrandName());
				row.createCell(colNum++).setCellValue(productDetails.getTotalQuantity() == null ? 0 : productDetails.getTotalQuantity());
				row.createCell(colNum++).setCellValue(productDetails.getSellingPrice() == null ? "" : productDetails.getSellingPrice().toString());
				row.createCell(colNum++).setCellValue(productDetails.getDiscount() == null ? 0 : productDetails.getDiscount());
				row.createCell(colNum++).setCellValue(productDetails.getDescription());
			}
			
			for (int i = 0; i < STOCK_REPORT_COLUMN.length; i++) {
				sheet.autoSizeColumn(i);
			}
			
			return getReportOutput(workbook, reportName);
		} catch (final Exception e) {
			logger.error("Error while generating stock report", e);
		}

		return null;
	}
	
	private ReportOutput generateTransactionReport(int instituteId, int start, int end,
			String inventoryTransactionTypeStr) {
		InventoryTransactionType inventoryTransactionType = InventoryTransactionType.valueOf(inventoryTransactionTypeStr);
		switch (inventoryTransactionType) {
		case SALE:
			return generateSaleTransactionReport(instituteId, start, end, inventoryTransactionType);
		default:
			break;
		}
		return null;
	}

	private ReportOutput generateSaleTransactionReport(int instituteId, int start, int end,
			InventoryTransactionType inventoryTransactionType) {
		try {
			
			List<TransactionSummary> transactionSummaryList = productTransactionsDao.getTransactionDetails(
					instituteId, start, end, inventoryTransactionType, null);
			
			Collections.sort(transactionSummaryList, new Comparator<TransactionSummary>() {
				@Override
				public int compare(TransactionSummary ts1, TransactionSummary ts2) {
					return (int)(ts1.getTransactionDate() - ts2.getTransactionDate());
				}
			});
			
			final Workbook workbook = new XSSFWorkbook();
	
			final String reportName = "SalesTransactionReport.xlsx";			
			
			final Sheet sheet = workbook.createSheet("Sales");
			
			final Font headerFont = workbook.createFont();
			headerFont.setBold(true);
			headerFont.setFontHeightInPoints((short) 16);
			headerFont.setColor(IndexedColors.BLACK.getIndex());

			// Create a CellStyle with the font
			final CellStyle headerCellStyle = workbook.createCellStyle();
			headerCellStyle.setFont(headerFont);
			
			final Row reportTypeRow = sheet.createRow(0);
			reportTypeRow.createCell(0).setCellValue(REPORT_TYPE);
			reportTypeRow.createCell(1).setCellValue(SALES_TRANSACTION);
	
			final Row fromDateRow = sheet.createRow(1);
			fromDateRow.createCell(0).setCellValue(DATE);
			fromDateRow.createCell(1).setCellValue(getDateStr(DateUtils.now()));
			
			// Create a Row
			final Row headerRow = sheet.createRow(4);
	
			// Create cells
			for (int i = 0; i < SALE_TRANSACTION_REPORT_COLUMN.length; i++) {
				final Cell cell = headerRow.createCell(i);
				cell.setCellValue(SALE_TRANSACTION_REPORT_COLUMN[i]);
				cell.setCellStyle(headerCellStyle);
			}
			
			if(CollectionUtils.isEmpty(transactionSummaryList)) {
				logger.info("Empty report {}", reportName);
				return getReportOutput(workbook, reportName);
			}
	
			int rowNum = 5;
			
			double rowTotalUnits = 0;
			double rowTotalSubtotal = 0;
			double rowTotalDiscount = 0;
			double rowTotalTax = 0;
			double rowTotalAdditionalCost = 0;
			double rowTotalAdditionalDiscount = 0;
			double rowTotalINR = 0;
			double rowTotalPaidFromWallet = 0;
			double rowTotalCreditAmount = 0;
			double rowTotalPaidAmount = 0;
			
			for(TransactionSummary transactionSummary : transactionSummaryList) {
				final Row row = sheet.createRow(rowNum++);
				int colNum = 0;
				
				row.createCell(colNum++).setCellValue(transactionSummary.getTransactionId().toString());
				row.createCell(colNum++).setCellValue(transactionSummary.getInventoryUserType() == InventoryUserType.STUDENT 
						? transactionSummary.getStudentLite().getName() 
						+ " (" + transactionSummary.getStudentLite().getAdmissionNumber() + ")" 
						: transactionSummary.getBuyerName());
				row.createCell(colNum++).setCellValue(transactionSummary.getTransactionBy());
				row.createCell(colNum++).setCellValue(DateUtils.getFormattedDate((int)(transactionSummary.getTransactionDate() / 1000)));
				row.createCell(colNum++).setCellValue(transactionSummary.getTransactionModeDisplayName());
				double totalUnits = 0;
				double subtotal = 0;
				double discount = 0;
				double tax = 0;
				double totalINR = 0;
				for(PurchasedProductSummary purchasedProductSummary : transactionSummary.getPurchasedProducts()) {
					totalUnits += purchasedProductSummary.getQuantity();
					subtotal += purchasedProductSummary.getTotalPrice();
					discount += purchasedProductSummary.getTotalDiscount();
					tax += purchasedProductSummary.getTotalTax();
					totalINR += purchasedProductSummary.getTotalPrice() - purchasedProductSummary.getTotalDiscount() 
							+ purchasedProductSummary.getTotalTax();
				}
				rowTotalUnits += totalUnits;
				row.createCell(colNum++).setCellValue(totalUnits);
				
				rowTotalSubtotal += subtotal;
				row.createCell(colNum++).setCellValue(subtotal);
				
				rowTotalDiscount += discount;
				row.createCell(colNum++).setCellValue(discount);
				
				rowTotalTax += tax;
				row.createCell(colNum++).setCellValue(tax);
				
				rowTotalAdditionalCost += transactionSummary.getAdditionalCost();
				row.createCell(colNum++).setCellValue(transactionSummary.getAdditionalCost());
				
				rowTotalAdditionalDiscount += transactionSummary.getAdditionalDiscount();
				row.createCell(colNum++).setCellValue(transactionSummary.getAdditionalDiscount());
				
				totalINR += transactionSummary.getAdditionalCost() - transactionSummary.getAdditionalDiscount();
				rowTotalINR += totalINR; 
				row.createCell(colNum++).setCellValue(totalINR);

				rowTotalPaidFromWallet += transactionSummary.getUsedWalletAmount();
				row.createCell(colNum++).setCellValue(transactionSummary.getUsedWalletAmount());
				
				rowTotalCreditAmount += transactionSummary.getWalletCreditAmount();
				row.createCell(colNum++).setCellValue(transactionSummary.getWalletCreditAmount());
				
				rowTotalPaidAmount += transactionSummary.getPaidAmount();
				row.createCell(colNum++).setCellValue(transactionSummary.getPaidAmount());
				
			}
			
			final Row row = sheet.createRow(rowNum++);
			int colNum = 0;
			row.createCell(colNum++).setCellValue(TOTAL);
			row.createCell(colNum++).setCellValue("");
			row.createCell(colNum++).setCellValue("");
			row.createCell(colNum++).setCellValue("");
			row.createCell(colNum++).setCellValue(EMPTY_TEXT);
			row.createCell(colNum++).setCellValue(rowTotalUnits);
			row.createCell(colNum++).setCellValue(rowTotalSubtotal);
			row.createCell(colNum++).setCellValue(rowTotalDiscount);
			row.createCell(colNum++).setCellValue(rowTotalTax);
			row.createCell(colNum++).setCellValue(rowTotalAdditionalCost);
			row.createCell(colNum++).setCellValue(rowTotalAdditionalDiscount);
			row.createCell(colNum++).setCellValue(rowTotalINR);
			row.createCell(colNum++).setCellValue(rowTotalPaidFromWallet);
			row.createCell(colNum++).setCellValue(rowTotalCreditAmount);
			row.createCell(colNum++).setCellValue(rowTotalPaidAmount);
			
			for (int i = 0; i < SALE_TRANSACTION_REPORT_COLUMN.length; i++) {
				sheet.autoSizeColumn(i);
			}
			
			return getReportOutput(workbook, reportName);
		} catch (final Exception e) {
			logger.error("Error while generating stock report", e);
		}		
		
		return null;
	}
	
	private ReportOutput generateUserWiseDayLevelCollectionReport(int instituteId, Integer start,
			Integer end, String inventoryTransactionTypeStr, UUID userId) {
		InventoryTransactionType inventoryTransactionType = InventoryTransactionType.valueOf(inventoryTransactionTypeStr);
		final User user = userManager.getUser(userId);
		boolean allUsers = false;
		if (user.getUserType() == UserType.ADMIN || user.getUserType() == UserType.INTERNAL) {
			allUsers = true;
		}
		try {
			List<TransactionSummary> transactionSummaryList = null;

			if (allUsers) {
				transactionSummaryList = productTransactionsDao.getTransactionDetails(
						instituteId, start, end, inventoryTransactionType, null);
			} else {
				transactionSummaryList = productTransactionsDao.getTransactionDetails(
						instituteId, start, end, inventoryTransactionType, userId);
			}

			final Map<Integer, Map<UUID, UserDayCollection>> userDayCollectionMap = new TreeMap<>();
			final UUID unknownUser = UUID.randomUUID();
			for (final TransactionSummary transactionSummary : transactionSummaryList) {
				UUID transactionBy = transactionSummary.getTransactionUUIDBy();
				if (transactionBy == null) {
					transactionBy = unknownUser;
				}
				final int startDay = DateUtils.getDayStart((int)(transactionSummary.getTransactionDate() / 1000l),
						DateUtils.DEFAULT_TIMEZONE);
				
				if(userDayCollectionMap.containsKey(startDay)) {
					if(userDayCollectionMap.get(startDay).containsKey(transactionBy)) {
						
						UserDayCollection userDayCollection = userDayCollectionMap.get(startDay).get(transactionBy);						
						double transactionAmount = transactionSummary.getAdditionalCost();
						double discount = transactionSummary.getAdditionalDiscount();
						for(PurchasedProductSummary purchasedProductSummary : transactionSummary.getPurchasedProducts()) {
							transactionAmount += purchasedProductSummary.getTotalPrice();
							transactionAmount += purchasedProductSummary.getTotalTax();
							discount += purchasedProductSummary.getTotalDiscount();
						}
						userDayCollection.setTransactionAmount(userDayCollection.getTransactionAmount() + transactionAmount);
						userDayCollection.setDiscount(userDayCollection.getDiscount() + discount);
						userDayCollection.setCreditGiven(userDayCollection.getCreditGiven() + transactionSummary.getWalletCreditAmount());						
						userDayCollectionMap.get(startDay).put(transactionBy, userDayCollection);
						
					} else {
						UserDayCollection userDayCol = new UserDayCollection(startDay, 0d, 0d, 0d);
						UserDayCollection userDayCollection = getUserDayCollectionDetails(userDayCol, transactionSummary);		
						userDayCollectionMap.get(startDay).put(transactionBy, userDayCollection);
					}
				} else {
					UserDayCollection userDayCol = new UserDayCollection(startDay, 0d, 0d, 0d);
					UserDayCollection userDayCollection = getUserDayCollectionDetails(userDayCol, transactionSummary);
					Map<UUID, UserDayCollection> userDayColMap = new HashMap<UUID, UserDayCollection>();
					userDayColMap.put(transactionBy, userDayCollection);
					userDayCollectionMap.put(startDay, userDayColMap);
				}
			}
			

			final Map<UUID, User> userDetailsMap = new LinkedHashMap<>();
			if (allUsers) {
				Set<UUID> userSet = new HashSet<UUID>();
				for(Map<UUID, UserDayCollection> uuidUserCollectionSet : userDayCollectionMap.values()) {
					userSet.addAll(uuidUserCollectionSet.keySet());
				}
				final List<User> users = userManager.getUsers(instituteId, userSet);

				Collections.sort(users, new Comparator<User>() {
					@Override
					public int compare(User u1, User u2) {
						return u1.getFirstName().compareTo(u2.getFirstName());
					}
				});

				for (final User userObj : users) {
					userDetailsMap.put(userObj.getUuid(), userObj);
				}
			} else {
				userDetailsMap.put(userId, user);
			}
			
			final Workbook workbook = new XSSFWorkbook();
			
			final String reportName = "UserDayLevelCollection.xlsx";			
			
			final Sheet sheet = workbook.createSheet("UserDailyCollection");

			final Font headerFont = workbook.createFont();
			headerFont.setBold(true);
			headerFont.setFontHeightInPoints((short) 16);
			headerFont.setColor(IndexedColors.BLACK.getIndex());

			// Create a CellStyle with the font
			final CellStyle headerCellStyle = workbook.createCellStyle();
			headerCellStyle.setFont(headerFont);
			
			final Row reportTypeRow = sheet.createRow(0);
			reportTypeRow.createCell(0).setCellValue(REPORT_TYPE);
			reportTypeRow.createCell(1).setCellValue(USER_DAY_LEVEL_COLLECTIONS);
	
			final Row fromDateRow = sheet.createRow(1);
			fromDateRow.createCell(0).setCellValue(DATE);
			fromDateRow.createCell(1).setCellValue(getDateStr(DateUtils.now()));
			
			final List<Integer> columnWidths = addHeader(workbook, sheet, 4, USER_DAY_LEVEL_COLLECTION);

			int rowNum = 5;
			int count = 1;
			if (!CollectionUtils.isEmpty(userDetailsMap)) {
				// Create Other rows and cells with inventory data
				for (final Entry<Integer, Map<UUID, UserDayCollection>> dayUserLevelCollection 
							: userDayCollectionMap.entrySet()) {
					final Map<UUID, UserDayCollection> dayLevelCollection = dayUserLevelCollection.getValue();
					for (final Entry<UUID, UserDayCollection> dayCollectionEntry : dayLevelCollection.entrySet()) {
						User userEntry = userDetailsMap.containsKey(dayCollectionEntry.getKey()) ? 
								userDetailsMap.get(dayCollectionEntry.getKey()) : null;
						if(userEntry == null) {
							continue;
						}
						final Row row = sheet.createRow(rowNum++);
						int colNum = 0;
						createCell(row, colNum++, String.valueOf(count++), columnWidths);
						createCell(row, colNum++,
								userEntry.getFirstName() + (StringUtils.isEmpty(userEntry.getLastName())
								? "" : " " + userEntry.getLastName()), columnWidths);
						createCell(row, colNum++, userEntry.getUserName(), columnWidths);
						createCell(row, colNum++, DateUtils.getFormattedDate(dayCollectionEntry.getValue().getDayStartTimestamp()),
								columnWidths);
						createCell(row, colNum++, dayCollectionEntry.getValue().getTransactionAmount(), columnWidths);
						createCell(row, colNum++, dayCollectionEntry.getValue().getDiscount(),
								columnWidths);
						createCell(row, colNum++, dayCollectionEntry.getValue().getCreditGiven(),
								columnWidths);
					}
					if (dayLevelCollection.containsKey(unknownUser)) {
						final UserDayCollection userDayCollection = dayLevelCollection.get(unknownUser);
							final Row row = sheet.createRow(rowNum++);
							int colNum = 0;
							createCell(row, colNum++, String.valueOf(count++), columnWidths);
							createCell(row, colNum++, "NA", columnWidths);
							createCell(row, colNum++, "Unknown", columnWidths);

							createCell(row, colNum++, DateUtils.getFormattedDate(userDayCollection.getDayStartTimestamp()),
									columnWidths);
							createCell(row, colNum++, userDayCollection.getTransactionAmount(), columnWidths);
							createCell(row, colNum++, userDayCollection.getDiscount(), columnWidths);
							createCell(row, colNum++, userDayCollection.getCreditGiven(), columnWidths);
						}
					}
					
				}
			for (int i = 0; i < USER_DAY_LEVEL_COLLECTION.length; i++) {
				sheet.autoSizeColumn(i);
			}
			return getReportOutput(workbook, reportName);

		} catch (final Exception e) {
			logger.error("Error while generating inventroy user day level collection report", e);
		}
		return null;
	}

	private UserDayCollection getUserDayCollectionDetails(UserDayCollection userDayCollection, 
			TransactionSummary transactionSummary) {
		double transactionAmount = transactionSummary.getAdditionalCost();
		double discount = transactionSummary.getAdditionalDiscount();
		for(PurchasedProductSummary purchasedProductSummary : transactionSummary.getPurchasedProducts()) {
			transactionAmount += purchasedProductSummary.getTotalPrice();
			transactionAmount += purchasedProductSummary.getTotalTax();
			discount += purchasedProductSummary.getTotalDiscount();
		}
		userDayCollection.setTransactionAmount(transactionAmount);
		userDayCollection.setDiscount(discount);
		userDayCollection.setCreditGiven(userDayCollection.getCreditGiven() + transactionSummary.getWalletCreditAmount());
		
		return userDayCollection;
	}

	private class UserDayCollection {
		private int dayStartTimestamp;
		private Double transactionAmount;
		private Double discount;
		private Double creditGiven;

		public UserDayCollection(int dayStartTimestamp, Double transactionAmount, Double discount, Double creditGiven) {
			this.dayStartTimestamp = dayStartTimestamp;
			this.transactionAmount = transactionAmount;
			this.discount = discount;
			this.creditGiven = creditGiven;
		}

		public int getDayStartTimestamp() {
			return dayStartTimestamp;
		}

		public void setDayStartTimestamp(int dayStartTimestamp) {
			this.dayStartTimestamp = dayStartTimestamp;
		}

		/**
		 * @return the transactionAmount
		 */
		public Double getTransactionAmount() {
			return transactionAmount;
		}

		/**
		 * @param transactionAmount the transactionAmount to set
		 */
		public void setTransactionAmount(Double transactionAmount) {
			this.transactionAmount = transactionAmount;
		}

		/**
		 * @return the discount
		 */
		public Double getDiscount() {
			return discount;
		}

		/**
		 * @param discount the discount to set
		 */
		public void setDiscount(Double discount) {
			this.discount = discount;
		}

		/**
		 * @return the creditGiven
		 */
		public Double getCreditGiven() {
			return creditGiven;
		}

		/**
		 * @param creditGiven the creditGiven to set
		 */
		public void setCreditGiven(Double creditGiven) {
			this.creditGiven = creditGiven;
		}

	}

	private void writeInFile(Workbook workbook, String fileName) {
		// Write the output to a file
		FileOutputStream fileOut;
		try {
			final String tempFileDir = System.getProperty("user.home") + System.getProperty("file.separator")
					+ "InventoryReports" + System.getProperty("file.separator");
			final File tempDir = new File(tempFileDir);
			if (!(tempDir.exists() && tempDir.isDirectory())) {
				tempDir.mkdirs();
			}
			fileOut = new FileOutputStream(tempFileDir + fileName);
			workbook.write(fileOut);
			fileOut.close();
			// Closing the workbook
			workbook.close();
		} catch (final FileNotFoundException e) {
			e.printStackTrace();
		} catch (final IOException e) {
			e.printStackTrace();
		}
	}

	private Map<UUID, ProductReportData> getSkuVsProductReportRowMap(List<TransactionRow> transactionRowList) {
		final Map<UUID, ProductReportData> skuVsProductReportRowMap = new HashMap<UUID, ProductReportData>();

		if (transactionRowList != null && !transactionRowList.isEmpty()) {
			for (final TransactionRow transactionRow : transactionRowList) {
				if (skuVsProductReportRowMap.containsKey(transactionRow.getSkuId())) {
					final ProductReportData existingProductReportData = skuVsProductReportRowMap
							.get(transactionRow.getSkuId());
					existingProductReportData.setClosingStock(transactionRow.getFinalQuantity());
					if (InventoryTransactionType.isPurchaseTransaction(transactionRow.getTransactionType())) {
						existingProductReportData.setProductPurchasedQty(
								existingProductReportData.getProductPurchasedQty() + transactionRow.getQuantity());
						existingProductReportData.setPurchaseAmount(
								existingProductReportData.getPurchaseAmount() + transactionRow.getTotalPrice());
					} else {
						existingProductReportData.setProductSaleQty(
								existingProductReportData.getProductSaleQty() + transactionRow.getQuantity());
						existingProductReportData.setSaleAmount(
								existingProductReportData.getSaleAmount() + transactionRow.getTotalPrice());
					}
					evaluateAndSetProfitLoss(existingProductReportData);
					skuVsProductReportRowMap.put(transactionRow.getSkuId(), existingProductReportData);
				} else {
					final ProductReportData productReportData = new ProductReportData();
					productReportData.setProductName(transactionRow.getProductName());
					productReportData.setOpeningStock(transactionRow.getInitialQuantity());
					productReportData.setClosingStock(transactionRow.getFinalQuantity());
					if (InventoryTransactionType.isPurchaseTransaction(transactionRow.getTransactionType())) {
						productReportData.setProductPurchasedQty(transactionRow.getQuantity());
						productReportData.setPurchaseAmount(transactionRow.getTotalPrice());
						productReportData.setProfitOrLoss(0.00);
					} else {
						productReportData.setProductSaleQty(transactionRow.getQuantity());
						productReportData.setSaleAmount(transactionRow.getTotalPrice());
					}
					skuVsProductReportRowMap.put(transactionRow.getSkuId(), productReportData);
				}
			}
		}
		return skuVsProductReportRowMap;
	}

	private void evaluateAndSetProfitLoss(ProductReportData existingProductReportData) {
		if (existingProductReportData.getPurchaseAmount() > 0 && existingProductReportData.getProductPurchasedQty() > 0
				&& existingProductReportData.getSaleAmount() > 0 && existingProductReportData.getProductSaleQty() > 0) {
			final double profit = ((existingProductReportData.getSaleAmount()
					/ existingProductReportData.getProductSaleQty())
					- (existingProductReportData.getPurchaseAmount()
							/ existingProductReportData.getProductPurchasedQty()))
					/ (existingProductReportData.getPurchaseAmount()
							/ existingProductReportData.getProductPurchasedQty());
			existingProductReportData.setProfitOrLoss(profit * 100);
		}
	}

}
