/**
 *
 */
package com.lernen.cloud.core.lib.staff;

import com.embrate.cloud.core.api.attendance.AttendanceDeviceServiceProviderType;
import com.embrate.cloud.core.api.attendance.DeviceUpdateUserData;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceType;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceDaySummary;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceSummary;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.StandardWithStaffDetails;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.staff.*;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.api.user.*;
import com.lernen.cloud.core.api.user.authentication.RegeneratePasswordUserData;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.document.DocumentManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.DocumentUtils;
import com.lernen.cloud.core.utils.ImageUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.PasswordUtils;
import com.lernen.cloud.dao.tier.attendance.staff.StaffAttendanceDao;
import com.lernen.cloud.dao.tier.staff.StaffDao;
import com.lernen.cloud.dao.tier.transport.configuration.TransportConfigurationDao;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.Map.Entry;

import static com.lernen.cloud.core.api.staff.FullStaffDetails.getStaffLite;

/**
 * <AUTHOR>
 *
 */
public class StaffManager {

	private static final Logger logger = LogManager.getLogger(StaffManager.class);

	private static final String S3_FILE_PATH_DELIMITER = "/";
	private static final String STAFF_COMMON_DOCUMENT_DIRECTORY_NAME = "common";
	private static final String THUMBNAIL_SUFFIX = "thumbnail";
	private static final String HYPHEN = "-";
	private static final String STAFF_PARENT_DIRECTORY_NAME = "staff";
	private static final int ONE_KB = 1024;
	private static final int FILE_SIZE_LIMIT = 250 * 100;
	private static final int FILE_COUNT_LIMIT = 15;
	private static final String JPEG = "jpeg";
	private static final String JPG = "jpg";
	private static final String PNG = "png";

	private final StaffDao staffDao;

	private final DocumentManager documentManager;

	private final UserPreferenceSettings userPreferenceSettings;


	private final UserPermissionManager userPermissionManager;
	
	private final UserManager userManager;

	private final TransportConfigurationDao transportConfigurationDao;

	private final StaffAttendanceDao staffAttendanceDao;
	private final InstituteManager instituteManager;

	public StaffManager(StaffDao staffDao, DocumentManager documentManager, UserPreferenceSettings userPreferenceSettings,
						UserPermissionManager userPermissionManager,UserManager userManager,
						TransportConfigurationDao transportConfigurationDao,
						StaffAttendanceDao staffAttendanceDao, InstituteManager instituteManager) {
		this.staffDao = staffDao;
		this.documentManager = documentManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
		this.userManager = userManager;
		this.transportConfigurationDao = transportConfigurationDao;
		this.staffAttendanceDao = staffAttendanceDao;
		this.instituteManager = instituteManager;
	}

	private StaffAttendanceStatus getStaffPresentStatus(UUID staffId, Map<UUID, StaffAttendanceSummary> staffAttendanceSummaryMap) {
		if (!staffAttendanceSummaryMap.containsKey(staffId)) {
			return StaffAttendanceStatus.LEAVE;
		}
		List<StaffAttendanceDaySummary> staffAttendanceDaySummaryList = staffAttendanceSummaryMap.get(staffId).getStaffAttendanceDaySummaryList();
		if (CollectionUtils.isEmpty(staffAttendanceDaySummaryList)) {
			return  StaffAttendanceStatus.LEAVE;
		}
		// Expected tp be fetched for a particular day
		StaffAttendanceDaySummary staffAttendanceDaySummary = staffAttendanceDaySummaryList.get(0);
		if(staffAttendanceDaySummary.getStaffAttendanceDayMetadata() != null && staffAttendanceDaySummary.getStaffAttendanceDayMetadata().getLastAttendanceType() == StaffAttendanceType.IN)
		{
			return StaffAttendanceStatus.PRESENT;
		}
		return staffAttendanceDaySummary.getStaffAttendanceStatus() ;
	}

	public StaffStats getStaffStats(int instituteId, boolean includeAttendance, int date) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}

		final List<Staff> staffs = staffDao.getStaff(instituteId);
		final  List<FullStaffDetails> fullStaffDetailsList = getFullStaffDetails(instituteId, staffs);
		List<StaffCategory> staffCategoryList = getStaffCategoryList(instituteId);

		if(CollectionUtils.isEmpty(fullStaffDetailsList)) {
			return new StaffStats(0, null, 0, null, 0, null,  staffCategoryList, null, null, null, null, 0, null, null);
		}

		Map<UUID, StaffAttendanceSummary> staffAttendanceSummaryMap = new HashMap<>();
		if(includeAttendance){
			List<StaffAttendanceSummary> staffAttendanceSummaryList = staffAttendanceDao.getStaffAttendanceSummary(instituteId, date, null, false);
			for(StaffAttendanceSummary staffAttendanceSummary : staffAttendanceSummaryList){
				staffAttendanceSummaryMap.put(staffAttendanceSummary.getStaffId(), staffAttendanceSummary);
			}
		}
		int totalOnboardStaff = 0;
		List<StaffLite> totalOnboardStaffDetailsList = new ArrayList<>();
		int totalPendingStaff = 0;
		List<StaffLite> pendingStaffDetailsList = new ArrayList<>();
		int totalTransportStaff = 0;
		List<StaffLite> transportStaffDetailsList = new ArrayList<>();
		int todayTotalPresentStaff = 0;

		final Map<UUID, Integer> staffTypeOnboardStaffCount = new TreeMap<>();
		Map<Gender, Integer> staffGenderWiseCount = new HashMap<>();
		Map<Gender, Integer> todayGenderWisePresentStaff = new HashMap<>();

		Map<StaffAttendanceStatus, Map<Gender, List<StaffBasicDetailsWithCategoryDepartDesignation>>> staffAttendanceGenderDetailsMap = new HashMap<>();
		staffAttendanceGenderDetailsMap.put(StaffAttendanceStatus.PRESENT, new HashMap<>());
		staffAttendanceGenderDetailsMap.put(StaffAttendanceStatus.HALF_DAY, new HashMap<>());
		staffAttendanceGenderDetailsMap.put(StaffAttendanceStatus.LEAVE,new HashMap<>());

		Map<UUID, List<StaffLite>> categoryWiseStaffDetailsMap = new HashMap<>();
		Map<Gender, List<StaffLite>> genderWiseStaffDetailsMap = new HashMap<>();

		for (final FullStaffDetails fullStaffDetails : fullStaffDetailsList) {
			StaffLite staffLite = getStaffLite(fullStaffDetails);
			UUID staffId = fullStaffDetails.getStaffId();
			UUID staffCategoryId = fullStaffDetails.getStaffBasicDetailsWithCategoryDepartDesignation().getStaffCategory().getStaffCategoryId();
			StaffAttendanceStatus status = getStaffPresentStatus(staffId, staffAttendanceSummaryMap);
			if (fullStaffDetails.getStaffStatus() == StaffStatus.ONBOARD) {
				if(status == StaffAttendanceStatus.PRESENT){
					todayTotalPresentStaff++;
				}
				totalOnboardStaff++;
				totalOnboardStaffDetailsList.add(staffLite);
				if (fullStaffDetails.isTransportStaff()) {
					totalTransportStaff++;
					transportStaffDetailsList.add(staffLite);
				}
				if (!staffTypeOnboardStaffCount.containsKey(staffCategoryId)) {
					staffTypeOnboardStaffCount.put(staffCategoryId, 0);
				}
				staffTypeOnboardStaffCount.put(staffCategoryId,
						staffTypeOnboardStaffCount.get(staffCategoryId) + 1);
				if (!categoryWiseStaffDetailsMap.containsKey(staffCategoryId)) {
					categoryWiseStaffDetailsMap.put(staffCategoryId, new ArrayList<>());
				}
				categoryWiseStaffDetailsMap.get(staffCategoryId).add(staffLite);

				Gender gender = fullStaffDetails.getStaffBasicDetailsWithCategoryDepartDesignation().getGender(); 
				if(gender == null) {
					continue;
				}
				if(!staffGenderWiseCount.containsKey(gender)) {
				staffGenderWiseCount.put(gender, 0);
				}
				staffGenderWiseCount.put(gender, staffGenderWiseCount.get(gender) + 1);

				if (!genderWiseStaffDetailsMap.containsKey(gender)) {
					genderWiseStaffDetailsMap.put(gender, new ArrayList<>());
				}
				genderWiseStaffDetailsMap.get(gender).add(staffLite);

				if(!todayGenderWisePresentStaff.containsKey(gender)){
					todayGenderWisePresentStaff.put(gender, 0);
					staffAttendanceGenderDetailsMap.get(StaffAttendanceStatus.PRESENT).put(gender, new ArrayList<>());
					staffAttendanceGenderDetailsMap.get(StaffAttendanceStatus.HALF_DAY).put(gender, new ArrayList<>());
					staffAttendanceGenderDetailsMap.get(StaffAttendanceStatus.LEAVE).put(gender, new ArrayList<>());
				}
				
				if(status == StaffAttendanceStatus.PRESENT){
					todayGenderWisePresentStaff.put(gender, todayGenderWisePresentStaff.get(gender) + 1);
				}
				staffAttendanceGenderDetailsMap.get(status).get(gender).add(fullStaffDetails.getStaffBasicDetailsWithCategoryDepartDesignation());
				
			} else if (fullStaffDetails.getStaffStatus() == StaffStatus.JOINER) {
				totalPendingStaff++;
				pendingStaffDetailsList.add(staffLite);
			}
		}

		return new StaffStats(totalOnboardStaff, totalOnboardStaffDetailsList, totalPendingStaff, pendingStaffDetailsList, totalTransportStaff, transportStaffDetailsList, staffCategoryList, categoryWiseStaffDetailsMap, staffTypeOnboardStaffCount, staffGenderWiseCount, genderWiseStaffDetailsMap, todayTotalPresentStaff, todayGenderWisePresentStaff, staffAttendanceGenderDetailsMap);
	}

	public UUID addStaff(RegisterStaffPayload registerStaffPaylaod, int instituteId, String imageName,
						 FileData staffImage, boolean fromScript) {
		return addStaff(registerStaffPaylaod, instituteId, imageName, staffImage, fromScript, null, false);
	}
	public UUID addStaff(RegisterStaffPayload registerStaffPaylaod, int instituteId, String imageName,
						 FileData staffImage, boolean fromScript, UUID userId, boolean checkAccess) {

		final Staff existingStaffWithName = staffDao.getStaff(instituteId, registerStaffPaylaod.getStaffBasicInfo().getStaffInstituteId());
		if (existingStaffWithName != null) {
			logger.error("Another staff already have this id {}. " +
					"Please provide a different one for institute {}.", registerStaffPaylaod.getStaffId(), instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Another staff already have this id. " +
							"Please provide a different one."));
		}

		if(checkAccess) {
			if (registerStaffPaylaod.getStaffBasicInfo() != null) {
				String primaryEmail = registerStaffPaylaod.getStaffBasicInfo().getPrimaryEmail();
				if (!StringUtils.isBlank(primaryEmail)) {
					userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ADD_PRIMARY_STAFF_EMAIL);
				}
			}
		}

		validateAddStaff(registerStaffPaylaod, false, fromScript);
		final MetaDataPreferences metaDataPreferences = userPreferenceSettings
				.getMetaDataPreferences(instituteId);
		final UUID staffId = staffDao.addStaff(registerStaffPaylaod, metaDataPreferences.isStaffCounter());
		if (staffId == null) {
			logger.error("Unable to add staff {}", registerStaffPaylaod);
			return null;
		}

		if (staffImage != null) {
			uploadStaffImage(instituteId, staffId, imageName, staffImage);
		}



		return staffId;
	}

	private void uploadStaffImage(int instituteId, UUID staffId, String documentName, FileData uploadPhoto) {
		try {
			final List<Document<StaffDocumentType>> staffDocumentUploaded = uploadDocument(instituteId, staffId,
					StaffDocumentType.STAFF_PROFILE_IMAGE, documentName, uploadPhoto);
			if (CollectionUtils.isEmpty(staffDocumentUploaded)) {
				logger.error("Unable to upload photo for staff {}", staffId);
			}

		} catch (final Exception e) {
			logger.error("Error while uploading photo for staff {}", staffId, e);
		}
	}

	private void validateAddStaff(RegisterStaffPayload registerStaffPaylaod, boolean update, boolean fromScript) {
		if (registerStaffPaylaod == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid staff payload."));
		}

		if (registerStaffPaylaod.getInstituteId() <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}

		if (registerStaffPaylaod.getStaffBasicInfo() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid staff basic info."));
		}

		if (StringUtils.isEmpty(registerStaffPaylaod.getStaffBasicInfo().getName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid first name."));
		}

		if (StringUtils.isEmpty(registerStaffPaylaod.getStaffBasicInfo().getStaffInstituteId())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid staff institue id."));
		}

		if(!fromScript) {
			if (StringUtils.isEmpty(registerStaffPaylaod.getStaffBasicInfo().getPrimaryEmail())) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid primary email Id."));
			}

			if (StringUtils.isEmpty(registerStaffPaylaod.getStaffBasicInfo().getPrimaryContactNumber())) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid primary contact number."));
			}
		}

		if(!update) {
			if(staffDao.getStaff(registerStaffPaylaod.getInstituteId(),
					registerStaffPaylaod.getStaffBasicInfo().getStaffInstituteId()) != null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Staff exists with this Staff Institute Id."));

			}
		}
	}

	public boolean onboardStaff(UUID staffId, boolean createUser, int instituteId, UUID userId,
								StaffOnboardPayload staffOnboardPayload) {

		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}
		if (staffId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid staff id."));
		}
		boolean result =  staffDao.onboardStaff(staffId, instituteId,
				staffOnboardPayload == null ? null : staffOnboardPayload.getStaffTimingDetails());
		if(!result) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Error whiling on-boarding staff."));
		}

		if(!createUser) {
			return true;
		}

		User user = userManager.getUser(staffId);
		if(user != null) {
			return userManager.updateUserStatus(instituteId, staffId, UserStatus.ENABLED);
		}

		final MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
		boolean staffUserCreated = false;
		if (metaDataPreferences.isCreateStaffUser()) {
			// TODO : Create standard user name
			if (metaDataPreferences.getUserPasswordLength() <= 0) {
				staffUserCreated = false;
				logger.error("Password length not setup for institute {}", instituteId);
			} else {
				final String password = PasswordUtils
						.generateNumericPassword(metaDataPreferences.getUserPasswordLength());
				Staff staff = getStaff(staffId);
				List<Integer> instituteScope = staffOnboardPayload == null ? null :
						CollectionUtils.isEmpty(staffOnboardPayload.getUserPayload().getInstituteScope()) ?
								null : staffOnboardPayload.getUserPayload().getInstituteScope();
				final UserPayload userPayload = new UserPayload(instituteId, staffId, UserType.STAFF,
						staff.getStaffBasicInfo().getPrimaryEmail(), staff.getStaffBasicInfo().getPrimaryContactNumber(),
						null, password,
						staffOnboardPayload == null ? null : staffOnboardPayload.getUserPayload() == null
								? null : staffOnboardPayload.getUserPayload().getAuthorizedModules(),
						staffOnboardPayload == null ? null : staffOnboardPayload.getUserPayload() == null
								? null : staffOnboardPayload.getUserPayload().getRoles(),
						instituteScope, false);
				final UUID staffUserId = userManager.createUserWithRandomUserName(userPayload, false,
						metaDataPreferences.getUserNameLength(), metaDataPreferences.getInstituteUniqueCode(), userId);
				if (staffUserId == null) {
					staffUserCreated = false;
					logger.error("Failed to create user for staff {}, instituteId {}", staffId, instituteId);
				} else {
					staffUserCreated = true;
					logger.info("Created user for staff {}, instituteId {}", staffId, instituteId);
				}
			}

		}

		return staffUserCreated;
	}

	public RegeneratePasswordUserData<Void> getResetPasswordUserDetails(UUID staffId) {
		User user = userManager.getUser(staffId);
		if(user == null) {
			logger.info("Cannot find user with id {}", staffId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Couldnot find user."));
		}
		return userManager.resetUserPassword(user.getUserType(), user.getUserName());
	}

	public boolean relieveStaff(UUID staffId, int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}
		if (staffId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid staff id."));
		}
		boolean relieved = staffDao.relieveStaff(staffId, instituteId);
		if(relieved) {
			User user = userManager.getUser(staffId);
			if(user != null) {
				return userManager.updateUserStatus(instituteId, staffId, UserStatus.DISABLED);
			}
			return true;
		}
		return false;
	}

	public List<Staff> getStaff(int instituteId, StaffStatus staffStatus, String includeUserStatus) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}

		if (staffStatus == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid Staff Status."));
		}

		return staffDao.getStaff(instituteId, staffStatus, includeUserStatus, null, false);
	}

	public List<Staff> getOrganisationLevelStaff(int instituteId, StaffStatus staffStatus, UserStatus userStatus){
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}

		if (staffStatus == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid Staff Status."));
		}
		
		return staffDao.getStaff(instituteId, staffStatus, null, userStatus, true);
	}

	public List<Staff> getOnboardedTransportStaff(int instituteId) {
		return staffDao.getOnboardedTransportStaff(instituteId);
	}

	public List<FullStaffDetails> getAdvanceStaffDetails(int instituteId, StaffStatus staffStatus, String includeUserStatus) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}

		if (staffStatus == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid Staff Status."));
		}

		return getFullStaffDetails(instituteId, staffDao.getStaff(instituteId, staffStatus, includeUserStatus, null, false));
	}

	public List<FullStaffDetails> getAdvanceStaffDetails(int instituteId, List<StaffStatus> staffStatus) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}
		return getFullStaffDetails(instituteId, staffDao.getStaff(instituteId,staffStatus));
	}

	public Staff getStaff(int instituteId, String staffInstituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}
		if (StringUtils.isBlank(staffInstituteId)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid staff institute id."));
		}
		return staffDao.getStaff(instituteId, staffInstituteId);
	}

	public List<Staff> getStaffByDeviceUserId(String deviceUserId, Set<Integer> institutes) {
		return staffDao.getStaffByDeviceUserId(deviceUserId, institutes);
	}
	public List<Staff> getStaff(int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}

		return staffDao.getStaff(instituteId);
	}

	public List<Staff> getStaffDetailsByCategories(int instituteId,Set<UUID> staffCategories) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}

		return staffDao.getStaffDetailsByCategories(instituteId,staffCategories);
	}

	public List<Document<StaffDocumentType>> uploadDocument(int instituteId, UUID staffId,
															StaffDocumentType staffDocumentType, String documentName, FileData fileData) {
		final Staff staff = staffDao.getStaff(instituteId, staffId);
		if (staff == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Staff does not exists"));
		}
		return uploadDocument(instituteId, staff, staffDocumentType, documentName, fileData);
	}

	public List<Document<StaffDocumentType>> uploadDocument(int instituteId, Staff staff,
															StaffDocumentType staffDocumentType, String documentName, FileData document) {
		final UUID staffId = staff.getStaffId();

		List<Document<StaffDocumentType>> staffDocuments = staff.getStaffDocuments();
		if (CollectionUtils.isEmpty(staffDocuments)) {
			staffDocuments = new ArrayList<>();
		}

		if (staffDocuments.size() == DocumentUtils.FILE_COUNT_LIMIT) {
			logger.error("Staff already have " + DocumentUtils.FILE_COUNT_LIMIT
					+ " documents uploaded. Please delete some to upload more.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COUNT_OF_DOCUMENT,
					"Staff already have " + DocumentUtils.FILE_COUNT_LIMIT
							+ " documents uploaded. Please delete some to upload more."));
		}

		final double length = document.getContent().length / DocumentUtils.ONE_KB;
		if (length > DocumentUtils.FILE_SIZE_LIMIT) {
			logger.error("Size Greater than " + DocumentUtils.FILE_SIZE_LIMIT + " kb");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SIZE_OF_DOCUMENT,
					"Size Of document cannot be greater than " + DocumentUtils.FILE_SIZE_LIMIT + " kb"));
		}

		if (!validDocument(staff, staffDocumentType, documentName, document)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT,
					"Document already exists with give type and name"));
		}

		/**
		 * upload document
		 */
		Document<StaffDocumentType> uploadedDocument = uploadDocument(instituteId, document, staffDocumentType, documentName, staffId, false);
		if(uploadedDocument != null) {
			staffDocuments.add(uploadedDocument);
		}

		if(staffDocumentType != StaffDocumentType.STAFF_PROFILE_IMAGE) {
			return staffDao.updateDocuments(instituteId, staffId, staffDocuments) ? staffDocuments : null;
		}

		/**
		 * upload staff profile image thumbnail
		 */
		FileData thumbnailDocument = ImageUtils.createThumbnailInBytes(document);
		Document<StaffDocumentType> uploadedThumbnail = uploadDocument(instituteId, thumbnailDocument, StaffDocumentType.STAFF_PROFILE_IMAGE_THUMBNAIL,
				documentName + HYPHEN + THUMBNAIL_SUFFIX, staffId, true);
		if(uploadedThumbnail != null) {
			staffDocuments.add(uploadedThumbnail);
		}
		return staffDao.updateDocuments(instituteId, staffId, staffDocuments) ? staffDocuments : null;

	}

	private Document<StaffDocumentType> uploadDocument(int instituteId, FileData fileData,
													   StaffDocumentType staffDocumentType, String documentName, UUID staffId,
													   boolean isThumbnail) {
		final UUID documentId = UUID.randomUUID();
		String fileExtension = FilenameUtils.getExtension(fileData.getFileName());
		Document<StaffDocumentType> newDocument = new Document<>(staffDocumentType, documentName, documentId,
				fileExtension, (int) (System.currentTimeMillis() / 1000l));
		String s3Path = buildDocumentPath(staffId, newDocument, isThumbnail);
		boolean documentUploaded = documentManager.uploadDocument(s3Path, instituteId, fileData);

		if (!documentUploaded) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT,
					"Unable to upload document. Please try again"));
		}
		return newDocument;
	}

	private boolean validDocument(Staff staff, StaffDocumentType staffDocumentType, String documentName,
								  FileData document) {
		if (staff == null || staffDocumentType == null || document == null) {
			return false;
		}
		if (staffDocumentType == StaffDocumentType.OTHER && StringUtils.isBlank(documentName)) {
			return false;
		}
		if (CollectionUtils.isEmpty(staff.getStaffDocuments())) {
			return true;
		}
		final String fileExtension = FilenameUtils.getExtension(document.getFileName());
		if (staffDocumentType == StaffDocumentType.STAFF_PROFILE_IMAGE && !validImageExtension(fileExtension)) {
			logger.error("Image extension {} not in valid format ", fileExtension);
			return false;
		}
		for (final Document<StaffDocumentType> staffDocument : staff.getStaffDocuments()) {
			if (staffDocument.getDocumentType() == staffDocumentType && staffDocumentType != StaffDocumentType.OTHER) {
				return false;
			} else if (staffDocument.getDocumentType() == staffDocumentType
					&& staffDocumentType == StaffDocumentType.OTHER
					&& documentName.equalsIgnoreCase(staffDocument.getDocumentName())) {
				return false;
			}
		}
		return true;
	}

	private boolean validImageExtension(String fileExtension) {
		return fileExtension.equalsIgnoreCase(PNG) || fileExtension.equalsIgnoreCase(JPEG)
				|| fileExtension.equalsIgnoreCase(JPG);
	}

	public String buildDocumentPath(UUID staffId, Document<StaffDocumentType> staffDocument, boolean isThumbnail) {
		String thumbnailPath = "";
		if(isThumbnail) {
			thumbnailPath = HYPHEN + THUMBNAIL_SUFFIX;
		}
		final StringBuilder s3Path = new StringBuilder();
		s3Path.append(STAFF_PARENT_DIRECTORY_NAME).append(S3_FILE_PATH_DELIMITER).append(staffId)
				.append(S3_FILE_PATH_DELIMITER).append(STAFF_COMMON_DOCUMENT_DIRECTORY_NAME)
				.append(S3_FILE_PATH_DELIMITER).append(staffDocument.getDocumentId()).append(thumbnailPath)
				.append(".").append(staffDocument.getFileExtension());
		return s3Path.toString();
	}

	public DownloadDocumentWrapper<Document<StaffDocumentType>> downloadDocument(int instituteId, UUID staffId,
																				 UUID documentId) {
		if (documentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Invalid document Information"));
		}
		final Staff staff = staffDao.getStaff(instituteId, staffId);
		if (staff == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Staff does not exists"));
		}

		final List<Document<StaffDocumentType>> staffDocuments = staff.getStaffDocuments();
		if (CollectionUtils.isEmpty(staffDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		for (final Document<StaffDocumentType> staffDocument : staffDocuments) {
			if (documentId.equals(staffDocument.getDocumentId())) {
				final String s3Path = buildDocumentPath(staffId, staffDocument, staffDocument.getDocumentType().isThumbnail());
				return new DownloadDocumentWrapper<>(staffDocument,
						documentManager.downloadDocument(instituteId, s3Path));
			}
		}
		return null;
	}
	public DownloadDocumentWrapper<Document<StaffDocumentType>> downloadStaffSignature(int instituteId, UUID staffId){
		FullStaffDetails fullStaffDetails = getFullStaffDetails(instituteId, staffId);
		return downloadStaffDocument(instituteId, fullStaffDetails, StaffDocumentType.STAFF_SIGNATURE);
	}

	public DownloadDocumentWrapper<Document<StaffDocumentType>> downloadStaffDocument(int instituteId, FullStaffDetails staff,
																					  StaffDocumentType staffDocumentType) {
		final List<Document<StaffDocumentType>> staffDocuments = staff.getStaffDocuments();
		if (CollectionUtils.isEmpty(staffDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		for (final Document<StaffDocumentType> staffDocument : staffDocuments) {
			if (staffDocument.getDocumentType() == staffDocumentType) {
				final String s3Path = buildDocumentPath(staff.getStaffId(), staffDocument, staffDocument.getDocumentType().isThumbnail());
				return new DownloadDocumentWrapper<>(staffDocument,
						documentManager.downloadDocument(instituteId, s3Path));
			}
		}
		return null;
	}

	public DownloadDocumentWrapper<Document<StaffDocumentType>> downloadStaffImage(int instituteId, FullStaffDetails staff) {
		final List<Document<StaffDocumentType>> staffDocuments = staff.getStaffDocuments();
		if (CollectionUtils.isEmpty(staffDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		for (final Document<StaffDocumentType> staffDocument : staffDocuments) {
			if (staffDocument.getDocumentType() == StaffDocumentType.STAFF_PROFILE_IMAGE) {
				final String s3Path = buildDocumentPath(staff.getStaffId(), staffDocument, staffDocument.getDocumentType().isThumbnail());
				return new DownloadDocumentWrapper<>(staffDocument,
						documentManager.downloadDocument(instituteId, s3Path));
			}
		}
		return null;
	}

	public List<Document<StaffDocumentType>> deleteDocument(int instituteId, UUID staffId, UUID documentId) {
		final Staff staff = staffDao.getStaff(instituteId, staffId);
		return deleteDocument(instituteId, staff, documentId);
	}

	public Staff getStaff(int instituteId, UUID staffId) {
		return staffDao.getStaff(instituteId, staffId);
	}

	public FullStaffDetails getFullStaffDetails(int instituteId, UUID staffId) {
		return getFullStaffDetails(instituteId, staffDao.getStaff(instituteId, staffId));
	}

	public Staff getStaff(UUID staffId) {
		return staffDao.getStaff(staffId);
	}

	private List<Document<StaffDocumentType>> deleteDocument(int instituteId, Staff staff, UUID documentId) {
		if (staff == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Staff does not exists"));
		}

		if (documentId == null || instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Invalid document Information"));
		}
		final UUID staffId = staff.getStaffId();
		final List<Document<StaffDocumentType>> staffDocuments = staff.getStaffDocuments();
		if (CollectionUtils.isEmpty(staffDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		final Iterator<Document<StaffDocumentType>> iterator = staffDocuments.iterator();
		boolean deleted = false;
		boolean isStaffProfileImage = false;
		while (iterator.hasNext()) {
			final Document<StaffDocumentType> staffDocument = iterator.next();
			if (documentId.equals(staffDocument.getDocumentId())) {
				final String s3Path = buildDocumentPath(staffId, staffDocument, staffDocument.getDocumentType().isThumbnail());
				if (documentManager.deleteDocument(instituteId, s3Path)) {
					iterator.remove();
					deleted = true;
					if(staffDocument.getDocumentType() == StaffDocumentType.STAFF_PROFILE_IMAGE) {
						isStaffProfileImage = true;
					}
				}
				break;
			}
		}
		if (!deleted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}

		if(isStaffProfileImage) {
			while (iterator.hasNext()) {
				final Document<StaffDocumentType> staffDocument = iterator.next();
				if (staffDocument.getDocumentType() == StaffDocumentType.STAFF_PROFILE_IMAGE_THUMBNAIL) {
					final String s3Path = buildDocumentPath(staffId, staffDocument, staffDocument.getDocumentType().isThumbnail());
					if (documentManager.deleteDocument(instituteId, s3Path)) {
						iterator.remove();
					}
					break;
				}
			}
		}

		return staffDao.updateDocuments(instituteId, staffId, staffDocuments) ? staffDocuments : null;
	}

	public List<Staff> searchStaff(int instituteId, String searchText, StaffStatus staffStatus,
								   String location) {
		if (instituteId <= 0) {
			return null;
		}
		return staffDao.searchStaff(instituteId, searchText, staffStatus, location);
	}

	public List<FullStaffDetails> advanceSearchStaff(int instituteId, String searchText, StaffStatus staffStatus, String location,
			String categories, String departments, String designations) {
		if (instituteId <= 0) {
			return null;
		}

		final List<UUID> staffCategoryList = new ArrayList<>();
		if (StringUtils.isNotBlank(categories)) {
			final String[] staffCategoryTokens = categories.split(",");
			for (final String staffCategoryToken : staffCategoryTokens) {
				if (StringUtils.isBlank(staffCategoryToken)) {
					continue;
				}
				staffCategoryList.add(UUID.fromString(staffCategoryToken));
			}
		}

		final Set<UUID> staffDepartmentList = new HashSet<>();
		if (StringUtils.isNotBlank(departments)) {
			final String[] staffDepartmentTokens = departments.split(",");
			for (final String staffDepartmentToken : staffDepartmentTokens) {
				if (StringUtils.isBlank(staffDepartmentToken)) {
					continue;
				}
				staffDepartmentList.add(UUID.fromString(staffDepartmentToken));
			}
		}

		final Set<UUID> staffDesignationList = new HashSet<>();
		if (StringUtils.isNotBlank(designations)) {
			final String[] staffDesignationTokens = designations.split(",");
			for (final String staffDesignationToken : staffDesignationTokens) {
				if (StringUtils.isBlank(staffDesignationToken)) {
					continue;
				}
				staffDesignationList.add(UUID.fromString(staffDesignationToken));
			}
		}

		List<Staff> staffList = staffDao.advanceSearchStaff(instituteId, searchText, staffStatus, location, staffCategoryList);
		if(CollectionUtils.isEmpty(staffList)) {
			return null;
		}
		if(CollectionUtils.isEmpty(staffDepartmentList) && CollectionUtils.isEmpty(staffDesignationList)) {
			return getFullStaffDetails(instituteId, staffList);
		}

		List<Staff> finalStaffList = new ArrayList<Staff>();
		for(Staff staff : staffList) {
			if(filterApplyToStaff(staff, staffDepartmentList, staffDesignationList)) {
				finalStaffList.add(staff);
			}
		}

		if(CollectionUtils.isEmpty(finalStaffList)) {
			return null;
		}

		return getFullStaffDetails(instituteId, finalStaffList);
	}

	public List<FullStaffDetails> advanceSearchStaff(int instituteId,
													 StaffFilterationCriteria staffFilterationCriteria) {

		if (instituteId <= 0) {
			return null;
		}

		List<Staff> staffList = staffDao.searchStaffDetailsWithFilter(instituteId,staffFilterationCriteria);
		if(CollectionUtils.isEmpty(staffList)) {
			return null;
		}
		if(CollectionUtils.isEmpty(staffFilterationCriteria.getStaffDepartment()) && CollectionUtils.isEmpty(staffFilterationCriteria.getStaffDesignation())) {
			return getFullStaffDetails(instituteId, staffList);
		}

		List<Staff> finalStaffList = new ArrayList<Staff>();
		for(Staff staff : staffList) {
			if(filterApplyToStaff(staff, staffFilterationCriteria.getStaffDepartment(), staffFilterationCriteria.getStaffDesignation())) {
				finalStaffList.add(staff);
			}
		}

		if(CollectionUtils.isEmpty(finalStaffList)) {
			return null;
		}

		return getFullStaffDetails(instituteId, finalStaffList);
	}

	private boolean filterApplyToStaff(Staff staff, Set<UUID> staffDepartmentList, Set<UUID> staffDesignationList) {

		Map<UUID, UUID> departmentDesignationMap = staff.getStaffBasicInfo().getDepartmentDesignationMapping();
		if(!MapUtils.isEmpty(departmentDesignationMap)) {

			boolean isDepartmentThere = CollectionUtils.isEmpty(staffDepartmentList)
					? true : !Collections.disjoint(staffDepartmentList,
					departmentDesignationMap.keySet());

			boolean isDesignationThere = CollectionUtils.isEmpty(staffDesignationList)
					? true : !Collections.disjoint(staffDesignationList,
					new HashSet<UUID>(departmentDesignationMap.values()));

			return isDepartmentThere ? isDesignationThere : false;
		}
		return false;
	}

	public UUID updateStaff(RegisterStaffPayload updateStaffPaylaod, int instituteId, String documentName,
							FileData uploadPhoto, boolean fromScript, UUID userId) {

		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}

		if (updateStaffPaylaod.getStaffId() == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid staff id."));
		}

		final Staff existingStaff = staffDao.getStaff(instituteId, updateStaffPaylaod.getStaffId());

		if (existingStaff == null) {
			logger.error("Staff does not exist with id {} , institute {}", updateStaffPaylaod.getStaffId(),
					instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Staff does not exists"));
		}

		final Staff existingStaffWithName = staffDao.getStaff(instituteId, updateStaffPaylaod.getStaffBasicInfo().getStaffInstituteId());
		if (existingStaffWithName != null) {
			if(!existingStaffWithName.getStaffId().equals(updateStaffPaylaod.getStaffId())) {
				logger.error("Another staff already have this id {}. Please provide a different one for institute {}.", updateStaffPaylaod.getStaffId(),
						instituteId);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Another staff already have this id. Please provide a different one."));
			}
		}
		// Check for the case when transport staff is being removed.
		// Staff should not belong to any transport
		if(existingStaff.isTransportStaff() && !updateStaffPaylaod.isTransportStaff()){
			Integer assignedRoutes = transportConfigurationDao.anyTransportRouteAssigned(instituteId, existingStaff.getStaffId());
			if(assignedRoutes == null ){
				logger.error("Unable to get assigned route status for institute {}, staff {}", instituteId, existingStaff.getStaffId());
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Unable to get assigned route status. Please try again."));
			}

			if(assignedRoutes > 0){
				logger.info("{} assigned route for institute {}, staff {}", assignedRoutes, instituteId, existingStaff.getStaffId());
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Staff has " + assignedRoutes + " transport routes assigned. So cannot remove this user from transport staff. Please remove the routes assigned to this user first."));
			}
		}

		if (updateStaffPaylaod.getStaffBasicInfo() != null) {
			String primaryEmail = updateStaffPaylaod.getStaffBasicInfo().getPrimaryEmail();
			String currentPrimaryEmail = existingStaff.getStaffBasicInfo().getPrimaryEmail();
			if (!primaryEmail.equalsIgnoreCase(currentPrimaryEmail)) {
				userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_PRIMARY_STAFF_EMAIL);
			}
		}

		validateAddStaff(updateStaffPaylaod, true, fromScript);

		final UUID staffId = staffDao.updateStaff(updateStaffPaylaod);
		if (staffId == null) {
			logger.error("Unable to add staff {}", updateStaffPaylaod);
			return null;
		}

		if(!userManager.updateUserInfo(updateStaffPaylaod.getStaffBasicInfo().getPrimaryEmail(),
				updateStaffPaylaod.getStaffBasicInfo().getPrimaryContactNumber(),
				updateStaffPaylaod.getStaffBasicInfo().getStaffInstituteId(),
				updateStaffPaylaod.getStaffBasicInfo().getName(), updateStaffPaylaod.getStaffId())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Error while updating user details of this staff."));
		}
		if (uploadPhoto == null) {
			return staffId;
		}
		// upload photo of staff
		uploadStaffProfileImage(instituteId, documentName, uploadPhoto, staffId);
		return staffId;
	}

	public void uploadStaffProfileImage(int instituteId, String documentName, FileData uploadPhoto, UUID staffId) {
		try {
			final Staff staff = staffDao.getStaff(instituteId, staffId);
			final Document<StaffDocumentType> staffExistingImage = staff.getStaffImage();
			boolean deletedExistingImage = false;
			if (staffExistingImage != null) {
				logger.info("Deleting existing image {}, for staff {}", staffExistingImage.getDocumentId(), staffId);
				deleteDocument(instituteId, staffId, staffExistingImage.getDocumentId());
				deletedExistingImage = true;
			}
			if (deletedExistingImage) {
				uploadDocument(instituteId, staff.getStaffId(), StaffDocumentType.STAFF_PROFILE_IMAGE, documentName,
						uploadPhoto);
			} else {
				uploadDocument(instituteId, staff, StaffDocumentType.STAFF_PROFILE_IMAGE, documentName, uploadPhoto);
			}

		} catch (final Exception e) {
			logger.error("Unable to upload staff image for {}", staffId, e);
		}
	}

	public boolean updateBulkStaffDetails(int instituteId,int academicSessionId,BulkStaffDetailsPayload updateBulkStaffDetailsPayload,UUID userId){
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}

		if(academicSessionId <= 0){
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid academic session id."));
		}

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid user id."));
		}

		validateBulkStaffDetailsPayload(updateBulkStaffDetailsPayload);
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.BULK_UPDATE_STAFF);
		return staffDao.updateBulkStaffDetails(updateBulkStaffDetailsPayload);
	}

	public void validateBulkStaffDetailsPayload(BulkStaffDetailsPayload bulkStaffDetailsPayload){

		if(bulkStaffDetailsPayload == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid bulk staff details payload."));
		}

		if(bulkStaffDetailsPayload.getInstituteId()<=0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid Institute Id."));
		}

		if(CollectionUtils.isEmpty(bulkStaffDetailsPayload.getStaffDetailsParametersList())){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid Staff Details Parameters List."));
		}

		if(CollectionUtils.isEmpty(bulkStaffDetailsPayload.getStaffTimingDetails()) && bulkStaffDetailsPayload.getStaffVisitingDaysPayload() == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid bulk staff Details."));
		}
	}

	public boolean deleteStaff(UUID staffId, int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid institute."));
		}
		if (staffId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF, "Invalid staff id."));
		}
		return staffDao.deleteStaff(staffId, instituteId);
	}

	public Double getWalletAmount(UUID staffId, DBLockMode dbLockMode) {
		return staffDao.getWalletAmount(staffId, dbLockMode);
	}

	/*
	 * Staff Module Enhancement staff_category, staff_departments, staff_designations
	 */

	//staff_category
	public UUID addStaffCategory(StaffCategory staffCategory, UUID userId) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		validateStaffCategoryPayload(staffCategory, false);

		StaffCategory existingStaffCategory = staffDao.checkStaffCategoryByName(staffCategory.getInstituteId(),
				staffCategory.getStaffCategoryName());
		if(existingStaffCategory != null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Staff Category already exists with given name. "
							+ "Please change the name and try again."));
		}

		return staffDao.addStaffCategory(staffCategory, userId);
	}

	private void validateStaffCategoryPayload(StaffCategory staffCategory, boolean update) {
		if (staffCategory.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if(update) {
			if (staffCategory.getStaffCategoryId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
			}
		}

		if (StringUtils.isBlank(staffCategory.getStaffCategoryName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
	}

	public StaffCategory getStaffCategoryDetailsByCategoryId(int instituteId, UUID staffCategoryId) {

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (staffCategoryId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}


		return staffDao.getStaffCategoryDetailsByCategoryId(instituteId, staffCategoryId);
	}

	public List<StaffCategory> getStaffCategoryList(int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		return staffDao.getStaffCategoryList(instituteId);
	}

	public boolean updateStaffCategory(StaffCategory staffCategory, UUID userId) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		validateStaffCategoryPayload(staffCategory, true);

		StaffCategory existingStaffCategory = staffDao.checkStaffCategoryByName(staffCategory.getInstituteId(),
				staffCategory.getStaffCategoryName());
		if(existingStaffCategory != null) {
			if(existingStaffCategory.getStaffCategoryId() != staffCategory.getStaffCategoryId()) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Staff Category already exists with given name. "
								+ "Please change the name and try again."));
			}
		}
		return staffDao.updateStaffCategory(staffCategory, userId);
	}

	public boolean deleteStaffCategory(int instituteId, UUID userId, UUID staffCategoryId) {

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (staffCategoryId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if(!CollectionUtils.isEmpty(getStaffDetailsByCategory(instituteId,  staffCategoryId))) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Category already contains staffs in it. "
							+ "Please change their category and try again."));
		}
		return staffDao.deleteStaffCategory(instituteId, staffCategoryId);
	}


	//staff_department
	public UUID addStaffDepartment(StaffDepartment staffDepartment, UUID userId) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		validateStaffDepartmentPayload(staffDepartment, false);

		StaffDepartment existingStaffDepartment = staffDao.checkStaffDepartmentByName(staffDepartment.getInstituteId(),
				staffDepartment.getStaffDepartmentName());
		if(existingStaffDepartment != null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Staff Department already exists with given name. "
							+ "Please change the name and try again."));
		}

		return staffDao.addStaffDepartment(staffDepartment, userId);
	}

	private void validateStaffDepartmentPayload(StaffDepartment staffDepartment, boolean update) {
		if (staffDepartment.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if(update) {
			if (staffDepartment.getStaffDepartmentId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
			}
		}

		if (StringUtils.isBlank(staffDepartment.getStaffDepartmentName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
	}

	public StaffDepartment getStaffDepartmentDetailsByDepartmentId(int instituteId, UUID staffDepartmentId) {

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (staffDepartmentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}


		return staffDao.getStaffDepartmentDetailsByDepartmentId(instituteId, staffDepartmentId);
	}

	public List<StaffDepartment> getStaffDepartmentList(int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		return staffDao.getStaffDepartmentList(instituteId);
	}

	public boolean updateStaffDepartment(StaffDepartment staffDepartment, UUID userId) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		validateStaffDepartmentPayload(staffDepartment, true);
		StaffDepartment existingStaffDepartment = staffDao.checkStaffDepartmentByName(staffDepartment.getInstituteId(),
				staffDepartment.getStaffDepartmentName());
		if(existingStaffDepartment != null) {
			if(existingStaffDepartment.getStaffDepartmentId() != staffDepartment.getStaffDepartmentId()) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Staff Department already exists with given name. "
								+ "Please change the name and try again."));
			}
		}
		return staffDao.updateStaffDepartment(staffDepartment, userId);
	}

	public boolean deleteStaffDepartment(int instituteId, UUID userId, UUID staffDepartmentId) {

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (staffDepartmentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if(!CollectionUtils.isEmpty(getStaffDetailsByDepartment(instituteId,  staffDepartmentId))) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Department already contains staffs in it. "
							+ "Please change their department and try again."));
		}
		return staffDao.deleteStaffDepartment(instituteId, staffDepartmentId);
	}


	//staff_designation
	public UUID addStaffDesignation(StaffDesignation staffDesignation, UUID userId) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		validateStaffDesignationPayload(staffDesignation, false);

		StaffDesignation existingStaffDesignation = staffDao.checkStaffDesignationByName(staffDesignation.getInstituteId(),
				staffDesignation.getStaffDesignationName());
		if(existingStaffDesignation != null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Staff Designation already exists with given name. "
							+ "Please change the name and try again."));
		}

		return staffDao.addStaffDesignation(staffDesignation, userId);
	}

	private void validateStaffDesignationPayload(StaffDesignation staffDesignation, boolean update) {
		if (staffDesignation.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if(update) {
			if (staffDesignation.getStaffDesignationId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
			}
		}

		if (StringUtils.isBlank(staffDesignation.getStaffDesignationName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
	}

	public StaffDesignation getStaffDesignationDetailsByDesignationId(int instituteId, UUID staffDesignationId) {

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (staffDesignationId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}


		return staffDao.getStaffDesignationDetailsByDesignationId(instituteId, staffDesignationId);
	}

	public List<StaffDesignation> getStaffDesignationList(int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		return staffDao.getStaffDesignationList(instituteId);
	}

	public boolean updateStaffDesignation(StaffDesignation staffDesignation, UUID userId) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		validateStaffDesignationPayload(staffDesignation, true);
		StaffDesignation existingStaffDesignation = staffDao.checkStaffDesignationByName(staffDesignation.getInstituteId(),
				staffDesignation.getStaffDesignationName());
		if(existingStaffDesignation != null) {
			if(existingStaffDesignation.getStaffDesignationId() != staffDesignation.getStaffDesignationId()) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Staff Designation already exists with given name. "
								+ "Please change the name and try again."));
			}
		}

		return staffDao.updateStaffDesignation(staffDesignation, userId);
	}

	public boolean deleteStaffDesignation(int instituteId, UUID userId, UUID staffDesignationId) {

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (staffDesignationId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if(!CollectionUtils.isEmpty(getStaffDetailsByDesignation(instituteId,  staffDesignationId))) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Designation already contains staffs in it. "
							+ "Please change their designation and try again."));
		}
		return staffDao.deleteStaffDesignation(instituteId, staffDesignationId);
	}

	public List<Staff> getStaffDetailsByCategory(int instituteId, UUID staffCategoryId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (staffCategoryId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		return staffDao.getStaffDetailsByCategory(instituteId, staffCategoryId);
	}

	public List<Staff> getStaffDetailsByDepartment(int instituteId, UUID staffDepartmentId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (staffDepartmentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		List<Staff> staffList =  staffDao.getStaff(instituteId);
		List<Staff> staffDetailsList = new ArrayList<Staff>();
		for(Staff staff : staffList) {
			if(staff.getStaffBasicInfo().getDepartmentDesignationMapping() != null) {
				if(staff.getStaffBasicInfo().getDepartmentDesignationMapping().containsKey(staffDepartmentId)) {
					staffDetailsList.add(staff);
				}
			}
		}
		if(CollectionUtils.isEmpty(staffDetailsList)) {
			return null;
		}
		return staffDetailsList;
	}

	public List<Staff> getStaffDetailsByDesignation(int instituteId, UUID staffDesignationId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (staffDesignationId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		List<Staff> staffList =  staffDao.getStaff(instituteId);
		List<Staff> staffDetailsList = new ArrayList<Staff>();
		for(Staff staff : staffList) {
			if(staff.getStaffBasicInfo().getDepartmentDesignationMapping() != null) {
				if(staff.getStaffBasicInfo().getDepartmentDesignationMapping().containsValue(staffDesignationId)) {
					staffDetailsList.add(staff);
				}
			}
		}
		if(CollectionUtils.isEmpty(staffDetailsList)) {
			return null;
		}
		return staffDetailsList;
	}


	public FullStaffDetails getFullStaffDetails(int instituteId, Staff staff) {


		HashMap<UUID, StaffCategory> categoryMap = getCategoryMap(instituteId);
		HashMap<UUID, StaffDepartment> departmentMap = getDepartmentMap(instituteId);
		HashMap<UUID, StaffDesignation> designationMap = getDesignationMap(instituteId);

		StaffCategory staffCategory = null;
		if(categoryMap.containsKey(staff.getStaffBasicInfo().getStaffCategoryId())) {
			staffCategory = categoryMap.get(staff.getStaffBasicInfo().getStaffCategoryId());
		}

		HashMap<StaffDepartment, StaffDesignation> departmentDesignationMapping = new HashMap<StaffDepartment, StaffDesignation>();
		List<String> departmentDesignationNameList = new ArrayList<String>();
		if(!CollectionUtils.isEmpty(staff.getStaffBasicInfo().getDepartmentDesignationMapping())) {
			for(Entry<UUID, UUID> departmentDesignationMap : staff.getStaffBasicInfo().getDepartmentDesignationMapping().entrySet()) {
				StaffDepartment staffDepartment = null;
				StaffDesignation staffDesignation = null;
				if(departmentMap.containsKey(departmentDesignationMap.getKey())) {
					staffDepartment = departmentMap.get(departmentDesignationMap.getKey());
				}
				if(designationMap.containsKey(departmentDesignationMap.getValue())) {
					staffDesignation = designationMap.get(departmentDesignationMap.getValue());
				}
				if(staffDepartment != null && staffDesignation != null) {
					departmentDesignationMapping.put(staffDepartment, staffDesignation);
					departmentDesignationNameList.add(
							staffDepartment.getStaffDepartmentName() + " (" + staffDesignation.getStaffDesignationName() + ")");
				}
			}
		}

		StaffBasicInfo staffBasicInfo = staff.getStaffBasicInfo();

		StaffBasicDetailsWithCategoryDepartDesignation staffBasicDetailsWithCategoryDepartDesignation =
				new StaffBasicDetailsWithCategoryDepartDesignation(staffBasicInfo.getStaffInstituteId(), staffBasicInfo.getName(),
						staffBasicInfo.getInitials(),
						staffBasicInfo.getGender(), staffBasicInfo.getDateOfBirth(), staffCategory,
						staffBasicInfo.getDepartmentDesignationMapping(),
						CollectionUtils.isEmpty(departmentDesignationMapping) ? null : departmentDesignationMapping,
						CollectionUtils.isEmpty(departmentDesignationNameList) ? null : departmentDesignationNameList,
						staffBasicInfo.getCategory(), staffBasicInfo.getMaritalStatus(), staffBasicInfo.getReligion(),
						staffBasicInfo.getFatherName(), staffBasicInfo.getMotherName(), staffBasicInfo.getBirthPlace(),
						staffBasicInfo.getMotherTongue(), staffBasicInfo.getSpeciallyAbled(), staffBasicInfo.getBpl(),
						staffBasicInfo.getAadharNumber(), staffBasicInfo.getPrimaryContactNumber(),
						staffBasicInfo.getAlternateContactNumber(), staffBasicInfo.getPrimaryEmail(), staffBasicInfo.getSecondaryEmail(), staffBasicInfo.getOasisId(), staffBasicInfo.getNationalCode(), staffBasicInfo.getStaffNatureOfAppointment());

		return new FullStaffDetails(staff.getInstituteId(), staff.getStaffId(),
				staff.getStartDate(), staff.getEndDate(), staff.getStaffStatus(), staffBasicDetailsWithCategoryDepartDesignation,
				staff.getStaffAddressContactInfo(), staff.getStaffQualificationExprienceInfo(),
				staff.getStaffJoiningInfo(), staff.getStaffBankInfo(), staff.getStaffMedicalInfo(), staff.getStaffDocuments(),
				staff.getStaffImage(), staff.getWalletAmount(), staff.getStaffTimingDetails(), staff.isTransportStaff(), staff.getDeviceUserId(), staff.getStaffVisitingDaysList());

	}


	public FullStaffDetails getFullStaffDetails(int instituteId, Staff staff,
												HashMap<UUID, StaffCategory> categoryMap, HashMap<UUID, StaffDepartment> departmentMap,
												HashMap<UUID, StaffDesignation> designationMap) {

		StaffCategory staffCategory = null;
		if(categoryMap.containsKey(staff.getStaffBasicInfo().getStaffCategoryId())) {
			staffCategory = categoryMap.get(staff.getStaffBasicInfo().getStaffCategoryId());
		}

		HashMap<StaffDepartment, StaffDesignation> departmentDesignationMapping = new HashMap<StaffDepartment, StaffDesignation>();
		List<String> departmentDesignationNameList = new ArrayList<String>();
		if(!CollectionUtils.isEmpty(staff.getStaffBasicInfo().getDepartmentDesignationMapping())) {
			for(Entry<UUID, UUID> departmentDesignationMap : staff.getStaffBasicInfo().getDepartmentDesignationMapping().entrySet()) {
				StaffDepartment staffDepartment = null;
				StaffDesignation staffDesignation = null;
				if(departmentMap.containsKey(departmentDesignationMap.getKey())) {
					staffDepartment = departmentMap.get(departmentDesignationMap.getKey());
				}
				if(designationMap.containsKey(departmentDesignationMap.getValue())) {
					staffDesignation = designationMap.get(departmentDesignationMap.getValue());
				}
				if(staffDepartment != null && staffDesignation != null) {
					departmentDesignationMapping.put(staffDepartment, staffDesignation);
					departmentDesignationNameList.add(
							staffDepartment.getStaffDepartmentName() + " (" + staffDesignation.getStaffDesignationName() + ")");
				}
			}
		}

		StaffBasicInfo staffBasicInfo = staff.getStaffBasicInfo();

		StaffBasicDetailsWithCategoryDepartDesignation staffBasicDetailsWithCategoryDepartDesignation =
				new StaffBasicDetailsWithCategoryDepartDesignation(staffBasicInfo.getStaffInstituteId(), staffBasicInfo.getName(),
						staffBasicInfo.getInitials(),
						staffBasicInfo.getGender(), staffBasicInfo.getDateOfBirth(), staffCategory,
						staffBasicInfo.getDepartmentDesignationMapping(),
						CollectionUtils.isEmpty(departmentDesignationMapping) ? null : departmentDesignationMapping,
						CollectionUtils.isEmpty(departmentDesignationNameList) ? null : departmentDesignationNameList,
						staffBasicInfo.getCategory(), staffBasicInfo.getMaritalStatus(), staffBasicInfo.getReligion(),
						staffBasicInfo.getFatherName(), staffBasicInfo.getMotherName(), staffBasicInfo.getBirthPlace(),
						staffBasicInfo.getMotherTongue(), staffBasicInfo.getSpeciallyAbled(), staffBasicInfo.getBpl(),
						staffBasicInfo.getAadharNumber(), staffBasicInfo.getPrimaryContactNumber(),
						staffBasicInfo.getAlternateContactNumber(), staffBasicInfo.getPrimaryEmail(), staffBasicInfo.getSecondaryEmail(), staffBasicInfo.getOasisId(), staffBasicInfo.getNationalCode(), staffBasicInfo.getStaffNatureOfAppointment());

		return new FullStaffDetails(staff.getInstituteId(), staff.getStaffId(),
				staff.getStartDate(), staff.getEndDate(), staff.getStaffStatus(),staffBasicDetailsWithCategoryDepartDesignation,
				staff.getStaffAddressContactInfo(), staff.getStaffQualificationExprienceInfo(),
				staff.getStaffJoiningInfo(), staff.getStaffBankInfo(), staff.getStaffMedicalInfo(), staff.getStaffDocuments(),
				staff.getStaffImage(), staff.getWalletAmount(), staff.getStaffTimingDetails(), staff.isTransportStaff(), staff.getDeviceUserId(), staff.getStaffVisitingDaysList());

	}


	public List<FullStaffDetails> getFullStaffDetails(int instituteId, List<Staff> staffList) {

		HashMap<UUID, StaffCategory> categoryMap = getCategoryMap(instituteId);
		HashMap<UUID, StaffDepartment> departmentMap = getDepartmentMap(instituteId);
		HashMap<UUID, StaffDesignation> designationMap = getDesignationMap(instituteId);

		List<FullStaffDetails> fullStaffDetailsList = new ArrayList<FullStaffDetails>();
		for(Staff staff : staffList) {
			fullStaffDetailsList.add(getFullStaffDetails(instituteId, staff, categoryMap, departmentMap, designationMap));
		}

		return CollectionUtils.isEmpty(fullStaffDetailsList) ? null : fullStaffDetailsList;
	}

	private HashMap<UUID, StaffCategory> getCategoryMap(int instituteId) {
		List<StaffCategory> staffCategoryList = getStaffCategoryList(instituteId);
		HashMap<UUID, StaffCategory> categoryMap = new HashMap<UUID, StaffCategory>();
		for(StaffCategory staffCategory : staffCategoryList) {
			categoryMap.put(staffCategory.getStaffCategoryId(), staffCategory);
		}
		return categoryMap;
	}

	private HashMap<UUID, StaffDepartment> getDepartmentMap(int instituteId) {
		List<StaffDepartment> staffDepartmentList = getStaffDepartmentList(instituteId);
		HashMap<UUID, StaffDepartment> departmentMap = new HashMap<UUID, StaffDepartment>();
		for(StaffDepartment staffDepartment : staffDepartmentList) {
			departmentMap.put(staffDepartment.getStaffDepartmentId(), staffDepartment);
		}
		return departmentMap;
	}

	private HashMap<UUID, StaffDesignation> getDesignationMap(int instituteId) {
		List<StaffDesignation> staffDesignationList = getStaffDesignationList(instituteId);
		HashMap<UUID, StaffDesignation> designationMap = new HashMap<UUID, StaffDesignation>();
		for(StaffDesignation staffDesignation : staffDesignationList) {
			designationMap.put(staffDesignation.getStaffDesignationId(), staffDesignation);
		}
		return designationMap;
	}

	public boolean updateBiometricDeviceIdentification(UUID staffId, AttendanceDeviceServiceProviderType serviceProviderType, DeviceUpdateUserData deviceUpdateUserData){
		return staffDao.updateBiometricDeviceIdentification(staffId, serviceProviderType,deviceUpdateUserData);
	}

	public List<String> getReligionDetails(int instituteId) {
		return staffDao.getReligionDetails(instituteId);
	}

	public boolean updateDeviceUserId(int instituteId, Map<String, String> staffIdDeviceIdMap){
		if(instituteId <= 0 || MapUtils.isEmpty(staffIdDeviceIdMap)){
			logger.error("Invalid institute id {} or empty payload", instituteId);
			return false;
		}
		List<Staff> staffList =  getStaff(instituteId);
		if(CollectionUtils.isEmpty(staffList)){
			logger.warn("No staff found for instituteId {}", instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No staff found for instituteId"));
		}
		Map<String, UUID> staffIdUUIDMap = new HashMap<>();
		Map<String, String> existingDeviceUserIds = new HashMap<>();
		for(Staff staff : staffList){
			staffIdUUIDMap.put(staff.getStaffBasicInfo().getStaffInstituteId().trim().toLowerCase(), staff.getStaffId());
			if(StringUtils.isNotBlank(staff.getDeviceUserId())){
				existingDeviceUserIds.put(staff.getDeviceUserId().trim().toLowerCase(), staff.getStaffBasicInfo().getStaffInstituteId());
			}
		}
		Map<UUID, String> uuidDeviceIdMap = new HashMap<>();
		for(Entry<String, String> mapping : staffIdDeviceIdMap.entrySet()){
			String staffInstId = mapping.getKey().trim().toLowerCase();
			if(!staffIdUUIDMap.containsKey(staffInstId)){
				logger.error("Staff with {} not found for instituteId {}", staffInstId, instituteId);
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Staff " + staffInstId + " not found"));
			}
			if(StringUtils.isNotBlank(mapping.getValue())
					&& existingDeviceUserIds.containsKey(mapping.getValue().trim().toLowerCase())){
				logger.error("Staff with {} is already assigned with {} for instituteId {}", existingDeviceUserIds.containsKey(mapping.getValue().trim().toLowerCase()), mapping.getValue(), instituteId);
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Staff " + existingDeviceUserIds.get(mapping.getValue().trim().toLowerCase()) + " is already assigned with " + mapping.getValue()));
			}
			// device id null is inserted to remove the old value if empty string is provided
			uuidDeviceIdMap.put(staffIdUUIDMap.get(staffInstId), StringUtils.isBlank(mapping.getValue()) ? null : mapping.getValue().trim());
		}

		return staffDao.updateStaffDeviceUserId(instituteId, uuidDeviceIdMap);
	}

	public List<Staff> getStaffs(int instituteId, Set<UUID> staffIdSet) {
		return staffDao.getStaff(instituteId, staffIdSet);
	}

	public Map<UUID, StaffLite> getStaffLiteMap(int instituteId, Set<UUID> staffIds) {
        List<Staff> staffList = getStaffs(instituteId, staffIds);
        List<FullStaffDetails> fullStaffDetails = getFullStaffDetails(instituteId, staffList);
        if(CollectionUtils.isEmpty(staffList)) {
            return null;
        }
        Map<UUID, StaffLite> staffLiteMap = new HashMap<>();
        for(FullStaffDetails staff : fullStaffDetails) {
            if(staff == null || staff.getStaffId() == null) {
                continue;
            }
            staffLiteMap.put(staff.getStaffId(), FullStaffDetails.getStaffLite(staff));
        }
        return staffLiteMap;
    }

	public StaffLite getClassTeacher(int instituteId, int academicSessionId, UUID standardId, Integer sectionId) {
		if(instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}

		if(academicSessionId <= 0) {
			logger.error("Invalid academic session id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id"));
		}

		if(standardId == null) {
			logger.error("Invalid standard id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid standard id"));
		}

		final List<StandardWithStaffDetails> standardWithStaffDetailsList = instituteManager.getStandardWithStaffDetailsList(
				instituteId, academicSessionId, standardId);

		if (!CollectionUtils.isEmpty(standardWithStaffDetailsList)) {
			for (StandardWithStaffDetails standardWithStaffDetails : standardWithStaffDetailsList) {
				if(standardWithStaffDetails == null || standardWithStaffDetails.getStandardRowDetails() == null
						|| standardWithStaffDetails.getStandardRowDetails().getStandardId() == null) {
					continue;
				}
				UUID currentLoopStandardId = standardWithStaffDetails.getStandardRowDetails().getStandardId();
				Integer currentLoopSectionId = standardWithStaffDetails.getStandardRowDetails().getSectionId();
				if (standardId.equals(currentLoopStandardId) && NumberUtils.equal(sectionId, currentLoopSectionId)) {
					return standardWithStaffDetails.getStaffLite();
				}
			}
		}
		return null;
	}
}
