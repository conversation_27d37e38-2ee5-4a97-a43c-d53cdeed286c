package com.lernen.cloud.core.lib.templates;

import java.util.LinkedList;
import java.util.List;
import java.util.Locale;

import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.configurations.EntityValue;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.embrate.cloud.core.api.service.communication.templates.ITemplate;
import com.embrate.cloud.core.api.service.communication.templates.TemplateType;
import com.embrate.cloud.dao.tier.service.communication.templates.ITemplateDao;

/**
 *
 * <AUTHOR>
 *
 */
public class TemplateLoader<T extends ITemplate> {

	private final ITemplateDao<T> templateDao;

	public TemplateLoader(ITemplateDao<T> templateDao) {
		this.templateDao = templateDao;
	}

	T loadTemplate(int instituteId, TemplateType templateType, String templateName, Locale locale,
			DeliveryMode deliveryMode) {

		final List<EntityValue> heirarchy = getLoadTemplateHierarchy(instituteId);
		for (final EntityValue entityValue : heirarchy) {
			final T template = templateDao.getLatestTemplate(templateType, templateName, entityValue.getValue(),
					locale.toString(), deliveryMode);
			if (template != null) {
				return template;
			}
		}
		throw new EmbrateRunTimeException("No template configured for " + instituteId);
	}

	private List<EntityValue> getLoadTemplateHierarchy(int instituteId) {
		final List<EntityValue> entityValues = new LinkedList<>();
		entityValues.add(new EntityValue(Entity.INSTITUTE, String.valueOf(instituteId)));
		entityValues.add(new EntityValue(Entity.GLOBAL, Entity.GLOBAL.name()));
		return entityValues;
	}
}
