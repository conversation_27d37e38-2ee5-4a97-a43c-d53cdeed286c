package com.lernen.cloud.core.lib.fees.configuration;

import com.embrate.cloud.core.api.fee.*;
import com.embrate.cloud.core.api.fee.configuration.FeeAssignmentAmountsPayload;
import com.embrate.cloud.core.utils.EMapUtils;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.exception.*;
import com.lernen.cloud.core.api.fees.*;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.audit.log.FeeAuditLogWriter;
import com.lernen.cloud.core.lib.fees.payment.FeeCalculator;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.dao.tier.fees.configuration.FeeConfigurationDao;
import com.lernen.cloud.dao.tier.fees.payment.FeePaymentTransactionDao;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.time.*;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class FeeConfigurationManager {

    private static final Logger logger = LogManager.getLogger(FeeConfigurationManager.class);

    private final FeeConfigurationDao feeConfigurationDao;

    private final FeeCalculator feeCalculator;

    private final FeePaymentManager feePaymentManager;

    private final InstituteManager instituteManager;

    private final StudentManager studentManager;

    private final FeePaymentTransactionDao feePaymentTransactionDao;

    private final TransactionTemplate transactionTemplate;

    private final UserPermissionManager userPermissionManager;

    private final FeeAuditLogWriter feeAuditLogWriter;

    public FeeConfigurationManager(FeeConfigurationDao feeConfigurationDao, FeeCalculator feeCalculator,
                                   FeePaymentManager feePaymentManager, InstituteManager instituteManager, StudentManager studentManager,
                                   FeePaymentTransactionDao feePaymentTransactionDao, TransactionTemplate transactionTemplate,
                                   UserPermissionManager userPermissionManager, FeeAuditLogWriter feeAuditLogWriter) {
        this.feeConfigurationDao = feeConfigurationDao;
        this.feeCalculator = feeCalculator;
        this.feePaymentManager = feePaymentManager;
        this.instituteManager = instituteManager;
        this.studentManager = studentManager;
        this.feePaymentTransactionDao = feePaymentTransactionDao;
        this.transactionTemplate = transactionTemplate;
        this.userPermissionManager = userPermissionManager;
        this.feeAuditLogWriter = feeAuditLogWriter;
    }

    public EntityFeeAssignment getFeeAssignment(int instituteId, FeeEntity feeEntity, String entityId,
                                                Integer academicSessionId) {
        return getFeeAssignment(instituteId, feeEntity, entityId, academicSessionId, false);
    }

    public EntityFeeAssignment getFeeAssignment(int instituteId, FeeEntity feeEntity, String entityId,
                                               Integer academicSessionId, boolean sortFees) {
        EntityFeeAssignment entityFeeAssignment = feeCalculator.getFeeAssigment(instituteId, feeEntity, entityId, academicSessionId);
        if (!sortFees) {
            return entityFeeAssignment;
        }
        Collections.sort(entityFeeAssignment.getFeeIdFeeHeadDetailsList(), new Comparator<FeeIdFeeHeadDetails>() {
            @Override
            public int compare(FeeIdFeeHeadDetails f1, FeeIdFeeHeadDetails f2) {
                Integer d1 = f1.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getDueDate();
                Integer d2 = f2.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getDueDate();
                Integer compareResult = NumberUtils.compare(d1, d2);
                if(compareResult != null){
                    return compareResult.intValue();
                }

                int p1 = f1.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeType().getPriority();
                int p2 = f2.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeType().getPriority();
                if (p1 != p2) {
                    return p1 - p2;
                }

                String fName1 = f1.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeName();
                String fName2 = f2.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeName();

                return StringUtils.compare(fName1, fName2);
            }
        });
        for (FeeIdFeeHeadDetails feeIdFeeHeadDetails : entityFeeAssignment.getFeeIdFeeHeadDetailsList()) {
            Collections.sort(feeIdFeeHeadDetails.getFeeHeadAmountDetailsList(), new Comparator<FeeHeadAmountDetails>() {
                @Override
                public int compare(FeeHeadAmountDetails fh1, FeeHeadAmountDetails fh2) {
                    return StringUtils.compare(fh1.getFeeHeadConfiguration().getFeeHead(), fh2.getFeeHeadConfiguration().getFeeHead());
                }
            });
        }
        return entityFeeAssignment;
    }

    public List<ResolvedDefaultEntityFeeAssignmentStructure> getEnrollmentDefaultFeeAssignmentStructure(int instituteId,
                                                                                                        UUID studentId) {
        final Student student = studentManager.getStudentByLatestAcademicSession(studentId,
                Arrays.asList(StudentStatus.ENROLMENT_PENDING));
        if (student == null) {
            logger.error("No student found with id {}", studentId);
            return null;
        }
        return getDefaultFeeAssignmentStructure(instituteId,
                student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
                student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId(),
                Arrays.asList(FeeStructureType.ENROLLMENT), false);
    }

    public EntityFeeAssignment getStudentFeeAssigment(int instituteId, Student student){
        return feeCalculator.getStudentFeeAssigment(instituteId, student);
    }

    public List<ResolvedDefaultEntityFeeAssignmentStructure> getDefaultFeeAssignmentStructure(int instituteId,
                                                                                              int academicSessionId, UUID standardId, List<FeeStructureType> feeStructureTypes,
                                                                                              boolean onlyPendingEnrollmentAllowedFees) {
        return feeCalculator.getDefaultFeeAssignmentStructure(instituteId, academicSessionId, standardId,
                feeStructureTypes, onlyPendingEnrollmentAllowedFees);
    }

    public List<DefaultEntityFeeAssignmentStructure> getDefaultFeeAssignmentStructure(int instituteId,
                                                                                      int academicSessionId) {
        return feeConfigurationDao.getDefaultFeeAssignmentStructure(instituteId, academicSessionId, null);
    }

    public List<DefaultEntityFeeAssignmentStructure> getDefaultFeeAssignmentStructure(int instituteId, int academicSessionId,
                                                                                      List<UUID> feeStructureIdsList) {
        return feeConfigurationDao.getDefaultFeeAssignmentStructure(instituteId, academicSessionId, feeStructureIdsList);
    }

    public boolean createDefaultFeeAssignmentStructure(int instituteId, int academicSessionId,
                                                       List<DefaultEntityFeeAssignmentStructurePayload> defaultEntityFeeAssignmentStructurePayloads, UUID userId, boolean skipAuth) {
        if (!skipAuth) {
            userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_STRUCTURE);
        }
        if (instituteId <= 0 || academicSessionId <= 0
                || CollectionUtils.isEmpty(defaultEntityFeeAssignmentStructurePayloads)) {
            logger.error(
                    "Invalid DefaultEntityFeeAssignmentStructure with instituteId {},  academicSessionId {}, payload {}",
                    instituteId, academicSessionId, defaultEntityFeeAssignmentStructurePayloads);
            return false;
        }
        if (!validDefaultFeeStructure(instituteId, academicSessionId, defaultEntityFeeAssignmentStructurePayloads,
                false)) {
            return false;
        }
        return feeConfigurationDao.createDefaultFeeAssignmentStructure(instituteId, academicSessionId,
                defaultEntityFeeAssignmentStructurePayloads);
    }

    private boolean validDefaultFeeStructure(int instituteId, int academicSessionId,
                                             List<DefaultEntityFeeAssignmentStructurePayload> defaultEntityFeeAssignmentStructurePayloads,
                                             Boolean update) {
        final List<DefaultEntityFeeAssignmentStructure> defaultEntityFeeAssignmentStructures = feeConfigurationDao
                .getDefaultFeeAssignmentStructure(instituteId, academicSessionId, null);
        final Set<String> existingFeeStructures = new HashSet<>();
        // Currently expectation is that only single online registration structure would present as we are not expecting
        // any resolution logic for final amount computation for online payment
        boolean onlineRegistrationStructureExists = false;
        for (final DefaultEntityFeeAssignmentStructure existingDefaultEntityFeeAssignmentStructure : defaultEntityFeeAssignmentStructures) {
            if(existingDefaultEntityFeeAssignmentStructure.getFeeStructureType() == FeeStructureType.ONLINE_STUDENT_REGISTRATION){
                onlineRegistrationStructureExists = true;
            }

            if (update) {
                if (existingDefaultEntityFeeAssignmentStructure.getStructureId()
                        .compareTo(defaultEntityFeeAssignmentStructurePayloads.get(0).getStructureId()) == 0) {
                    continue;
                }
            }
            existingFeeStructures
                    .add(existingDefaultEntityFeeAssignmentStructure.getStructureName().trim().toLowerCase());

        }
        for (final DefaultEntityFeeAssignmentStructurePayload defaultEntityFeeAssignmentStructurePayload : defaultEntityFeeAssignmentStructurePayloads) {
            if (StringUtils.isBlank(defaultEntityFeeAssignmentStructurePayload.getStructureName())) {
                logger.error("Fee structure name is invalid.");
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
                        "Fee structure name is invalid"));
            }
            if (existingFeeStructures
                    .contains(defaultEntityFeeAssignmentStructurePayload.getStructureName().trim().toLowerCase())) {
                logger.error("Fee structure with name {} already exists for institute {}, session {}",
                        defaultEntityFeeAssignmentStructurePayload.getStructureName().trim().toLowerCase(), instituteId,
                        academicSessionId);
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
                        "Fee structure with name " + defaultEntityFeeAssignmentStructurePayload.getStructureName()
                                + " already exists."));
            }

            if(!update && defaultEntityFeeAssignmentStructurePayload.getFeeStructureType() == FeeStructureType.ONLINE_STUDENT_REGISTRATION &&
                    onlineRegistrationStructureExists){
                logger.error("Fee structure for online student registration already exists for institute {}, session {}", instituteId,
                        academicSessionId);
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
                        "Fee structure for online student registration already exists. Only single structure can be created for online registration."));
            }
        }

        final List<FeeConfigurationResponse> feeConfigurationResponses = getFeeConfigurationByAcademicYear(instituteId,
                academicSessionId);
        final Set<UUID> feeIdInSession = new HashSet<>();
        for (final FeeConfigurationResponse feeConfigurationResponse : feeConfigurationResponses) {
            feeIdInSession.add(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeId());
        }

        for (final DefaultEntityFeeAssignmentStructurePayload defaultEntityFeeAssignmentStructurePayload : defaultEntityFeeAssignmentStructurePayloads) {
            if (defaultEntityFeeAssignmentStructurePayload.getFeeStructureType() == null) {
                logger.error("Fee structure type is invalid.");
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
                        "Fee structure type is invalid"));
            }
            boolean feePresent = false;
            for (final EntityFeeAssignmentPayload entityFeeAssignmentPayload : defaultEntityFeeAssignmentStructurePayload
                    .getEntityFeeAssignmentPayloads()) {
                if (CollectionUtils.isEmpty(entityFeeAssignmentPayload.getFeeIdFeeHeadsList())) {
                    continue;
                }
                feePresent = true;
                for (final FeeIdFeeHead feeIdFeeHead : entityFeeAssignmentPayload.getFeeIdFeeHeadsList()) {
                    if (!feeIdInSession.contains(feeIdFeeHead.getFeeId())) {
                        logger.error("Fee Id {} does not belong in institute {}, session {}", feeIdFeeHead.getFeeId(),
                                instituteId, academicSessionId);
                        throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
                                        "Fee assigned in structure is invalid"));
                    }
                    for (final FeeHeadAmount feeHeadAmount : feeIdFeeHead.getFeeHeadAmountList()) {
                        if (feeHeadAmount.getAmount() == null || feeHeadAmount.getAmount() <= 0) {
                            throw new ApplicationException(
                                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
                                            "Fee Head amount cannot be zero or negative."));
                        }
                    }
                }
            }
            if (!feePresent) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
                        "No fees added. Please add atleast one fees and try again."));
            }
        }
        return true;
    }

    public boolean addFeeCategoryList(int instituteId, List<FeeCategoryPayload> feeCategoryList) {
        return feeConfigurationDao.addFeeCategoryList(instituteId, feeCategoryList);
    }

    /**
     * This method gets the fee category list to which the fee head belongs.
     *
     * @param instituteId
     * @return
     */
    public List<FeeCategory> getCategoryList(int instituteId) {
        return feeConfigurationDao.getCategoryList(instituteId);
    }

    public FeeHeadConfiguration getFeeHeadConfiguration(int instituteId, String feeHeadName) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute."));
        }

        if (StringUtils.isBlank(feeHeadName)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Empty fee Head Name."));
        }
        return feeConfigurationDao.getFeeHeadConfiguration(instituteId, feeHeadName);
    }

    /**
     * All validations of fee configuration
     */
    private void validateFeeConfiguration(FeeConfigurationBasicInfo feeConfigurationBasicInfo) {
        if (feeConfigurationBasicInfo == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Empty fee configuration."));
        }

        validateFeeConfiguratinBasicInfo(feeConfigurationBasicInfo);

        if (feeConfigurationBasicInfo.getFeeType() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Invalid fee variation."));
        }
    }

    /**
     * This method validates the basic information of any fee configuration.
     *
     * @param feeConfigurationBasicInfo
     */
    private void validateFeeConfiguratinBasicInfo(FeeConfigurationBasicInfo feeConfigurationBasicInfo) {
        if (feeConfigurationBasicInfo == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
                    "Empty fee configuration basic info."));
        }
        if (feeConfigurationBasicInfo.getInstituteId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Invalid Institue Id."));
        }

        if (StringUtils.isEmpty(feeConfigurationBasicInfo.getFeeName())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Invalid Fee name."));
        }
        if (feeConfigurationBasicInfo.getAcademicSessionId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Invalid academic session."));
        }
    }

    /**
     * This method validates the fee configuration fields while updation.
     *
     * @param feeConfigurationUpdatePayload
     */
    private void validateFeeConfigurationUpdate(FeeConfigurationUpdatePayload feeConfigurationUpdatePayload) {

        if (feeConfigurationUpdatePayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Invalid request."));
        }

        if (feeConfigurationUpdatePayload.getInstituteId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Invalid Institue Id."));
        }

        if (StringUtils.isEmpty(feeConfigurationUpdatePayload.getFeeName())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Invalid Fee name."));
        }
        if (feeConfigurationUpdatePayload.getFeeId() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Invalid fee id."));
        }

    }

    /**
     * This method validates the special fee configuration fields while
     * updation.
     *
     * @param specialFeeConfigurationUpdatePayload
     */
    private void validateSpecialFeeConfigurationUpdate(
            SpecialFeeConfigurationUpdatePayload specialFeeConfigurationUpdatePayload) {
        if (specialFeeConfigurationUpdatePayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION, "Invalid request."));
        }
        validateFeeConfigurationUpdate(specialFeeConfigurationUpdatePayload.getFeeConfigurationUpdatePayload());

    }

    /**
     * This method validates the month range of the special fee configuration,
     * that the range should fall within the academic year range.
     *
     * @param academicSessionId
     * @param startMonth
     * @param endMonth
     * @return
     */
    private List<MonthYear> validateSpecialFeesMonthRange(int academicSessionId, Month startMonth, Month endMonth) {

        final List<MonthYear> monthYears = new ArrayList<>();

        final AcademicSession academicSession = instituteManager
                .getAcademicSessionByAcademicSessionId(academicSessionId);
        int startYear = 0;
        int endYear = 0;
        if (startMonth.getValue() < academicSession.getStartMonth().getValue()) {
            startYear = academicSession.getEndYear();
        } else {
            startYear = academicSession.getStartYear();
        }
        if (endMonth.getValue() < academicSession.getStartMonth().getValue()) {
            endYear = academicSession.getEndYear();
        } else {
            endYear = academicSession.getStartYear();
        }

        DateTime startDate = new DateTime();
        startDate = startDate.withYear(startYear).withMonthOfYear(startMonth.getValue()).withDayOfMonth(1)
                .withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0);

        DateTime endDate = new DateTime();
        endDate = endDate.withYear(endYear).withMonthOfYear(endMonth.getValue()).withDayOfMonth(1).withHourOfDay(0)
                .withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0);

        if (startDate.getMillis() > endDate.getMillis()) {
            return null;
        }

        while (startDate.getMillis() <= endDate.getMillis()) {
            monthYears.add(new MonthYear(Month.of(startDate.getMonthOfYear()), startDate.getYear()));
            startDate = startDate.plusMonths(1);
        }
        return monthYears;
    }

    /**
     * @return feeId
     */
    public UUID addFeeConfiguration(FeeConfigurationBasicInfo feeConfigurationBasicInfo) {
        validateFeeConfiguration(feeConfigurationBasicInfo);
        /**
         * Update due date if not specified
         */
        if (feeConfigurationBasicInfo.getDueDate() == null || feeConfigurationBasicInfo.getDueDate() <= 0) {
            final AcademicSession academicSession = instituteManager
                    .getAcademicSessionByAcademicSessionId(feeConfigurationBasicInfo.getAcademicSessionId());
            feeConfigurationBasicInfo.setDueDate(academicSession.getSessionEndTime());
        }

        return feeConfigurationDao.addFeeConfiguration(feeConfigurationBasicInfo);
    }

    public List<UUID> addFeeConfiguration(List<FeeConfigurationBasicInfo> feeConfigurationList) {
        for (final FeeConfigurationBasicInfo feeConfigurationBasicInfo : feeConfigurationList) {
            validateFeeConfiguration(feeConfigurationBasicInfo);
            /**
             * Update due date if not specified
             */
            if (feeConfigurationBasicInfo.getDueDate() == null || feeConfigurationBasicInfo.getDueDate() <= 0) {
                final AcademicSession academicSession = instituteManager
                        .getAcademicSessionByAcademicSessionId(feeConfigurationBasicInfo.getAcademicSessionId());
                feeConfigurationBasicInfo.setDueDate(academicSession.getSessionEndTime());
            }
        }
        return feeConfigurationDao.addFeeConfiguration(feeConfigurationList);
    }

    private boolean regularFeesExists(int instituteId, int academicSessionId) {
        final List<FeeConfigurationResponse> feeConfigurationResponses = feeConfigurationDao
                .getFeeConfigurationByAcademicYear(instituteId, academicSessionId);
        for (final FeeConfigurationResponse feeConfigurationResponse : feeConfigurationResponses) {
            if (FeeType.REGULAR.equals(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeType())) {
                return true;
            }
        }
        return false;
    }

    /**
     * This method adds regular fees configurations. On the basis of duration
     * and start month it automatically prepares the month year list.
     *
     * @param instituteId
     * @param duration
     * @param academicSessionId
     * @return
     */
    public boolean addRegularFeeConfiguration(int instituteId, int duration, int academicSessionId, boolean fineApplicable, boolean transferToWallet, UUID userId) {

        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_CONFIGURATION);

        if (regularFeesExists(instituteId, academicSessionId)) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION,
                    "Regular Fees Already Exists. Please delete existing regular fees to add new ones"));
        }
        final List<FeeConfigurationBasicInfo> feeConfigurationList = new ArrayList<>();
        Month sMonth = null;
        Month emonth = null;
        int syear = 0;
        int eyear = 0;
        final AcademicSession academicSession = instituteManager
                .getAcademicSessionByAcademicSessionId(academicSessionId);

        Month startMonth = academicSession.getStartMonth();
        int startYear = academicSession.getStartYear();
        for (int i = 0; i < 12 / duration; i++) {
            sMonth = startMonth;
            syear = startYear;
            final FeeConfigurationBasicInfo feeConfigurationBasicInfo = new FeeConfigurationBasicInfo();
            feeConfigurationBasicInfo.setInstituteId(instituteId);
            feeConfigurationBasicInfo.setAcademicSessionId(academicSessionId);
            feeConfigurationBasicInfo.setFeeType(FeeType.REGULAR);
            feeConfigurationBasicInfo.setFineApplicable(fineApplicable);
            feeConfigurationBasicInfo.setTransferToWallet(transferToWallet);

            final List<MonthYear> monthYearList = new ArrayList<>();
            for (int j = 0; j < duration; j++) {
                final MonthYear monthYear = new MonthYear();
                monthYear.setMonth(Month.of((startMonth.getValue() + j) > 12 ? (startMonth.getValue() + j) % 12
                        : startMonth.getValue() + j));
                monthYear.setYear(startYear);
                monthYearList.add(monthYear);
                emonth = monthYear.getMonth();
                eyear = monthYear.getYear();
                if (monthYear.getMonth().getValue() == 12) {
                    startYear++;
                }
            }
            startMonth = Month.of((startMonth.getValue() + duration) > 12 ? (startMonth.getValue() + duration) % 12
                    : startMonth.getValue() + duration);

            if (sMonth == emonth) {
                feeConfigurationBasicInfo.setFeeName(sMonth + " " + syear);
            } else {
                feeConfigurationBasicInfo.setFeeName(sMonth + " " + syear + " - " + emonth + " " + eyear);
            }

            if (!CollectionUtils.isEmpty(monthYearList)) {
                feeConfigurationBasicInfo.setStartMonthYear(monthYearList.get(0));
                feeConfigurationBasicInfo.setEndMonthYear(monthYearList.get(monthYearList.size() - 1));
            }

            feeConfigurationList.add(feeConfigurationBasicInfo);
        }

        if (CollectionUtils.isEmpty(feeConfigurationList)) {
            return false;
        }
        final List<UUID> feeIds = addFeeConfiguration(feeConfigurationList);
        if (!CollectionUtils.isEmpty(feeIds)) {
            feeAuditLogWriter.addRegularFeeConfigurationCreateLog(instituteId, userId, duration, feeConfigurationList);
            return true;
        }
        return false;
    }

    /**
     * This method add special fees configurations.
     *
     * @param specialFeeConfigurationPayload
     * @return
     */
    public Boolean addSpecialFeeConfiguration(SpecialFeeConfigurationPayload specialFeeConfigurationPayload,
                                              UUID userId) {

        userPermissionManager.verifyAuthorisation(
                specialFeeConfigurationPayload.getFeeConfigurationBasicInfo().getInstituteId(), userId,
                AuthorisationRequiredAction.FEE_CONFIGURATION);

        final List<MonthYear> monthYearList = validateSpecialFeesMonthRange(
                specialFeeConfigurationPayload.getFeeConfigurationBasicInfo().getAcademicSessionId(),
                specialFeeConfigurationPayload.getStartMonth(), specialFeeConfigurationPayload.getEndMonth());
        if (CollectionUtils.isEmpty(monthYearList)) {
            return false;
        }
        specialFeeConfigurationPayload.getFeeConfigurationBasicInfo().setStartMonthYear(monthYearList.get(0));
        specialFeeConfigurationPayload.getFeeConfigurationBasicInfo()
                .setEndMonthYear(monthYearList.get(monthYearList.size() - 1));
        specialFeeConfigurationPayload.getFeeConfigurationBasicInfo().setFeeType(FeeType.SPECIAL);
        final UUID feeId = addFeeConfiguration(specialFeeConfigurationPayload.getFeeConfigurationBasicInfo());
        if (feeId != null) {
            feeAuditLogWriter.addSpecialFeeConfigurationCreateLog(userId,
                    specialFeeConfigurationPayload.getFeeConfigurationBasicInfo());
            return true;
        }
        return false;
    }

    /**
     * This method adds one time fees configurations.
     *
     * @param feeConfigurationBasicInfo
     * @return
     */
    public Boolean addOneTimeFeeConfiguration(FeeConfigurationBasicInfo feeConfigurationBasicInfo, UUID userId) {

        userPermissionManager.verifyAuthorisation(feeConfigurationBasicInfo.getInstituteId(), userId,
                AuthorisationRequiredAction.FEE_CONFIGURATION);

        feeConfigurationBasicInfo.setFeeType(FeeType.ONE_TIME);
        final UUID feeId = addFeeConfiguration(feeConfigurationBasicInfo);
        if (feeId != null) {
            feeAuditLogWriter.addOneTimeFeeConfigurationCreateLog(userId, feeConfigurationBasicInfo);
            return true;
        }
        return false;
    }

    // Update Fee Configuration APIs

    /**
     * This API updates one time or regular fees.
     *
     * @param feeConfigurationUpdatePayload
     * @return
     */
    public UUID updateFeeConfiguration(FeeConfigurationUpdatePayload feeConfigurationUpdatePayload, UUID userId) {
        userPermissionManager.verifyAuthorisation(feeConfigurationUpdatePayload.getInstituteId(), userId,
                AuthorisationRequiredAction.FEE_CONFIGURATION);
        validateFeeConfigurationUpdate(feeConfigurationUpdatePayload);
        return feeConfigurationDao.updateFeeConfiguration(feeConfigurationUpdatePayload);
    }

    /**
     * This API updates special fees configurations. The start and end month of
     * special fees can also be updated.
     *
     * @param specialFeeConfigurationUpdatePayload
     * @return fee id
     */
    public UUID updateSpecialFeeConfiguration(SpecialFeeConfigurationUpdatePayload specialFeeConfigurationUpdatePayload,
                                              UUID userId) {
        userPermissionManager.verifyAuthorisation(
                specialFeeConfigurationUpdatePayload.getFeeConfigurationUpdatePayload().getInstituteId(), userId,
                AuthorisationRequiredAction.FEE_CONFIGURATION);
        validateSpecialFeeConfigurationUpdate(specialFeeConfigurationUpdatePayload);
        final FeeConfigurationResponse feeConfigurationResponse = getFeeConfiguration(
                specialFeeConfigurationUpdatePayload.getFeeConfigurationUpdatePayload().getInstituteId(),
                specialFeeConfigurationUpdatePayload.getFeeConfigurationUpdatePayload().getFeeId());

        if (feeConfigurationResponse == null) {
            return null;
        }

        final List<MonthYear> monthYearList = validateSpecialFeesMonthRange(
                feeConfigurationResponse.getFeeConfigurationBasicInfo().getAcademicSessionId(),
                specialFeeConfigurationUpdatePayload.getStartMonth(),
                specialFeeConfigurationUpdatePayload.getEndMonth());
        if (CollectionUtils.isEmpty(monthYearList)) {
            return null;
        }

        return feeConfigurationDao.updateSpecialFeeConfiguration(
                specialFeeConfigurationUpdatePayload.getFeeConfigurationUpdatePayload(), monthYearList.get(0),
                monthYearList.get(monthYearList.size() - 1));

    }

    // Delete Fee Configuration APIs

    /**
     * This API deletes regular fees for the given session.
     *
     * @param instituteId
     * @param academicSessionId
     * @return
     */
    public boolean deleteRegularFeeConfigurationByAcademicSession(int instituteId, int academicSessionId, UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_CONFIGURATION);
        final boolean success = feeConfigurationDao.deleteRegularFeeConfigurationByAcademicSession(instituteId,
                academicSessionId);
        if (success) {
            feeAuditLogWriter.addRegularFeeConfigurationDeleteLog(instituteId, userId, academicSessionId);
        }
        return success;
    }

    /**
     * This API deletes one time or special fees on the basis of fee type and
     * fee id.
     *
     * @param instituteId
     * @param feeId
     * @param feeType
     * @return
     */
    public boolean deleteFeeConfigurationByFeeId(int instituteId, UUID feeId, FeeType feeType, UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_CONFIGURATION);
        final boolean success = feeConfigurationDao.deleteFeeConfigurationByFeeId(instituteId, feeId, feeType);
        if (success) {
            feeAuditLogWriter.addFeeConfigurationDeleteLog(instituteId, userId, feeType, feeId);
        }

        return success;
    }

    // Get Fee Configuration APIs

    /**
     * This method gets the fee configuration on the basis of fee id.
     *
     * @param instituteId
     * @param feeId
     * @return
     */
    public FeeConfigurationResponse getFeeConfiguration(int instituteId, UUID feeId) {
        return feeConfigurationDao.getFeeConfiguration(instituteId, feeId);
    }

    /**
     * This method gets all the fee configurations of the given academic session
     *
     * @param instituteId
     * @param academicSessionId
     * @return
     */
    public List<FeeConfigurationResponse> getFeeConfigurationByAcademicYear(int instituteId, int academicSessionId) {
        return feeConfigurationDao.getFeeConfigurationByAcademicYear(instituteId, academicSessionId);
    }

    public Map<Integer, List<FeeConfigurationResponse>> getAllFeeConfiguration(int instituteId) {
        return feeConfigurationDao.getAllFeeConfiguration(instituteId);
    }

    public List<FeeConfigurationResponse> getFeeConfigurations(int instituteId, Set<UUID> feeIds) {
        return feeConfigurationDao.getFeeConfigurations(instituteId, feeIds);
    }

    public List<AuthorizedFeeAssignment> getAuthorizedFeeAssignment(int instituteId, int academicSessionId) {
        List<AuthorizedFeeAssignment> authorizedFeeAssignmentList = feeConfigurationDao.getAuthorizedFeeAssignment(instituteId, academicSessionId);
        Collections.sort(authorizedFeeAssignmentList);
        return authorizedFeeAssignmentList;
    }

    public FeeIdFeeHeadData getFeeIdFeeHeadData(int instituteId, int academicSessionId) {

        final List<FeeConfigurationResponse> feeConfigurationResponses = feeConfigurationDao
                .getFeeConfigurationByAcademicYear(instituteId, academicSessionId);
        final List<FeeHeadConfigurationResponse> feeHeadConfigurationResponseList = feeConfigurationDao
                .getFeeHeadConfiguration(instituteId);

        final List<FeeConfigurationBasicInfo> feeConfigurationBasicInfoList = new ArrayList<>();
        for (final FeeConfigurationResponse feeConfigurationResponse : feeConfigurationResponses) {
            feeConfigurationBasicInfoList.add(feeConfigurationResponse.getFeeConfigurationBasicInfo());
        }

        return new FeeIdFeeHeadData(feeConfigurationBasicInfoList, feeHeadConfigurationResponseList);
    }

    // Fee Head Configurations

    /**
     * All validations of fee head configurations
     */
    private void validateFeeHeadConfigurations(List<FeeHeadConfiguration> feeHeadConfigurationList) {
        if (CollectionUtils.isEmpty(feeHeadConfigurationList)) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION,
                    "Empty fee head configuration."));
        }
        for (final FeeHeadConfiguration feeHeadConfiguration : feeHeadConfigurationList) {
            if (feeHeadConfiguration.getInstituteId() <= 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION, "Empty Institue Id."));
            }
            if (feeHeadConfiguration.getFeeCategoryId() <= 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION, "Empty fee category."));
            }
            if (StringUtils.isBlank(feeHeadConfiguration.getFeeHead())) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION, "Empty fee head."));
            }
        }
    }

    private void validateFeeHeadConfigurationForUpdate(FeeHeadConfiguration feeHeadConfiguration) {

        if (feeHeadConfiguration == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION,
                    "Invalid fee head configuration."));
        }
        if (feeHeadConfiguration.getFeeHeadId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION, "Invalid fee Id."));
        }
        if (feeHeadConfiguration.getInstituteId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION, "Invalid Institue Id."));
        }
        if (feeHeadConfiguration.getFeeCategoryId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION, "Invalid fee category."));
        }
        if (StringUtils.isBlank(feeHeadConfiguration.getFeeHead())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION, "Invalid fee head."));
        }
    }

    /**
     * This method adds multiple fee head configurations of custom fee head
     * types
     *
     * @param feeHeadConfigurationList
     * @return
     */
    public boolean addFeeHeadConfigurations(int instituteId, List<FeeHeadConfiguration> feeHeadConfigurationList,
                                            UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.FEE_HEAD_CONFIGURATION);
        validateFeeHeadConfigurations(feeHeadConfigurationList);
        return feeConfigurationDao.addFeeHeadConfiguration(feeHeadConfigurationList, false);
    }

    /**
     * This method updates single fee head configuration on the basis of fee
     * head id for the given institute
     *
     * @param feeHeadConfiguration
     * @return
     */
    public int updateFeeHeadConfiguration(FeeHeadConfiguration feeHeadConfiguration, UUID userId) {
        userPermissionManager.verifyAuthorisation(feeHeadConfiguration.getInstituteId(), userId,
                AuthorisationRequiredAction.FEE_HEAD_CONFIGURATION);
        validateFeeHeadConfigurationForUpdate(feeHeadConfiguration);
        return feeConfigurationDao.updateFeeHeadConfiguration(feeHeadConfiguration, false);
    }

    /**
     * This method deletes single fee head configuration on the basis of fee
     * head id for the given institute
     *
     * @param instituteId
     * @param feeHeadId
     * @return
     */
    public boolean deleteFeeHeadConfiguration(int instituteId, int feeHeadId, UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.FEE_HEAD_CONFIGURATION);
        return feeConfigurationDao.deleteFeeHeadConfiguration(instituteId, feeHeadId, false);
    }

    /**
     * This method gets all the fee head configurations for the given institute
     *
     * @param instituteId
     * @return
     */
    public List<FeeHeadConfigurationResponse> getFeeHeadConfiguration(int instituteId) {
        return feeConfigurationDao.getFeeHeadConfiguration(instituteId);
    }

    // Fee Assignment APIs

    /**
     * Fee assignment details validations
     */
    private void validateFeeAssignmentDetails(FeeAssignmentDetails feeAssignmentDetails,
                                              FeeAssignmentState feeAssignmentState) {
        if (feeAssignmentDetails == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT, "Invalid fee assignment details"));
        }
        if (feeAssignmentDetails.getInstituteId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT, "Invalid Institue Id."));
        }
        if (feeAssignmentDetails.getFeeId() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT, "Invalid fee Id."));
        }
        if (CollectionUtils.isEmpty(feeAssignmentDetails.getEntityFees())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT, "Invalid entity fees."));
        }
        for (final EntityFees entityFees : feeAssignmentDetails.getEntityFees()) {
            if (StringUtils.isBlank(entityFees.getEntityId())) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT, "Invalid entity id."));
            }
            if (entityFees.getFeeEntity() == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT, "Invalid fee entity name."));
            }
            if (feeAssignmentState == FeeAssignmentState.NEW
                    || feeAssignmentState == FeeAssignmentState.PARTIAL_UPDATE) {
                if (CollectionUtils.isEmpty(entityFees.getFeeHeadAmount())) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
                            "Invalid fee head amount list."));
                }
                for (final FeeHeadAmount feeHeadAmount : entityFees.getFeeHeadAmount()) {
                    if (feeHeadAmount.getFeeHeadId() <= 0) {
                        throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT, "Invalid fee head id."));
                    }
                    if (feeHeadAmount.getAmount() == null || feeHeadAmount.getAmount() <= 0) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
                                "Invalid fee head amount."));
                    }
                }
            } else if (feeAssignmentState == FeeAssignmentState.COMPLETE_UPDATE) {
                if (!CollectionUtils.isEmpty(entityFees.getFeeHeadAmount())) {
                    for (final FeeHeadAmount feeHeadAmount : entityFees.getFeeHeadAmount()) {
                        if (feeHeadAmount.getFeeHeadId() <= 0) {
                            throw new ApplicationException(new ErrorResponse(
                                    ApplicationErrorCode.INVALID_FEE_ASSIGNMENT, "Invalid fee head id."));
                        }
                        if (feeHeadAmount.getAmount() != null && feeHeadAmount.getAmount() <= 0) {
                            throw new ApplicationException(new ErrorResponse(
                                    ApplicationErrorCode.INVALID_FEE_ASSIGNMENT, "Invalid fee head amount."));
                        }
                    }
                }
            }

        }

    }

    public boolean assignFeesWithoutTransactionCancellation(FeeAssignmentDetails feeAssignmentDetails, Module module,
                                                            FeeAssignmentState feeAssignmentState, UUID userId) {
        validateFeeAssignmentDetails(feeAssignmentDetails, feeAssignmentState);

        final boolean success = feeConfigurationDao.assignFeesWithoutTransaction(feeAssignmentDetails, module,
                feeAssignmentState);
        if (success) {
            feeAuditLogWriter.addFeeAssignmentLog(feeAssignmentDetails, userId);
        }
        return success;
    }

    public boolean updateAssignedFeeAmounts(int instituteId, int academicSessionId, FeeAssignmentAmountsPayload payload, UUID userId) {

        if (instituteId <= 0 || academicSessionId <= 0 || payload == null || payload.getStudentId() == null || userId == null) {
            logger.error("Invalid request instituteId {}, academicSessionId {}, payload {}, userId {}", instituteId, academicSessionId, payload, userId);
            return false;
        }

        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_ASSIGNMENT);
        if (CollectionUtils.isEmpty(payload.getFeeIdFeeHeadList())) {
            return true;
        }

        UUID studentId = payload.getStudentId();
        EntityFeeAssignment existingAssignment = getFeeAssignment(instituteId, FeeEntity.STUDENT, studentId.toString(), academicSessionId);

        Map<UUID, Map<Integer, Double>> existingFeeIdFeeHeadAmountsMap = EMapUtils.getMap(existingAssignment.getFeeIdFeeHeadDetailsList(), new EMapUtils.MapFunction<FeeIdFeeHeadDetails, UUID, Map<Integer, Double>>() {
            @Override
            public UUID getKey(FeeIdFeeHeadDetails entry) {
                return entry.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeId();
            }

            @Override
            public Map<Integer, Double> getValue(FeeIdFeeHeadDetails entry) {
                Map<Integer, Double> feeHeadMap = new HashMap<>();
                for (FeeHeadAmountDetails feeHeadAmountDetails : entry.getFeeHeadAmountDetailsList()) {
                    feeHeadMap.put(feeHeadAmountDetails.getFeeHeadId(), feeHeadAmountDetails.getAmount());
                }
                return feeHeadMap;
            }
        });

        Map<UUID, Map<Integer, Double>> updateFeeAmountMap = new HashMap<>();
        for (FeeIdFeeHead feeIdFeeHead : payload.getFeeIdFeeHeadList()) {
            UUID feeId = feeIdFeeHead.getFeeId();
            if (!existingFeeIdFeeHeadAmountsMap.containsKey(feeId)) {
                logger.error("instituteId {}, feeId {} not assigned to student {}", instituteId, feeId, studentId);
                return false;
            }
            Map<Integer, Double> existingFeeHeadMap = existingFeeIdFeeHeadAmountsMap.get(feeId);
            for (FeeHeadAmount feeHeadAmount : feeIdFeeHead.getFeeHeadAmountList()) {
                int feeHeadId = feeHeadAmount.getFeeHeadId();
                Double feeHeadAmountValue = feeHeadAmount.getAmount();
                if (feeHeadAmountValue == null || Double.compare(feeHeadAmountValue, 0d) <= 0) {
                    logger.error("instituteId {}, feeId {}, feeHeadId {}, feeHeadAmountValue {} cannot be null or <= 0 to student {}", instituteId, feeId, feeHeadId, feeHeadAmountValue, studentId);
                    return false;
                }
                Double existingFeeHeadAmountValue = existingFeeHeadMap.get(feeHeadId);
                if (existingFeeHeadAmountValue == null) {
                    logger.error("instituteId {}, feeId {}, feeHeadId {} not assigned to student {}", instituteId, feeId, feeHeadId, studentId);
                    return false;
                }

                if (NumberUtils.equal(existingFeeHeadAmountValue, feeHeadAmountValue)) {
                    logger.info("instituteId {}, feeId {}, feeHeadId {} not changed for student {}", instituteId, feeId, feeHeadId, studentId);
                    continue;
                }

                if (!updateFeeAmountMap.containsKey(feeId)) {
                    updateFeeAmountMap.put(feeId, new HashMap<>());
                }

                updateFeeAmountMap.get(feeId).put(feeHeadId, feeHeadAmountValue);
            }
        }
        return transactionTemplate.execute(new TransactionCallback<Boolean>() {
            @Override
            public Boolean doInTransaction(TransactionStatus transactionStatus) {
                for (Entry<UUID, Map<Integer, Double>> entry : updateFeeAmountMap.entrySet()) {
                    UUID feeId = entry.getKey();
                    List<FeeHeadAmount> feeHeadAmountList = new ArrayList<>();
                    for (Entry<Integer, Double> feeHeadEntry : entry.getValue().entrySet()) {
                        feeHeadAmountList.add(new FeeHeadAmount(feeHeadEntry.getKey(), feeHeadEntry.getValue()));
                    }
                    EntityFees entityFees = new EntityFees(studentId.toString(), FeeEntity.STUDENT, feeHeadAmountList);
                    final FeeAssignmentDetails feeAssignmentDetails = new FeeAssignmentDetails(instituteId, feeId,
                            Arrays.asList(entityFees));

                    logger.info("Updating fee assignment for instituteId {}, feeAssignmentDetails {} for student {}", instituteId, feeAssignmentDetails, studentId);
                    // PARTIAL_UPDATE is applicable as only amount is updated.
                    if (!assignFees(feeAssignmentDetails, Module.FEES,
                            FeeAssignmentState.PARTIAL_UPDATE, false, false, userId)) {
                        logger.error("Unable to assign fee for instituteId {}, feeAssignmentDetails {} for student {} ", instituteId, feeAssignmentDetails, studentId);
                        throw new EmbrateRunTimeException("Unable to assign fee");
                    }
                }
                return true;
            }
        });
    }


    /**
     * This method assigns fees or update earlier assignment on the basis of the
     * isUpdate flag
     *
     * @param feeAssignmentDetails
     * @return
     */
    // TODO : Add checks to prevent addition of lower entity assignment if
    // higher is already present. Also validate the entity id and entity name
    // combination exist in system
    // TODO: Fees should not be updated if its controlled via some module (eg
    // transport, hostel etc). Handle all scenarios for add/update/delete in
    // system feeheads. Currently it can be inconsistent in transport history
    // and actual assignment.
    public boolean assignFees(FeeAssignmentDetails feeAssignmentDetails, Module module,
                              FeeAssignmentState feeAssignmentState, boolean updateTransactions, boolean performInTransaction,
                              UUID userId) {

        userPermissionManager.verifyAuthorisation(feeAssignmentDetails.getInstituteId(), userId,
                AuthorisationRequiredAction.FEE_ASSIGNMENT);

        validateFeeAssignmentDetails(feeAssignmentDetails, feeAssignmentState);
        final FeeConfigurationResponse feeConfigurationResponse = feeConfigurationDao
                .getFeeConfiguration(feeAssignmentDetails.getInstituteId(), feeAssignmentDetails.getFeeId());
        if (feeConfigurationResponse == null) {
            logger.info("No fee exists with id " + feeAssignmentDetails.getFeeId());
            return false;
        }
        final int instituteId = feeAssignmentDetails.getInstituteId();
        final int academicSessionId = feeConfigurationResponse.getAcademicSession().getAcademicSessionId();
        final UUID feeId = feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeId();

        final Set<UUID> studentIds = new HashSet<>();
        for (final EntityFees entityFees : feeAssignmentDetails.getEntityFees()) {
            if (FeeEntity.CLASS.equals(entityFees.getFeeEntity())) {
                final List<Student> students = studentManager.getClassStudents(instituteId, academicSessionId,
                        UUID.fromString(entityFees.getEntityId()));
                for (final Student student : students) {
                    studentIds.add(student.getStudentId());
                }
            } else if (FeeEntity.STUDENT.equals(entityFees.getFeeEntity())) {
                final UUID studentId = UUID.fromString(entityFees.getEntityId());
                studentIds.add(studentId);
            } else {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
                        "Fee Entity not supported : " + entityFees.getFeeEntity()));
            }

        }


        // Add condition to check if any amount in fee head is decreased then proceed further else directly update the
        // assigned amount
        Map<UUID, Map<UUID, Map<Integer, Double>>> existingFeeAssignment = getStudentFeeAssignments(instituteId, academicSessionId, studentIds);
        boolean negativeAssignmentChange = false;
        Set<Integer> requiredFeeHeadIds = new HashSet<>();
        for (EntityFees entityFees : feeAssignmentDetails.getEntityFees()) {
            UUID studentId = UUID.fromString(entityFees.getEntityId());
            if (existingFeeAssignment.get(studentId) == null) {
                continue;
            }
            Map<Integer, Double> feeHeadMap = existingFeeAssignment.get(studentId).get(feeId);
            for (FeeHeadAmount feeHeadAmount : entityFees.getFeeHeadAmount()) {
                requiredFeeHeadIds.add(feeHeadAmount.getFeeHeadId());
                Double amount = feeHeadMap.get(feeHeadAmount.getFeeHeadId());
                if(amount == null || Double.compare(amount, 0) == 0){
                    continue;
                }
                if(feeHeadAmount.getAmount() == null ||  Double.compare(feeHeadAmount.getAmount(), 0) == 0 || Double.compare(amount, feeHeadAmount.getAmount()) > 0){
                    negativeAssignmentChange = true;
                    break;
                }
            }
        }

        if(!negativeAssignmentChange){
           logger.info("Amount change is positive only so no need to cancel the invoices for feeAssignmentDetails {}", feeAssignmentDetails);
           return executeFeeAssignment(feeAssignmentDetails, module, feeAssignmentState, performInTransaction, userId);
        }

        // Check for payment transactions if assigned fees is reduced
        final List<FeePaymentTransaction> existingFeePaymentTransactions = feePaymentTransactionDao
                .getFeePaymentTransactions(instituteId, feeId, studentIds);

        final List<FeePaymentTransaction> finalFeePaymentTransactions = getToBeCancelledFeePaymentTransactions(requiredFeeHeadIds, existingFeePaymentTransactions);

        if (CollectionUtils.isEmpty(finalFeePaymentTransactions)) {
            logger.info("No transactions to update for feeAssignmentDetails {}", feeAssignmentDetails);
            return executeFeeAssignment(feeAssignmentDetails, module, feeAssignmentState, performInTransaction, userId);
        }

        if (!updateTransactions) {
            logger.info("Fee cannot be assigned as payments were already made for given fees {}", feeAssignmentDetails);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
                    "Fee cannot be assigned as payments were already made for given fees"));
        }

        logger.info("Cancelling transactions and regenerating for finalFeePaymentTransactions {}", finalFeePaymentTransactions);

        final boolean success = cancelPaymentsAndAssignFees(feeAssignmentDetails, feeAssignmentState, instituteId,
                academicSessionId, feeId, studentIds, module, performInTransaction, finalFeePaymentTransactions, userId);

        if (success) {
            feeAuditLogWriter.addFeeAssignmentLog(feeAssignmentDetails, userId);
        }

        return success;
    }

    private static List<FeePaymentTransaction> getToBeCancelledFeePaymentTransactions(Set<Integer> requiredFeeHeadIdsForTransactionCancel, List<FeePaymentTransaction> existingFeePaymentTransactions) {
        final List<FeePaymentTransaction> finalFeePaymentTransactions = new ArrayList<>();
        if (requiredFeeHeadIdsForTransactionCancel == null) {
            finalFeePaymentTransactions.addAll(existingFeePaymentTransactions);
            return finalFeePaymentTransactions;
        }

        for (FeePaymentTransaction feePaymentTransaction : existingFeePaymentTransactions) {
            boolean cancelTransaction = isCancelTransaction(requiredFeeHeadIdsForTransactionCancel, feePaymentTransaction);
            if (cancelTransaction) {
                finalFeePaymentTransactions.add(feePaymentTransaction);
            }
        }
        return finalFeePaymentTransactions;
    }

    private static boolean isCancelTransaction(Set<Integer> requiredFeeHeadIdsForTransactionCancel, FeePaymentTransaction feePaymentTransaction) {
        boolean cancelTransaction = false;
        for (FeeIdFeeHeadTransaction feeIdFeeHeadTransaction : feePaymentTransaction.getFeeIdFeeHeadTransactions()) {
            for (FeeHeadTransactionAmounts feeHeadTransactionAmounts : feeIdFeeHeadTransaction.getFeeHeadTransactionAmounts()) {
                if (requiredFeeHeadIdsForTransactionCancel.contains(feeHeadTransactionAmounts.getFeeHeadId())&&
                        (Double.compare(feeHeadTransactionAmounts.getPaidAmount(), 0d) > 0
                                || Double.compare(feeHeadTransactionAmounts.getInstantDiscount(), 0d) > 0)) {
                    cancelTransaction = true;
                    break;
                }
            }
        }
        return cancelTransaction;
    }

    private boolean executeFeeAssignment(FeeAssignmentDetails feeAssignmentDetails, Module module, FeeAssignmentState feeAssignmentState, boolean performInTransaction, UUID userId) {
        if (performInTransaction) {
            final boolean success = feeConfigurationDao.assignFeesWithTransaction(feeAssignmentDetails, module,
                    feeAssignmentState);
            if (success) {
                feeAuditLogWriter.addFeeAssignmentLog(feeAssignmentDetails, userId);
            }
            return success;
        }

        final boolean success = feeConfigurationDao.assignFeesWithoutTransaction(feeAssignmentDetails, module,
                feeAssignmentState);
        if (success) {
            feeAuditLogWriter.addFeeAssignmentLog(feeAssignmentDetails, userId);
        }
        return success;
    }

    private boolean cancelPaymentsAndAssignFees(FeeAssignmentDetails feeAssignmentDetails,
                                                FeeAssignmentState feeAssignmentState, int instituteId, int academicSessionId, UUID feeId,
                                                Set<UUID> studentIds, Module module, boolean performInTransaction,
                                                List<FeePaymentTransaction> feePaymentTransactions, UUID userId) {

        if (performInTransaction) {
            return cancelPaymentsAndAssignFeesWithTransaction(feeAssignmentDetails, feeAssignmentState, instituteId,
                    academicSessionId, feeId, studentIds, module, feePaymentTransactions, userId);
        }
        return cancelPaymentsAndAssignFeesWithoutTransaction(feeAssignmentDetails, feeAssignmentState, instituteId,
                academicSessionId, feeId, studentIds, module, feePaymentTransactions, userId);
    }

    private boolean cancelPaymentsAndAssignFeesWithoutTransaction(FeeAssignmentDetails feeAssignmentDetails,
                                                                  FeeAssignmentState feeAssignmentState, int instituteId, int academicSessionId, UUID feeId,
                                                                  Set<UUID> studentIds, Module module, List<FeePaymentTransaction> feePaymentTransactions, UUID userId) {
        return cancelPaymentsAndAssignFees(feeAssignmentDetails, feeAssignmentState, instituteId, academicSessionId,
                feeId, studentIds, module, feePaymentTransactions, userId);
    }

    private boolean cancelPaymentsAndAssignFeesWithTransaction(FeeAssignmentDetails feeAssignmentDetails,
                                                               FeeAssignmentState feeAssignmentState, int instituteId, int academicSessionId, UUID feeId,
                                                               Set<UUID> studentIds, Module module, List<FeePaymentTransaction> feePaymentTransactions, UUID userId) {
        try {
            final Boolean updatedFeesAssignment = transactionTemplate.execute(new TransactionCallback<Boolean>() {

                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    return cancelPaymentsAndAssignFees(feeAssignmentDetails, feeAssignmentState, instituteId,
                            academicSessionId, feeId, studentIds, module, feePaymentTransactions, userId);
                }
            });
            return updatedFeesAssignment;
        } catch (final Exception e) {
            logger.error("Unable to Add Fee payment transaction");
            e.printStackTrace();
        }
        return false;
    }

    private Map<UUID, Map<UUID, Map<Integer, Double>>> getStudentFeeAssignments(int instituteId, int academicSessionId,
                                                                                Set<UUID> studentIds) {
        final Map<UUID, Map<UUID, Map<Integer, Double>>> feeAssignments = new HashMap<>();
        for (final UUID studentId : studentIds) {
            final EntityFeeAssignment entityFeeAssignment = getFeeAssignment(instituteId, FeeEntity.STUDENT,
                    studentId.toString(), academicSessionId);
            feeAssignments.put(studentId, getStudentFeeAssignmentMap(entityFeeAssignment));
        }
        return feeAssignments;
    }

    /**
     * This method does the handling of assigning fees for given entities. It
     * checks if the assigned amount has been changed for a feeid (either
     * addition/deletion/update and transaction is already made for that feeId
     * then it will cancel the fees and generates new one with updated assigned
     * amount. This method must be called by authorized modules and whenever
     * required only. Unnoticed usage of this function could lead to transaction
     * cancellation/ regeneration for large students
     *
     * @param feeAssignmentDetails
     * @param feeAssignmentState
     * @param instituteId
     * @param academicSessionId
     * @param feeId
     * @param studentIds
     * @param module
     * @param feePaymentTransactions
     * @return
     */
    private boolean cancelPaymentsAndAssignFees(FeeAssignmentDetails feeAssignmentDetails,
                                                FeeAssignmentState feeAssignmentState, int instituteId, int academicSessionId, UUID feeId,
                                                Set<UUID> studentIds, Module module, List<FeePaymentTransaction> feePaymentTransactions, UUID userId) {

        logger.info("Checking fee cancellation status and assigning fees for feeId {} ", feeId);

        if (CollectionUtils.isEmpty(feePaymentTransactions)) {
            throw new RuntimeException("No payment made, fees can be simply upgraded.");
        }

        final Map<UUID, Map<UUID, Map<Integer, Double>>> oldFeeAssignments = getStudentFeeAssignments(instituteId,
                academicSessionId, studentIds);

        if (!feeConfigurationDao.assignFeesWithoutTransaction(feeAssignmentDetails, module, feeAssignmentState)) {
            throw new RuntimeException("Could not update the assignment");
        }

        final Map<UUID, Map<UUID, Map<Integer, Double>>> newFeeAssignments = getStudentFeeAssignments(instituteId,
                academicSessionId, studentIds);
        //
        // final List<FeePaymentTransaction> feePaymentTransactions =
        // feePaymentTransactionDao
        // .getFeePaymentTransactions(instituteId, feeId, studentIds);
        //
        final Map<UUID, List<FeePaymentTransaction>> studentFeePaymentTransactionMap = getStudentWiseTransactions(
                feePaymentTransactions);

        for (final Entry<UUID, List<FeePaymentTransaction>> studentFeePaymentTransactionEntry : studentFeePaymentTransactionMap
                .entrySet()) {
            final UUID studentId = studentFeePaymentTransactionEntry.getKey();
            final List<FeePaymentTransaction> studentFeePaymentTransactions = studentFeePaymentTransactionEntry
                    .getValue();
            if (CollectionUtils.isEmpty(studentFeePaymentTransactions)) {
                logger.info("No transaction present, skipping for student " + studentId);
                continue;
            }

            if (!isFeesAssignmentChanged(oldFeeAssignments.get(studentId), newFeeAssignments.get(studentId), feeId)) {
                logger.info("No fees changed, skipping for student " + studentId);
                continue;
            }

            Collections.sort(studentFeePaymentTransactions);
            final List<FeePaymentTransaction> cancelledFeePaymentTransactions = new ArrayList<>();
            /**
             * All the transaction needs to be cancelled which contains the
             * changed fee id assignment structure irrespective of fee head
             * amount under transaction changed or not. Since we freeze the
             * assignment structure at first payment so cancellation of all
             * transaction of that fee id is necessarys
             */
            for (final FeePaymentTransaction feePaymentTransaction : studentFeePaymentTransactions) {
                if (CollectionUtils.isEmpty(feePaymentTransaction.getFeeIdFeeHeadTransactions())) {
                    continue;
                }
                /**
                 * marking cancel remark as null for this case.
                 */
                FeeCancelledPayload feeCancelledPayload = new FeeCancelledPayload(instituteId,
                        feePaymentTransaction.getFeePaymentTransactionMetaData().getTransactionId(), userId, DateUtils.now(), null);
                if (!feePaymentTransactionDao.cancelFeePaymentTransactionWithoutTransaction(feeCancelledPayload, userId)) {
                    throw new RuntimeException("Could not cancel transaction "
                            + feePaymentTransaction.getFeePaymentTransactionMetaData().getTransactionId());
                }
                cancelledFeePaymentTransactions.add(feePaymentTransaction);
            }

            if (CollectionUtils.isEmpty(cancelledFeePaymentTransactions)) {
                logger.info("No transactions to cancel for student " + studentId);
                continue;
            }

            final StudentFeesDetails studentFeesDetails = feeCalculator.getPaymentDetails(instituteId,
                    academicSessionId, studentId);
            final Map<Integer, Double> feeHeadPaymentDetailsMap = new HashMap<>();
            for (final FeePaymentDetails feePaymentDetails : studentFeesDetails.getFeePaymentDetailsList()) {
                if (!feePaymentDetails.getFeeConfigurationBasicInfo().getFeeId().equals(feeId)) {
                    continue;
                }
                if (feePaymentDetails.getFeePaymentStatus() != FeePaymentStatus.UNPAID) {
                    throw new RuntimeException(
                            "Fees " + feeId.toString() + " is final not in unpaid state. Something wrong.");
                }
                for (final FeeHeadPaymentDetails feeHeadPaymentDetails : feePaymentDetails.getFeeHeadPaymentDetails()) {
                    feeHeadPaymentDetailsMap.put(feeHeadPaymentDetails.getFeeHeadConfiguration().getFeeHeadId(),
                            feeHeadPaymentDetails.getBalanceAmount());
                }
                break;
            }

            if (feeHeadPaymentDetailsMap.isEmpty()) {
                logger.info(
                        "Fee {} is not assigned anymore to student {}. New receipt should be generated with just credit wallet statement.",
                        feeId, studentId);
            }

            for (final FeePaymentTransaction cancelledFeePaymentTransaction : cancelledFeePaymentTransactions) {
                final FeePaymentResponse newFeePaymentResponse = regenerateFeeTransaction(
                        cancelledFeePaymentTransaction, feeId, academicSessionId, feeHeadPaymentDetailsMap, userId,
                        module);
                if (newFeePaymentResponse == null) {
                    throw new RuntimeException("Could not generate new transaction.");
                }
            }
        }
        return true;
    }

    private boolean isFeesAssignmentChanged(Map<UUID, Map<Integer, Double>> oldAssignment,
                                            Map<UUID, Map<Integer, Double>> newAssignment, UUID feeId) {

        if (oldAssignment.containsKey(feeId) && newAssignment.containsKey(feeId)) {
            if (!oldAssignment.get(feeId).keySet().containsAll(newAssignment.get(feeId).keySet())
                    || !newAssignment.get(feeId).keySet().containsAll(oldAssignment.get(feeId).keySet())) {
                return true;
            }
            for (final Entry<Integer, Double> feeHeadEntry : oldAssignment.get(feeId).entrySet()) {
                final Integer feeHeadId = feeHeadEntry.getKey();
                if (Double.compare(newAssignment.get(feeId).get(feeHeadId), feeHeadEntry.getValue()) != 0) {
                    return true;
                }
            }
            return false;
        }
        return true;

    }

    private Map<UUID, Map<Integer, Double>> getStudentFeeAssignmentMap(EntityFeeAssignment entityFeeAssignment) {
        final Map<UUID, Map<Integer, Double>> feeIdFeeHeadAssignedAmountMap = new HashMap<>();
        if (entityFeeAssignment == null) {
            return feeIdFeeHeadAssignedAmountMap;
        }
        for (final FeeIdFeeHeadDetails feeIdFeeHeadDetails : entityFeeAssignment.getFeeIdFeeHeadDetailsList()) {
            final UUID feeId = feeIdFeeHeadDetails.getFeeConfigurationResponse().getFeeConfigurationBasicInfo()
                    .getFeeId();
            for (final FeeHeadAmountDetails feeHeadAmountDetails : feeIdFeeHeadDetails.getFeeHeadAmountDetailsList()) {
                if (!feeIdFeeHeadAssignedAmountMap.containsKey(feeId)) {
                    feeIdFeeHeadAssignedAmountMap.put(feeId, new HashMap<>());
                }
                feeIdFeeHeadAssignedAmountMap.get(feeId).put(
                        feeHeadAmountDetails.getFeeHeadConfiguration().getFeeHeadId(),
                        feeHeadAmountDetails.getAmount());
            }
        }

        return feeIdFeeHeadAssignedAmountMap;
    }

    private Map<UUID, List<FeePaymentTransaction>> getStudentWiseTransactions(
            List<FeePaymentTransaction> feePaymentTransactions) {
        final Map<UUID, List<FeePaymentTransaction>> studentFeePaymentTransactionMap = new HashMap<>();
        for (final FeePaymentTransaction feePaymentTransaction : feePaymentTransactions) {
            final UUID studentId = feePaymentTransaction.getFeePaymentTransactionMetaData().getStudentId();
            if (!studentFeePaymentTransactionMap.containsKey(studentId)) {
                studentFeePaymentTransactionMap.put(studentId, new ArrayList<>());
            }
            studentFeePaymentTransactionMap.get(studentId).add(feePaymentTransaction);
        }
        return studentFeePaymentTransactionMap;
    }

    /**
     * Should be used within transaction only
     *
     * @param feeId
     * @param academicSessionId
     * @param feeHeadPaymentDetailsMap
     * @return
     */
    private FeePaymentResponse regenerateFeeTransaction(FeePaymentTransaction cancelledFeePaymentTransaction,
                                                        UUID feeId, int academicSessionId, final Map<Integer, Double> feeHeadPaymentDetailsMap, UUID userId,
                                                        Module module) {
        final FeePaymentPayload feePaymentPayload = new FeePaymentPayload();
        final int instituteId = cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getInstituteId();
        final UUID studentId = cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getStudentId();

        logger.info("Regenerating fees for " + studentId + " , fees " + feeId);
        final List<FeeIdFeeHeadTransaction> feeIdFeeHeadTransactionList = new ArrayList<>();

        double overPaidAmount = 0d;
        for (final FeeIdFeeHeadTransaction feeIdFeeHeadTransaction : cancelledFeePaymentTransaction
                .getFeeIdFeeHeadTransactions()) {
            if (!feeIdFeeHeadTransaction.getFeeId().equals(feeId)) {
                feeIdFeeHeadTransactionList.add(feeIdFeeHeadTransaction);
                logger.info("Skipped fees for " + feeIdFeeHeadTransaction.getFeeId());
                continue;
            }
            logger.info("Processing fees for " + feeIdFeeHeadTransaction.getFeeId());

            final List<FeeHeadTransactionAmounts> updatedFeeHeadTransactionAmounts = new ArrayList<>();
            for (final FeeHeadTransactionAmounts feeHeadTransactionAmounts : feeIdFeeHeadTransaction
                    .getFeeHeadTransactionAmounts()) {
                final Integer feeHeadId = feeHeadTransactionAmounts.getFeeHeadId();
                if (!feeHeadPaymentDetailsMap.containsKey(feeHeadId)) {
                    overPaidAmount += feeHeadTransactionAmounts.getPaidAmount();
                    continue;
                }
                final double latestPaybleAmount = feeHeadPaymentDetailsMap.get(feeHeadId);
                final double paidAmount = feeHeadTransactionAmounts.getPaidAmount();
                final double instantDiscountAmount = feeHeadTransactionAmounts.getInstantDiscount();
                final double fineAmount = feeHeadTransactionAmounts.getFineAmount();
                if (latestPaybleAmount >= paidAmount + instantDiscountAmount) {
                    updatedFeeHeadTransactionAmounts.add(feeHeadTransactionAmounts);
                    feeHeadPaymentDetailsMap.put(feeHeadId, latestPaybleAmount - paidAmount - instantDiscountAmount);
                } else if (latestPaybleAmount <= instantDiscountAmount) {
                    updatedFeeHeadTransactionAmounts
                            .add(new FeeHeadTransactionAmounts(feeHeadId, 0d, latestPaybleAmount, fineAmount));
                    feeHeadPaymentDetailsMap.put(feeHeadId, 0d);
                    overPaidAmount += paidAmount;
                } else {
                    updatedFeeHeadTransactionAmounts.add(new FeeHeadTransactionAmounts(feeHeadId,
                            latestPaybleAmount - instantDiscountAmount, instantDiscountAmount, fineAmount));
                    feeHeadPaymentDetailsMap.put(feeHeadId, 0d);
                    overPaidAmount += (paidAmount - latestPaybleAmount + instantDiscountAmount);
                }
            }
            if (!CollectionUtils.isEmpty(updatedFeeHeadTransactionAmounts)) {
                final FeeIdFeeHeadTransaction updatedFeeIdFeeHeadTransaction = new FeeIdFeeHeadTransaction(feeId,
                        updatedFeeHeadTransactionAmounts);
                feeIdFeeHeadTransactionList.add(updatedFeeIdFeeHeadTransaction);
            }

        }
        feePaymentPayload.setFeeIdFeeHeadTransactionList(feeIdFeeHeadTransactionList);


        final FeePaymentTransactionMetaData feePaymentTransactionMetaData = new FeePaymentTransactionMetaData(
                instituteId, studentId,
                cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getTransactionMode(),
                cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getChequeNumber(),
                cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getTransactionReference(),
                cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getTransactionDate(),
                cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getDescription(),
                cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getDebitWalletAmount(),
                cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getCreditWalletAmount()
                        + overPaidAmount,
                academicSessionId,
                cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getRemark(),
                cancelledFeePaymentTransaction.getFeePaymentTransactionMetaData().getCancelRemarks());
        feePaymentPayload.setFeePaymentTransactionMetaData(feePaymentTransactionMetaData);

        return feePaymentManager.addFeePaymentWithoutTransaction(feePaymentPayload, userId, false, module);
    }

    public boolean deleteAssignedFees(int instituteId, UUID feeId, FeeEntity feeEntity, String entityId, Module module,
                                      UUID userId) {
        return deleteAssignedFees(instituteId, feeId, Arrays.asList(new FeeEntityData(feeEntity, entityId)), module, userId);
    }

    /**
     * Only assignments which are authorized by a module will be deleted, other
     * will remain as it is.
     *
     * @param instituteId
     * @param feeId
     * @param feeEntityDataList
     * @param module
     * @return
     */
    public boolean deleteAssignedFees(int instituteId, UUID feeId, List<FeeEntityData> feeEntityDataList, Module module,
                                      UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_ASSIGNMENT);
        logger.info("Deleting for feeId " + feeId);
        final FeeConfigurationResponse feeConfigurationResponse = feeConfigurationDao.getFeeConfiguration(instituteId,
                feeId);
        if (feeConfigurationResponse == null) {
            return false;
        }
        final Set<UUID> studentIds = new HashSet<>();
        for(FeeEntityData feeEntityData : feeEntityDataList) {
            FeeEntity feeEntity = feeEntityData.getFeeEntity();
            String entityId = feeEntityData.getEntityId();
            if (FeeEntity.CLASS.equals(feeEntity)) {
                final List<Student> students = studentManager.getClassStudents(instituteId,
                        feeConfigurationResponse.getAcademicSession().getAcademicSessionId(), UUID.fromString(entityId));
                for (final Student student : students) {
                    studentIds.add(student.getStudentId());
                }
            } else if (FeeEntity.STUDENT.equals(feeEntity)) {
                final UUID studentId = UUID.fromString(entityId);
                studentIds.add(studentId);
            } else {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
                        "Fee Entity not supported : " + feeEntity.name()));
            }
        }

        // TODO : Handle transaction cancellation in delete scenarios
        if (feePaymentTransactionDao.checkIfFeesPaidByAnyStudent(instituteId, feeId, studentIds)) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
                    "Cannot Delete fee assignment as existing fees amount is already paid for some students"));
        }
        return feeConfigurationDao.deleteAuthorizedFeeAssignment(instituteId, feeId, feeEntityDataList, module);
    }

    public BulkAssignmentFeesStatus getBulkFeeAssignmentStatus(int instituteId, int academicSessionId,
                                                               List<UUID> students, UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_ASSIGNMENT);
        if (CollectionUtils.isEmpty(students)) {
            return null;
        }
        final List<Student> studentList = studentManager.getStudentByAcademicSessionStudentIds(instituteId,
                academicSessionId, students);
        if (CollectionUtils.isEmpty(studentList)) {
            return null;
        }
        final Map<UUID, FeeEntityData> feeEntityDataMap = new HashMap<>();
        for (final Student student : studentList) {
            final UUID standardId = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
            feeEntityDataMap.put(standardId, new FeeEntityData(FeeEntity.CLASS, standardId.toString()));
        }

        final Map<String, EntityFeeAssignment> entityFeeAssignments = feeConfigurationDao.getFeeAssignment(instituteId,
                new ArrayList<>(feeEntityDataMap.values()), academicSessionId);

        final Map<UUID, FeeIdFeeHeadDetails> feeIdFeeHeadDetailsStatus = new HashMap<>();
        for (final Entry<String, EntityFeeAssignment> entityFeeAssignmentEntry : entityFeeAssignments.entrySet()) {
            for (final FeeIdFeeHeadDetails feeIdFeeHeadDetails : entityFeeAssignmentEntry.getValue()
                    .getFeeIdFeeHeadDetailsList()) {
                final UUID feeId = feeIdFeeHeadDetails.getFeeConfigurationResponse().getFeeConfigurationBasicInfo()
                        .getFeeId();
                if (!feeIdFeeHeadDetailsStatus.containsKey(feeId)) {
                    feeIdFeeHeadDetailsStatus.put(feeId, feeIdFeeHeadDetails);
                } else {
                    feeIdFeeHeadDetailsStatus.get(feeId).getFeeHeadAmountDetailsList()
                            .addAll(feeIdFeeHeadDetails.getFeeHeadAmountDetailsList());
                }
            }
        }

        final List<FeeIdFeeHeadDetails> feeIdFeeHeadDetailsList = new ArrayList<>();

        final List<AuthorizedFeeAssignment> authorizedFeeAssignments = getAuthorizedFeeAssignment(instituteId,
                academicSessionId);
        for (final AuthorizedFeeAssignment authorizedFeeAssignment : authorizedFeeAssignments) {
            final UUID feeId = authorizedFeeAssignment.getFeeConfigurationResponse().getFeeConfigurationBasicInfo()
                    .getFeeId();

            final List<FeeHeadAmountDetails> feeHeadAmountDetailsList = new ArrayList<>();
            for (final AuthorizedFeeHeadAssignment authorizedFeeHeadAssignment : authorizedFeeAssignment
                    .getAuthorizedFeeHeadAssignments()) {
                feeHeadAmountDetailsList.add(new FeeHeadAmountDetails(
                        authorizedFeeHeadAssignment.getFeeHeadConfigurationResponse().getFeeHeadConfiguration(),
                        FeeEntity.STUDENT, authorizedFeeHeadAssignment.getModules()));
            }
            if (feeIdFeeHeadDetailsStatus.containsKey(feeId)) {
                final FeeIdFeeHeadDetails feeIdFeeHeadDetails = feeIdFeeHeadDetailsStatus.get(feeId);
                final Set<FeeHeadAmountDetails> feeHeadAmountDetailSet = new HashSet<>();
                for (final FeeHeadAmountDetails feeHeadAmountDetails : feeIdFeeHeadDetails
                        .getFeeHeadAmountDetailsList()) {
                    feeHeadAmountDetails.setFeeEntity(FeeEntity.CLASS);
                    feeHeadAmountDetailSet.add(feeHeadAmountDetails);
                }
                for (final FeeHeadAmountDetails feeHeadAmountDetails : feeHeadAmountDetailsList) {
                    feeHeadAmountDetailSet.add(feeHeadAmountDetails);
                }
                feeIdFeeHeadDetailsList
                        .add(new FeeIdFeeHeadDetails(authorizedFeeAssignment.getFeeConfigurationResponse(),
                                new ArrayList<>(feeHeadAmountDetailSet)));
            } else {
                feeIdFeeHeadDetailsList.add(new FeeIdFeeHeadDetails(
                        authorizedFeeAssignment.getFeeConfigurationResponse(), feeHeadAmountDetailsList));
            }
        }
        return new BulkAssignmentFeesStatus(studentList, feeIdFeeHeadDetailsList);
    }

    public boolean addFeeAssignmentPermission(List<FeeAssignmentPermission> feeAssignmentPermissions, int instituteId) {
        return feeConfigurationDao.addFeeAssignmentPermission(feeAssignmentPermissions, instituteId);
    }

    public boolean deleteFeeAssignmentPermission(int instituteId, int academicSessionId, Module module) {
        return feeConfigurationDao.deleteFeeAssignmentPermission(instituteId, academicSessionId, module);
    }

    public boolean deleteDefaultFeeAssignmentStructure(int instituteId, int academicSessionId, UUID structureId,
                                                       UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_STRUCTURE);
        return feeConfigurationDao.deleteDefaultFeeAssignmentStructure(instituteId, academicSessionId, structureId);
    }

    public boolean updateDefaultFeeAssignmentStructure(int instituteId, int academicSessionId, UUID structureId,
                                                       List<DefaultEntityFeeAssignmentStructurePayload> defaultEntityFeeAssignmentStructurePayloads, UUID userId, boolean skipAuth) {
        if (!skipAuth) {
            userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_STRUCTURE);
        }
        if (!validDefaultFeeStructure(instituteId, academicSessionId, defaultEntityFeeAssignmentStructurePayloads,
                true)) {
            return false;
        }
        return feeConfigurationDao.updateDefaultFeeAssignmentStructure(instituteId, academicSessionId, structureId,
                defaultEntityFeeAssignmentStructurePayloads);
    }

    public List<FeePaymentDetails> getPaidFeeDetailsOfAFeeHead(int instituteId, UUID studentId, int academicSessionId,
                                                               int feeHeadId) {

        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
        }

        if (studentId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student id."));
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid academic session id."));
        }

        if (feeHeadId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION, "Invalid fee head id."));
        }

        return feePaymentTransactionDao.getPaidFeeDetailsOfAFeeHead(instituteId, studentId, academicSessionId, feeHeadId);
    }

    public boolean deleteAssignedFeesByFeeIds(int instituteId, List<UUID> feeIds, UUID entityId, Integer feeHeadId) {
        return feeConfigurationDao.deleteAssignedFeesByFeeIds(instituteId, feeIds, entityId,
                feeHeadId);
    }

    public boolean deleteAssignedFeesByFeeHead(int instituteId, int academicSessionId, final List<FeeEntityData> feeEntityDataList,
                                               Set<Integer> feeHeadIds) {
        return feeConfigurationDao.deleteAssignedFeesByFeeHead(instituteId, academicSessionId, feeEntityDataList, feeHeadIds);
    }

    public List<FeeStructureMetaData> getFeeStructureMetaDataForStandard(int instituteId, Integer academicSessionId,
                                                                         UUID standardId, FeeStructureType feeStructureType) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
        }
        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
        }
        if (standardId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Invalid standard id."));
        }
        return feeConfigurationDao.getFeeStructureMetaDataForStandard(instituteId, academicSessionId, standardId, feeStructureType);
    }

    public boolean deleteStudentAssignedFeesByEntityId(int instituteId, int academicSessionId, UUID studentId) {
        return feeConfigurationDao.deleteStudentAssignedFeesByEntityId(instituteId, academicSessionId, studentId);
    }

    public List<String> checkStudentFeeStructureChangeEligibility(int instituteId, int academicSessionId,
                                                                  UUID studentId) {

        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute."));
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
        }

        if (studentId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student id."));
        }

        List<String> eligibleList = new ArrayList<String>();
        Set<UUID> studentIds = new HashSet<UUID>();
        studentIds.add(studentId);
        final Map<UUID, Map<Integer, Double>> feeAssignments = getStudentFeeAssignments(instituteId,
                academicSessionId, studentIds).get(studentId);
        Set<UUID> feeIds = new HashSet<UUID>();
        for (Entry<UUID, Map<Integer, Double>> entry : feeAssignments.entrySet()) {
            feeIds.add(entry.getKey());
        }

        if (feePaymentTransactionDao.checkIfFeesPaidByAnyStudent(instituteId, feeIds, studentIds)) {
            eligibleList.add("Student already have paid fees for current session. Please cancel that for further actions.");
        }
        return CollectionUtils.isEmpty(eligibleList) ? null : eligibleList;
    }

    public boolean studentFeeStructureChange(int instituteId, int academicSessionId, UUID studentId, List<UUID> feeStructureIdsList,
                                             UUID userId) {

        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_STRUCTURE_CHANGE);

        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute."));
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session."));
        }

        if (CollectionUtils.isEmpty(feeStructureIdsList)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Please select a fee structure to assign."));
        }

        List<ErrorDetails> errorDetailsList = new ArrayList<ErrorDetails>();

        if (!CollectionUtils.isEmpty(checkStudentFeeStructureChangeEligibility(
                instituteId, academicSessionId, studentId))) {
            ErrorDetails errorDetails = new ErrorDetails(instituteId, academicSessionId, Entity.USER,
                    String.valueOf(studentId), ActionType.CHECK_FEE_STRUCTURE_CHANGE_ELIGIBILITY,
                    "Student not eligible for fee structure change.");
            errorDetailsList.add(errorDetails);
            logger.error("Student {} of institute {} not eligible for class change", studentId, instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Cannot change fee structure. Please contact technical team for futher assistance"));
        }

        final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
            @Override
            public Boolean doInTransaction(TransactionStatus status) {

                //delete fee assignment
                boolean isSuccess = deleteStudentFeeAssignment(instituteId, academicSessionId, studentId, errorDetailsList);

                if (!isSuccess) {
                    logger.error("Error while deleting fee structure of studentId {} for institute {}",
                            studentId, instituteId);
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_STRUCTURE_DETAILS,
                            "Error while deleting fee structure. Please try again in sometime."));
                }

                //add fee structure
                if (!CollectionUtils.isEmpty(feeStructureIdsList)) {
                    Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId);
                    assignFeeStructure(instituteId, academicSessionId, studentId,
                            student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId(),
                            feeStructureIdsList, errorDetailsList, userId);
                }
                return isSuccess;
            }
        });

        String errorStatement = "Error occurred while assigning fee structure. "
                + "Please contact technical team for further assistance.";

        return logError(errorDetailsList, errorStatement, ApplicationErrorCode.INVALID_FEE_STRUCTURE_DETAILS);
    }

    private boolean logError(List<ErrorDetails> errorDetailsList, String errorStatement, ApplicationErrorCode applicationErrorCode) {
        if (!CollectionUtils.isEmpty(errorDetailsList)) {
            logger.error("Below is the error logs. {} ", errorDetailsList);
            throw new ApplicationException(new ErrorResponse(applicationErrorCode,
                    errorStatement));
        }
        logger.info("Action done successfully!");
        return true;

    }

    public boolean deleteStudentFeeAssignment(int instituteId, int academicSessionId, UUID studentId,
                                              List<ErrorDetails> errorDetailsList) {

        if (!deleteStudentAssignedFeesByEntityId(instituteId, academicSessionId, studentId)) {
            ErrorDetails errorDetails = new ErrorDetails(instituteId, academicSessionId, Entity.INSTITUTE,
                    String.valueOf(instituteId), ActionType.DELETE_FEE_ASSIGNMENT,
                    "Unable to delete fee assignment.");
            errorDetailsList.add(errorDetails);

            logger.error("Unable to delete fee assignment of institute {}, nextAcademicSessionId {} for student {} ",
                    instituteId, academicSessionId, studentId);

            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CLASS_CHANGE_DETAILS,
                    "Error while deleting fee assignment. Please try again in sometime."));
        }

        logger.info("Fee assignment deleted successfully of institute {}, nextAcademicSessionId {} for student {} ",
                instituteId, academicSessionId, studentId);

        return true;
    }

    public boolean assignFeeStructure(int instituteId, int academicSessionId, UUID studentId, UUID standardId,
                                      List<UUID> feeStructureIdsList, List<ErrorDetails> errorDetailsList, UUID userId) {

        if (!assignFeeStructure(instituteId, academicSessionId, studentId, standardId, feeStructureIdsList,
                Arrays.asList(FeeStructureType.ENROLLMENT, FeeStructureType.REGISTRATION), false, userId, errorDetailsList)) {

            logger.error("Unable to assign fee structure of institute {}, nextAcademicSessionId {} for student {} ",
                    instituteId, academicSessionId,
                    studentId);

            return false;
        }

        logger.info("Fee Structure assigned successfully of institute {}, nextAcademicSessionId {} for student {} ",
                instituteId, academicSessionId,
                studentId);

        return true;

    }

    public boolean assignFeeStructure(int instituteId, int academicSessionId, UUID studentId, UUID standardId,
                                      List<UUID> feeStructureIds, List<FeeStructureType> feeStructureTypes,
                                      boolean onlyPendingEnrollmentAllowedFees, UUID userId, List<ErrorDetails> errorDetailsList) {
        try {
            final List<ResolvedDefaultEntityFeeAssignmentStructure> resolvedDefaultEntityFeeAssignmentStructures =
                    getDefaultFeeAssignmentStructure(instituteId, academicSessionId, standardId, feeStructureTypes,
                            onlyPendingEnrollmentAllowedFees);
            if (CollectionUtils.isEmpty(resolvedDefaultEntityFeeAssignmentStructures)) {
                logger.info(
                        "No fee structure found for instituteId {}, academicSessionId {}, standardId {} , feeStructureTypes {}, onlyPendingEnrollmentAllowedFees {}",
                        instituteId, academicSessionId, standardId, feeStructureTypes,
                        onlyPendingEnrollmentAllowedFees);
                ErrorDetails errorDetails = new ErrorDetails(instituteId, academicSessionId, Entity.USER, studentId.toString(),
                        ActionType.ASSIGN_FEE_STRUCTURE, "No fee structure found with given fee structure ids.");
                errorDetailsList.add(errorDetails);
                return false;
            }
            boolean success = true;
            for (final ResolvedDefaultEntityFeeAssignmentStructure resolvedDefaultEntityFeeAssignmentStructure : resolvedDefaultEntityFeeAssignmentStructures) {
                if (!feeStructureIds.contains(resolvedDefaultEntityFeeAssignmentStructure.getStructureId())) {
                    continue;
                }
                for (final FeeIdFeeHeadDetails feeIdFeeHeadDetails : resolvedDefaultEntityFeeAssignmentStructure
                        .getFeeIdFeeHeadDetailsList()) {
                    final UUID feeId = feeIdFeeHeadDetails.getFeeConfigurationResponse().getFeeConfigurationBasicInfo()
                            .getFeeId();
                    final List<EntityFees> entityFees = new ArrayList<>();
                    final List<FeeHeadAmount> feeHeadAmounts = new ArrayList<>();
                    for (final FeeHeadAmountDetails feeHeadAmountDetails : feeIdFeeHeadDetails
                            .getFeeHeadAmountDetailsList()) {
                        feeHeadAmounts.add(new FeeHeadAmount(feeHeadAmountDetails.getFeeHeadId(),
                                feeHeadAmountDetails.getAmount()));
                    }
                    entityFees.add(new EntityFees(studentId.toString(), FeeEntity.STUDENT, feeHeadAmounts));
                    final FeeAssignmentDetails feeAssignmentDetails = new FeeAssignmentDetails(instituteId, feeId,
                            entityFees);
                    final boolean status = assignFees(feeAssignmentDetails, Module.FEES,
                            FeeAssignmentState.NEW, false, true, userId);
                    if (!status) {
                        ErrorDetails errorDetails = new ErrorDetails(instituteId, academicSessionId, Entity.USER, studentId.toString(),
                                ActionType.ASSIGN_FEE_STRUCTURE, "Unable to assign fee structure.");
                        errorDetailsList.add(errorDetails);
                        logger.error("Unable to assign fees for student {}, {}", studentId, feeAssignmentDetails);
                    }
                    success &= status;
                }
            }
            logger.info("Successfully assigned fee structures for student {}, session {}", studentId,
                    academicSessionId);
            return success;
        } catch (final Exception e) {
            ErrorDetails errorDetails = new ErrorDetails(instituteId, academicSessionId, Entity.USER, studentId.toString(),
                    ActionType.ASSIGN_FEE_STRUCTURE, e.toString());
            errorDetailsList.add(errorDetails);
            logger.error("Error while assigning fees for student {}", studentId, e);
        }
        return false;
    }

    public boolean assignInstituteStudentFeeStructure(int instituteId, int academicSessionId,
                                                      List<UUID> feeStructureIds, UUID userId, boolean update) {
        try {
            List<Student> studentList = studentManager.getStudentsInAcademicSession(instituteId, academicSessionId);
            int totalCount = 0;
            int failedCount = 0;
            int successCount = 0;
            for (Student student : studentList) {
                if (student == null || student.getStudentBasicInfo().isRte()) {
                    continue;
                }
                UUID studentId = student.getStudentId();
                UUID standardId = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
                boolean status = true;
                if (update) {
                    status = assignFeeStructure(instituteId, academicSessionId, studentId, standardId,
                            feeStructureIds, Arrays.asList(FeeStructureType.REGISTRATION, FeeStructureType.ENROLLMENT),
                            false, userId, new ArrayList<>());
                }
                if (status) {
                    successCount++;
                    logger.info("Successfully assigned to student {}", studentId);
                } else {
                    failedCount++;
                    logger.error("Error while assigning to student {}", studentId);
                }
                totalCount++;
            }
            logger.info("Total - {}, Success - {}, Failed - {}", totalCount, successCount, failedCount);
            return totalCount == successCount;
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.info("Error while assigning fee structure", ex);
            return false;
        }
    }

    public Map<UUID, Map<String, EntityFeeAssignment>> getFeeAssignmentOfStandard(int instituteId, Set<UUID> standardIds,
                                                                                  Integer academicSessionId, Set<Integer> feeHeadIds, Set<StudentStatus> statusSet) {
        return feeConfigurationDao.getFeeAssignmentOfStandard(instituteId, standardIds, academicSessionId, feeHeadIds, statusSet);
    }

    public List<String> readableBulkFeeStructureAssignment(int instituteId, int academicSessionId, UUID userId,
                    BulkStudentFeeStructureAssignmentReadablePayload bulkStudentFeeStructureAssignmentReadablePayload) {

        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute."));
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
        }

        if (userId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid user id."));
        }

        if (bulkStudentFeeStructureAssignmentReadablePayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid payload."));
        }

        if (CollectionUtils.isEmpty(bulkStudentFeeStructureAssignmentReadablePayload.getAdmissionNumberSet())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select at least one student to assign fee structure."));
        }

        if (CollectionUtils.isEmpty(bulkStudentFeeStructureAssignmentReadablePayload.getFeesStructureNameSet())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select at least one fees structure to assign fee structure."));
        }

        /**
         * fetch student ids by admission number
         */
        List<Student> studentList = studentManager.getStudentByAcademicSessionAdmissionNumber(
                instituteId, academicSessionId, bulkStudentFeeStructureAssignmentReadablePayload.getAdmissionNumberSet());
        Map<UUID, Student> studentMap = studentManager.getStudentMap(studentList);

        /**
         * fetch fee structure id by structure names
         */
        final List<DefaultEntityFeeAssignmentStructure> defaultEntityFeeAssignmentStructureList = getDefaultFeeAssignmentStructure(instituteId, academicSessionId);
        Set<UUID> feeStructureIdSet = new HashSet<>();
        for(DefaultEntityFeeAssignmentStructure defaultEntityFeeAssignmentStructure : defaultEntityFeeAssignmentStructureList) {
            if(bulkStudentFeeStructureAssignmentReadablePayload.getFeesStructureNameSet().contains(defaultEntityFeeAssignmentStructure.getStructureName())) {
                feeStructureIdSet.add(defaultEntityFeeAssignmentStructure.getStructureId());
            }
        }

        return bulkFeeStructureAssignment(instituteId, academicSessionId, userId, new BulkStudentFeeStructureAssignmentPayload(studentMap.keySet(), feeStructureIdSet));

    }

    public List<String> bulkFeeStructureAssignment(int instituteId, int academicSessionId, UUID userId,
                                                   BulkStudentFeeStructureAssignmentPayload bulkStudentFeeStructureAssignmentPayload) {

        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute."));
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
        }

        if (userId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid user id."));
        }

        if (bulkStudentFeeStructureAssignmentPayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid payload."));
        }

        if (CollectionUtils.isEmpty(bulkStudentFeeStructureAssignmentPayload.getStudentIds())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select at least one student to assign fee structure."));
        }

        if (CollectionUtils.isEmpty(bulkStudentFeeStructureAssignmentPayload.getFeesStructureIds())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select at least one fees structure to assign fee structure."));
        }

        List<Student> requiredStudents = studentManager.getStudentByAcademicSessionStudentIds(instituteId, academicSessionId,
                new ArrayList<>(bulkStudentFeeStructureAssignmentPayload.getStudentIds()),
                new HashSet<>(Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING,
                        StudentStatus.NSO, StudentStatus.RELIEVED)));



        Set<UUID> standardIdsSet = new HashSet<>();
        Map<UUID, Student> studentMap = new HashMap<>();
        for (Student student : requiredStudents) {
            standardIdsSet.add(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId());
            studentMap.put(student.getStudentId(), student);
        }

        //FeeId, List<FeeHeadId>
        Map<UUID, Map<Integer, FeeHeadAmountDetails>> instituteStructureFeeFeeHeadListMap = new HashMap<>();
        //StandardId, FeeId, List<FeeHeadId>
        Map<String, Map<UUID, Map<Integer, FeeHeadAmountDetails>>> standardStructureFeeFeeHeadListMap = new HashMap<>();
        computeFeeStructureMaps(instituteId, academicSessionId, bulkStudentFeeStructureAssignmentPayload,
                instituteStructureFeeFeeHeadListMap, standardStructureFeeFeeHeadListMap);

        BulkStudentFeeData bulkStudentFeeData = feeCalculator.getBulkStudentFeeData(instituteId, academicSessionId, standardIdsSet,
                null, null);

        List<String> errorDetailsList = isFeeAssignmentUpdationPossible(bulkStudentFeeData, studentMap, instituteStructureFeeFeeHeadListMap,
                standardStructureFeeFeeHeadListMap);

        /**
         * if errors present return back and showing errors in FE
         */
        if (!CollectionUtils.isEmpty(errorDetailsList)) {
            return errorDetailsList;
        }

        errorDetailsList = new ArrayList<>();

        //FeeId, StudentID, FeeHeadId, Amount
        Map<UUID, Map<UUID, Map<Integer, Double>>> addFeesAssignmentAmountMap = new HashMap<>();
        Map<UUID, Map<UUID, Map<Integer, Double>>> updateFeeAssignmentAmountMap = new HashMap<>();

        computeAddUpdateFeesStudentFeeHeadAmounts(instituteId, bulkStudentFeeData, studentMap, instituteStructureFeeFeeHeadListMap,
                standardStructureFeeFeeHeadListMap, addFeesAssignmentAmountMap, updateFeeAssignmentAmountMap);

        //feeId, FeeAssignmentDetails
        List<FeeAssignmentDetails> addNewFeeAssignmentDetailsList = getFeeAssignmentDetailsList(instituteId, addFeesAssignmentAmountMap);
        //feeId, FeeAssignmentDetails
        List<FeeAssignmentDetails> updateFeeAssignmentDetailsList = getFeeAssignmentDetailsList(instituteId, updateFeeAssignmentAmountMap);

        if (!addUpdateFeeAssignmentAtomic(instituteId, userId, addNewFeeAssignmentDetailsList, updateFeeAssignmentDetailsList)) {
            errorDetailsList.add("Error occurred while assigning bulk fee structure. Please contact technical team for further assistance.");
        }

        return errorDetailsList;
    }

    private boolean addUpdateFeeAssignmentAtomic(int instituteId, UUID userId, List<FeeAssignmentDetails> addNewFeeAssignmentDetailsList,
                                                 List<FeeAssignmentDetails> updateFeeAssignmentDetailsList) {

        /**
         * catching error which is thrown as we want to send
         */
        try {
            final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {

                    for (FeeAssignmentDetails addNewFeeAssignmentDetails : addNewFeeAssignmentDetailsList) {
                        //add fee assignment
                        final boolean isSuccess = assignFees(addNewFeeAssignmentDetails, Module.FEES, FeeAssignmentState.NEW, false,
                                false, userId);

                        if (!isSuccess) {
                            logger.error("Error while adding fees of feeId {} for institute {}",
                                    addNewFeeAssignmentDetails.getFeeId(), instituteId);
                            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_STRUCTURE_DETAILS,
                                    "Error while adding fees. Please try again in sometime."));
                        }
                    }

                    //update fee structure
                    for (FeeAssignmentDetails updateFeeAssignmentDetails : updateFeeAssignmentDetailsList) {
                        //add fee assignment
                        final boolean isSuccess = assignFees(updateFeeAssignmentDetails, Module.FEES, FeeAssignmentState.PARTIAL_UPDATE, false,
                                false, userId);

                        if (!isSuccess) {
                            logger.error("Error while updating fees of feeId {} for institute {}", updateFeeAssignmentDetails.getFeeId(), instituteId);
                            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_STRUCTURE_DETAILS,
                                    "Error while updating fees. Please try again in sometime."));
                        }
                    }
                    return true;
                }
            });
            return status;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error while adding or updating fees for institute {}", instituteId);
            return false;
        }
    }

    private List<FeeAssignmentDetails> getFeeAssignmentDetailsList(int instituteId, Map<UUID, Map<UUID, Map<Integer, Double>>> feesAssignmentAmountMap) {
        List<FeeAssignmentDetails> feeAssignmentDetailsList = new ArrayList<>();
        if (CollectionUtils.isEmpty(feesAssignmentAmountMap)) {
            return feeAssignmentDetailsList;
        }
        for (Entry<UUID, Map<UUID, Map<Integer, Double>>> feesAssignmentAmountEntry : feesAssignmentAmountMap.entrySet()) {
            UUID feeId = feesAssignmentAmountEntry.getKey();
            Map<UUID, Map<Integer, Double>> feeAssignmentAmountMap = feesAssignmentAmountEntry.getValue();
            for (Entry<UUID, Map<Integer, Double>> feeAssignmentAmountEntry : feeAssignmentAmountMap.entrySet()) {
                UUID studentId = feeAssignmentAmountEntry.getKey();
                Map<Integer, Double> feeHeadAmountMap = feeAssignmentAmountEntry.getValue();
                final List<EntityFees> entityFees = new ArrayList<>();
                final List<FeeHeadAmount> feeHeadAmounts = new ArrayList<>();
                for (final Entry<Integer, Double> feeHeadAmountEntry : feeHeadAmountMap.entrySet()) {
                    feeHeadAmounts.add(new FeeHeadAmount(feeHeadAmountEntry.getKey(), feeHeadAmountEntry.getValue()));
                }
                entityFees.add(new EntityFees(studentId.toString(), FeeEntity.STUDENT, feeHeadAmounts));
                final FeeAssignmentDetails feeAssignmentDetails = new FeeAssignmentDetails(instituteId, feeId,
                        entityFees);
                feeAssignmentDetailsList.add(feeAssignmentDetails);
            }
        }
        return feeAssignmentDetailsList;
    }

    private void computeAddUpdateFeesStudentFeeHeadAmounts(int instituteId, BulkStudentFeeData bulkStudentFeeData, Map<UUID, Student> studentMap,
                                                           Map<UUID, Map<Integer, FeeHeadAmountDetails>> instituteStructureFeeFeeHeadListMap,
                                                           Map<String, Map<UUID, Map<Integer, FeeHeadAmountDetails>>> standardStructureFeeFeeHeadListMap,
                                                           Map<UUID, Map<UUID, Map<Integer, Double>>> addFeesAssignmentAmountMap,
                                                           Map<UUID, Map<UUID, Map<Integer, Double>>> updateFeeAssignmentAmountMap) {
        //StudentId, FeeId, FeeHeadId, Amount
        Map<UUID, Map<UUID, Map<Integer, Double>>> studentFeeIdFeeHeadAmountMap = getStudentFeeAssignment(instituteId, studentMap, bulkStudentFeeData);

        for (Entry<UUID, Map<Integer, FeeHeadAmountDetails>> feeIdFeeHeadIdAmountDetailsEntry : instituteStructureFeeFeeHeadListMap.entrySet()) {

            UUID feeId = feeIdFeeHeadIdAmountDetailsEntry.getKey();
            Map<Integer, FeeHeadAmountDetails> feeHeadIdAmountDetailsMap = feeIdFeeHeadIdAmountDetailsEntry.getValue();

            if (!addFeesAssignmentAmountMap.containsKey(feeId)) {
                addFeesAssignmentAmountMap.put(feeId, new HashMap<>());
            }

            for (Entry<UUID, Map<UUID, Map<Integer, Double>>> studentFeeIdFeeHeadAmountEntry : studentFeeIdFeeHeadAmountMap.entrySet()) {

                UUID studentId = studentFeeIdFeeHeadAmountEntry.getKey();
                Map<UUID, Map<Integer, Double>> feeIdFeeHeadAmountMap = studentFeeIdFeeHeadAmountEntry.getValue();

                /**
                 * if fees not assigned to student then all feeHeads are added in add map
                 */
                if (CollectionUtils.isEmpty(feeIdFeeHeadAmountMap) || !feeIdFeeHeadAmountMap.containsKey(feeId)) {
                    for (final FeeHeadAmountDetails feeHeadAmountDetails : feeHeadIdAmountDetailsMap.values()) {
                        if (!addFeesAssignmentAmountMap.get(feeId).containsKey(studentId)) {
                            addFeesAssignmentAmountMap.get(feeId).put(studentId, new HashMap<>());
                        }
                        addFeesAssignmentAmountMap.get(feeId).get(studentId).put(feeHeadAmountDetails.getFeeHeadId(), feeHeadAmountDetails.getAmount());
                    }
                    continue;
                }

                Map<Integer, Double> feeHeadAmountMap = feeIdFeeHeadAmountMap.get(feeId);
                for (Entry<Integer, FeeHeadAmountDetails> feeHeadIdAmountDetailsEntry : feeHeadIdAmountDetailsMap.entrySet()) {
                    Integer feeHeadId = feeHeadIdAmountDetailsEntry.getKey();
                    FeeHeadAmountDetails feeHeadAmountDetailsValue = feeHeadIdAmountDetailsEntry.getValue();
                    if (feeHeadAmountMap.containsKey(feeHeadId)) {
                        //update
                        if (updateFeeAssignmentAmountMap.containsKey(feeId)) {
                            if (!updateFeeAssignmentAmountMap.get(feeId).containsKey(studentId)) {
                                updateFeeAssignmentAmountMap.get(feeId).put(studentId, new HashMap<>());
                            }
                            updateFeeAssignmentAmountMap.get(feeId).get(studentId).put(feeHeadAmountDetailsValue.getFeeHeadId(), feeHeadAmountDetailsValue.getAmount());
                        } else {
                            updateFeeAssignmentAmountMap.put(feeId, new HashMap<>());
                            updateFeeAssignmentAmountMap.get(feeId).put(studentId, new HashMap<>());
                            updateFeeAssignmentAmountMap.get(feeId).get(studentId).put(feeHeadAmountDetailsValue.getFeeHeadId(), feeHeadAmountDetailsValue.getAmount());
                        }
                    } else {
                        //add
                        if (addFeesAssignmentAmountMap.containsKey(feeId)) {
                            if (!addFeesAssignmentAmountMap.get(feeId).containsKey(studentId)) {
                                addFeesAssignmentAmountMap.get(feeId).put(studentId, new HashMap<>());
                            }
                            addFeesAssignmentAmountMap.get(feeId).get(studentId).put(feeHeadAmountDetailsValue.getFeeHeadId(), feeHeadAmountDetailsValue.getAmount());
                        } else {
                            addFeesAssignmentAmountMap.put(feeId, new HashMap<>());
                            addFeesAssignmentAmountMap.get(feeId).put(studentId, new HashMap<>());
                            addFeesAssignmentAmountMap.get(feeId).get(studentId).put(feeHeadAmountDetailsValue.getFeeHeadId(), feeHeadAmountDetailsValue.getAmount());
                        }
                    }
                }
            }
        }

        for (Entry<UUID, Map<UUID, Map<Integer, Double>>> studentFeeIdFeeHeadAmountEntry : studentFeeIdFeeHeadAmountMap.entrySet()) {

            UUID studentId = studentFeeIdFeeHeadAmountEntry.getKey();
            Student student = studentMap.get(studentId);
            UUID standardId = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
            Map<UUID, Map<Integer, FeeHeadAmountDetails>> structureFeeFeeHeadListMap = standardStructureFeeFeeHeadListMap.get(standardId.toString());
            /**
             * filtering out standards in which student doesnot belong
             */
            if (CollectionUtils.isEmpty(structureFeeFeeHeadListMap)) {
                continue;
            }
            for (Entry<UUID, Map<Integer, FeeHeadAmountDetails>> feeIdFeeHeadIdAmountDetailsEntry : structureFeeFeeHeadListMap.entrySet()) {
                UUID feeId = feeIdFeeHeadIdAmountDetailsEntry.getKey();
                Map<Integer, FeeHeadAmountDetails> feeHeadIdAmountDetailsMap = feeIdFeeHeadIdAmountDetailsEntry.getValue();

                if (!addFeesAssignmentAmountMap.containsKey(feeId)) {
                    addFeesAssignmentAmountMap.put(feeId, new HashMap<>());
                }

                Map<UUID, Map<Integer, Double>> feeIdFeeHeadAmountMap = studentFeeIdFeeHeadAmountEntry.getValue();

                /**
                 * if fees not assigned to student then all feeHeads are added in add map
                 */
                if (CollectionUtils.isEmpty(feeIdFeeHeadAmountMap) || !feeIdFeeHeadAmountMap.containsKey(feeId)) {
                    for (final FeeHeadAmountDetails feeHeadAmountDetails : feeHeadIdAmountDetailsMap.values()) {
                        if (!addFeesAssignmentAmountMap.get(feeId).containsKey(studentId)) {
                            addFeesAssignmentAmountMap.get(feeId).put(studentId, new HashMap<>());
                        }
                        addFeesAssignmentAmountMap.get(feeId).get(studentId).put(feeHeadAmountDetails.getFeeHeadId(), feeHeadAmountDetails.getAmount());
                    }
                    continue;
                }

                Map<Integer, Double> feeHeadAmountMap = feeIdFeeHeadAmountMap.get(feeId);
                for (Entry<Integer, FeeHeadAmountDetails> feeHeadIdAmountDetailsEntry : feeHeadIdAmountDetailsMap.entrySet()) {
                    Integer feeHeadId = feeHeadIdAmountDetailsEntry.getKey();
                    FeeHeadAmountDetails feeHeadAmountDetailsValue = feeHeadIdAmountDetailsEntry.getValue();
                    if (feeHeadAmountMap.containsKey(feeHeadId)) {
                        //update
                        if (updateFeeAssignmentAmountMap.containsKey(feeId)) {
                            if (!updateFeeAssignmentAmountMap.get(feeId).containsKey(studentId)) {
                                updateFeeAssignmentAmountMap.get(feeId).put(studentId, new HashMap<>());
                            }
                            updateFeeAssignmentAmountMap.get(feeId).get(studentId).put(feeHeadAmountDetailsValue.getFeeHeadId(), feeHeadAmountDetailsValue.getAmount());
                        } else {
                            updateFeeAssignmentAmountMap.put(feeId, new HashMap<>());
                            updateFeeAssignmentAmountMap.get(feeId).put(studentId, new HashMap<>());
                            updateFeeAssignmentAmountMap.get(feeId).get(studentId).put(feeHeadAmountDetailsValue.getFeeHeadId(), feeHeadAmountDetailsValue.getAmount());
                        }
                    } else {
                        //add
                        if (addFeesAssignmentAmountMap.containsKey(feeId)) {
                            if (!addFeesAssignmentAmountMap.get(feeId).containsKey(studentId)) {
                                addFeesAssignmentAmountMap.get(feeId).put(studentId, new HashMap<>());
                            }
                            addFeesAssignmentAmountMap.get(feeId).get(studentId).put(feeHeadAmountDetailsValue.getFeeHeadId(), feeHeadAmountDetailsValue.getAmount());
                        } else {
                            addFeesAssignmentAmountMap.put(feeId, new HashMap<>());
                            addFeesAssignmentAmountMap.get(feeId).put(studentId, new HashMap<>());
                            addFeesAssignmentAmountMap.get(feeId).get(studentId).put(feeHeadAmountDetailsValue.getFeeHeadId(), feeHeadAmountDetailsValue.getAmount());
                        }
                    }
                }
            }
        }
    }

    private List<String> isFeeAssignmentUpdationPossible(BulkStudentFeeData bulkStudentFeeData, Map<UUID, Student> studentMap,
                                                         Map<UUID, Map<Integer, FeeHeadAmountDetails>> instituteStructureFeeFeeHeadListMap,
                                                         Map<String, Map<UUID, Map<Integer, FeeHeadAmountDetails>>> standardStructureFeeFeeHeadListMap) {

        List<String> errorDetailsList = new ArrayList<>();
        Map<UUID, FeeConfigurationBasicInfo> feeConfigurationBasicInfoMap = new HashMap<>();
        Map<Integer, FeeHeadConfiguration> feeHeadConfigurationHashMap = new HashMap<>();

        //studentId, FeeId, FeeHeadId, Amount
        Map<UUID, Map<UUID, Map<Integer, Double>>> studentFeePayment = getStudentPaidFeePaymentDetails(studentMap, bulkStudentFeeData,
                feeConfigurationBasicInfoMap, feeHeadConfigurationHashMap);
        /**
         * Assuming payment will only be considered when any amount is present in
         * paid amount or paid fine amount
         */
        for (Entry<UUID, Map<UUID, Map<Integer, Double>>> studentFeeFeeHeadAmountMapEntry : studentFeePayment.entrySet()) {
            UUID studentId = studentFeeFeeHeadAmountMapEntry.getKey();
            Student student = studentMap.get(studentId);
            String studentName = student.getStudentBasicInfo().getName() + " (" + student.getStudentBasicInfo().getAdmissionNumber() + ")";
            String standardId = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId().toString();
            Map<UUID, Map<Integer, FeeHeadAmountDetails>> structureFeeFeeHeadListMap = standardStructureFeeFeeHeadListMap.get(standardId);
            for (Entry<UUID, Map<Integer, Double>> feeFeeHeadAmountMapEntry : studentFeeFeeHeadAmountMapEntry.getValue().entrySet()) {
                UUID feeId = feeFeeHeadAmountMapEntry.getKey();

                /**
                 * if fees is paid for feeIds which doesn't belong to any assigning structure then no need to check
                 */
                if (!instituteStructureFeeFeeHeadListMap.containsKey(feeId) &&
                        (CollectionUtils.isEmpty(structureFeeFeeHeadListMap) || !structureFeeFeeHeadListMap.containsKey(feeId))) {
                    continue;
                }

                FeeConfigurationBasicInfo feeConfigurationBasicInfo = feeConfigurationBasicInfoMap.get(feeId);
                String feeName = feeConfigurationBasicInfo.getFeeName();
                for (Entry<Integer, Double> feeHeadAmountMapEntry : feeFeeHeadAmountMapEntry.getValue().entrySet()) {
                    Integer feeHeadId = feeHeadAmountMapEntry.getKey();

                    /**
                     * if fees is paid for feeHeadId which doesn't belong to any assigning structure then no need to check
                     */
                    if ((CollectionUtils.isEmpty(instituteStructureFeeFeeHeadListMap.get(feeId)) || !instituteStructureFeeFeeHeadListMap.get(feeId).containsKey(feeHeadId))
                            && (CollectionUtils.isEmpty(structureFeeFeeHeadListMap) || CollectionUtils.isEmpty(structureFeeFeeHeadListMap.get(feeId))
                            || !structureFeeFeeHeadListMap.get(feeId).containsKey(feeHeadId))) {
                        continue;
                    }

                    FeeHeadConfiguration feeHeadConfiguration = feeHeadConfigurationHashMap.get(feeHeadId);
                    String feeHeadName = feeHeadConfiguration.getFeeHead();
                    errorDetailsList.add(String.format("%s : Fees paid for fee head %s of fees %s.", studentName, feeHeadName, feeName));
                }
            }
        }
        return errorDetailsList;
    }

    private void computeFeeStructureMaps(int instituteId, int academicSessionId, BulkStudentFeeStructureAssignmentPayload bulkStudentFeeStructureAssignmentPayload,
                                         Map<UUID, Map<Integer, FeeHeadAmountDetails>> instituteStructureFeeFeeHeadListMap,
                                         Map<String, Map<UUID, Map<Integer, FeeHeadAmountDetails>>> standardStructureFeeFeeHeadListMap) {
        List<DefaultEntityFeeAssignmentStructure> defaultEntityFeeAssignmentStructureList = getDefaultFeeAssignmentStructure(
                instituteId, academicSessionId);

        for (DefaultEntityFeeAssignmentStructure defaultEntityFeeAssignmentStructure : defaultEntityFeeAssignmentStructureList) {
            if (!bulkStudentFeeStructureAssignmentPayload.getFeesStructureIds().contains(defaultEntityFeeAssignmentStructure.getStructureId())) {
                continue;
            }

            List<EntityFeeAssignment> entityFeeAssignmentList = defaultEntityFeeAssignmentStructure.getEntityFeeAssignments();
            if (CollectionUtils.isEmpty(entityFeeAssignmentList)) {
                continue;
            }
            for (EntityFeeAssignment entityFeeAssignment : entityFeeAssignmentList) {
                List<FeeIdFeeHeadDetails> feeIdFeeHeadDetailsList = entityFeeAssignment.getFeeIdFeeHeadDetailsList();
                if (CollectionUtils.isEmpty(feeIdFeeHeadDetailsList)) {
                    continue;
                }
                FeeEntity feeEntity = entityFeeAssignment.getFeeEntity();
                String feeEntityId = entityFeeAssignment.getEntityId();
                if (feeEntity == FeeEntity.INSTITUTE) {
                    for (FeeIdFeeHeadDetails feeIdFeeHeadDetails : entityFeeAssignment.getFeeIdFeeHeadDetailsList()) {
                        UUID feeId = feeIdFeeHeadDetails.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeId();
                        if (!instituteStructureFeeFeeHeadListMap.containsKey(feeId)) {
                            instituteStructureFeeFeeHeadListMap.put(feeId, new HashMap<>());
                        }
                        for (FeeHeadAmountDetails feeHeadAmountDetails : feeIdFeeHeadDetails.getFeeHeadAmountDetailsList()) {
                            int feeHeadId = feeHeadAmountDetails.getFeeHeadId();
                            if (!instituteStructureFeeFeeHeadListMap.get(feeId).containsKey(feeHeadId)) {
                                instituteStructureFeeFeeHeadListMap.get(feeId).put(feeHeadId, feeHeadAmountDetails);
                            }
                        }
                    }
                } else if (feeEntity == FeeEntity.CLASS) {
                    if (!standardStructureFeeFeeHeadListMap.containsKey(feeEntityId)) {
                        standardStructureFeeFeeHeadListMap.put(feeEntityId, new HashMap<>());
                    }
                    for (FeeIdFeeHeadDetails feeIdFeeHeadDetails : entityFeeAssignment.getFeeIdFeeHeadDetailsList()) {
                        UUID feeId = feeIdFeeHeadDetails.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeId();
                        if (!standardStructureFeeFeeHeadListMap.get(feeEntityId).containsKey(feeId)) {
                            standardStructureFeeFeeHeadListMap.get(feeEntityId).put(feeId, new HashMap<>());
                        }
                        for (FeeHeadAmountDetails feeHeadAmountDetails : feeIdFeeHeadDetails.getFeeHeadAmountDetailsList()) {
                            int feeHeadId = feeHeadAmountDetails.getFeeHeadId();
                            if (!standardStructureFeeFeeHeadListMap.get(feeEntityId).get(feeId).containsKey(feeHeadId)) {
                                standardStructureFeeFeeHeadListMap.get(feeEntityId).get(feeId).put(feeHeadId, feeHeadAmountDetails);
                            }
                        }
                    }
                }
            }
        }
    }

    private Map<UUID, Map<UUID, Map<Integer, Double>>> getStudentFeeAssignment(int instituteId, Map<UUID, Student> studentMap, BulkStudentFeeData bulkStudentFeeData) {
        Map<UUID, EntityFeeAssignment> studentResolveFeeAssignment = feeCalculator.getResolvedFeeAssignment(instituteId, bulkStudentFeeData.getEntityFeeAssignmentsMap(), studentMap);
        Map<UUID, Map<UUID, Map<Integer, Double>>> studentFeeAssignment = new HashMap<>();
        for (Entry<UUID, Student> entry : studentMap.entrySet()) {
            UUID studentId = entry.getKey();
            EntityFeeAssignment entityFeeAssignment = studentResolveFeeAssignment.get(studentId);
            Map<UUID, Map<Integer, Double>> assignedFeesAmounts = entityFeeAssignment == null || CollectionUtils.isEmpty(entityFeeAssignment.getFeeIdFeeHeadDetailsList())
                    ? new HashMap<>() : feeCalculator.getStudentAssignedFeesMap(entityFeeAssignment.getFeeIdFeeHeadDetailsList());
            studentFeeAssignment.put(studentId, assignedFeesAmounts);
        }
        return studentFeeAssignment;
    }

    private Map<UUID, Map<UUID, Map<Integer, Double>>> getStudentPaidFeePaymentDetails(Map<UUID, Student> studentMap, BulkStudentFeeData bulkStudentFeeData,
                                                                                       Map<UUID, FeeConfigurationBasicInfo> feeConfigurationBasicInfoMap, Map<Integer, FeeHeadConfiguration> feeHeadConfigurationHashMap) {
        Map<UUID, Map<UUID, Map<Integer, Double>>> studentFeePayment = new HashMap<>();
        for (Entry<UUID, List<FeePaymentDetails>> feePaymentDetailsEntry : bulkStudentFeeData.getFeePaymentDetailsMaps().entrySet()) {
            UUID studentId = feePaymentDetailsEntry.getKey();
            if (!studentMap.containsKey(studentId)) {
                continue;
            }
            if (!studentFeePayment.containsKey(studentId)) {
                studentFeePayment.put(studentId, new HashMap<>());
            }
            List<FeePaymentDetails> feePaymentDetailsList = feePaymentDetailsEntry.getValue();
            if (CollectionUtils.isEmpty(feePaymentDetailsList)) {
                continue;
            }

            for (FeePaymentDetails feePaymentDetails : feePaymentDetailsList) {
                if (feePaymentDetails == null || CollectionUtils.isEmpty(feePaymentDetails.getFeeHeadPaymentDetails())) {
                    continue;
                }
                /**
                 * filtering entries which are of unpaid fees
                 */
                if (feePaymentDetails.getFeePaymentStatus() == FeePaymentStatus.UNPAID) {
                    continue;
                }
                UUID feeId = feePaymentDetails.getFeeConfigurationBasicInfo().getFeeId();
                if (!studentFeePayment.get(studentId).containsKey(feeId)) {
                    studentFeePayment.get(studentId).put(feeId, new HashMap<>());
                }
                if (!feeConfigurationBasicInfoMap.containsKey(feeId)) {
                    feeConfigurationBasicInfoMap.put(feeId, feePaymentDetails.getFeeConfigurationBasicInfo());
                }

                List<FeeHeadPaymentDetails> feeHeadPaymentDetailsList = feePaymentDetails.getFeeHeadPaymentDetails();

                for (FeeHeadPaymentDetails feeHeadPaymentDetails : feeHeadPaymentDetailsList) {
                    int feeHeadId = feeHeadPaymentDetails.getFeeHeadConfiguration().getFeeHeadId();
                    double paidAmount = NumberUtils.addValues(feeHeadPaymentDetails.getPaidAmount(), feeHeadPaymentDetails.getPaidFineAmount());
                    if (!studentFeePayment.get(studentId).get(feeId).containsKey(feeHeadId)) {
                        studentFeePayment.get(studentId).get(feeId).put(feeHeadId, paidAmount);
                    } else {
                        studentFeePayment.get(studentId).get(feeId).put(feeHeadId,
                                NumberUtils.addValues(studentFeePayment.get(studentId).get(feeId).get(feeHeadId), paidAmount));
                    }
                    if (!feeHeadConfigurationHashMap.containsKey(feeHeadId)) {
                        feeHeadConfigurationHashMap.put(feeHeadId, feeHeadPaymentDetails.getFeeHeadConfiguration());
                    }
                }
            }
        }
        return studentFeePayment;
    }

    public List<String> readableBulkFeeAssignmentDeletion(int instituteId, int academicSessionId, UUID userId,
                                                          BulkStudentFeesDeletionReadablePayload bulkStudentFeesDeletionReadablePayload) {

        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute."));
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
        }

        if (userId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid user id."));
        }

        if (bulkStudentFeesDeletionReadablePayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid payload."));
        }

        if (CollectionUtils.isEmpty(bulkStudentFeesDeletionReadablePayload.getStudentAdmissionNumberSet())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select at least one student to delete fee assignment."));
        }

        if (CollectionUtils.isEmpty(bulkStudentFeesDeletionReadablePayload.getFeesNameSet())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select at least one fees to delete fee assignment."));
        }

        /**
         * fetch student ids by admission number
         */
        List<Student> studentList = studentManager.getStudentByAcademicSessionAdmissionNumber(
                instituteId, academicSessionId, bulkStudentFeesDeletionReadablePayload.getStudentAdmissionNumberSet());
        Map<UUID, Student> studentMap = studentManager.getStudentMap(studentList);

        /**
         * fetch fee ids by fee names
         */
        final List<FeeConfigurationResponse> feeConfigurationResponseList = getFeeConfigurationByAcademicYear(instituteId, academicSessionId);
        Set<UUID> feeIdSet = new HashSet<>();
        for(FeeConfigurationResponse feeConfigurationResponse : feeConfigurationResponseList) {
            if(bulkStudentFeesDeletionReadablePayload.getFeesNameSet().contains(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeName())) {
                feeIdSet.add(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeId());
            }
        }

        return bulkFeeAssignmentDeletion(instituteId, academicSessionId, userId, new BulkStudentFeesDeletionPayload(studentMap.keySet(), feeIdSet));

    }

    public List<String> bulkFeeAssignmentDeletion(int instituteId, int academicSessionId, UUID userId, BulkStudentFeesDeletionPayload bulkStudentFeesDeletionPayload) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute."));
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
        }

        if (userId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid user id."));
        }

        if (bulkStudentFeesDeletionPayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid payload."));
        }

        if (CollectionUtils.isEmpty(bulkStudentFeesDeletionPayload.getStudentIds())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select at least one student to delete fee assignment."));
        }

        if (CollectionUtils.isEmpty(bulkStudentFeesDeletionPayload.getFeesIds())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select at least one fees to delete fee assignment."));
        }

        List<String> eligibleList = new ArrayList<String>();

        if (feePaymentTransactionDao.checkIfFeesPaidByAnyStudent(instituteId, bulkStudentFeesDeletionPayload.getFeesIds(),
                bulkStudentFeesDeletionPayload.getStudentIds())) {
            eligibleList.add("Student already have paid fees for these fees. Please cancel that for further actions.");
        }

        if (!CollectionUtils.isEmpty(eligibleList)) {
            return eligibleList;
        }

        List<Student> requiredStudents = studentManager.getStudentByAcademicSessionStudentIds(instituteId, academicSessionId,
                new ArrayList<>(bulkStudentFeesDeletionPayload.getStudentIds()),
                new HashSet<>(Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING,
                        StudentStatus.NSO, StudentStatus.RELIEVED)));

        Set<UUID> standardIdsSet = new HashSet<>();
        Map<UUID, Student> studentMap = new HashMap<>();
        for (Student student : requiredStudents) {
            standardIdsSet.add(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId());
            studentMap.put(student.getStudentId(), student);
        }

        BulkStudentFeeData bulkStudentFeeData = feeCalculator.getBulkStudentFeeData(instituteId, academicSessionId, standardIdsSet,
                null, null);

        Map<UUID, Map<UUID, Map<Integer, Double>>> studentFeeIdFeeHeadAmountMap = getStudentFeeAssignment(
                instituteId, studentMap, bulkStudentFeeData);

        List<FeeAssignmentDetails> feeAssignmentDetailsList = new ArrayList<>();
        //not using getFeeAssignmentDetailsList as it return Map<FeeId, Map<StudentId, Map<FeeHadId, Double>>>
        //and we need Map<StudentID, Map<FeeId, Map<FeeHadId, Double>>>
        for (Entry<UUID, Map<UUID, Map<Integer, Double>>> feesAssignmentAmountEntry : studentFeeIdFeeHeadAmountMap.entrySet()) {
            UUID studentId = feesAssignmentAmountEntry.getKey();
            if(!bulkStudentFeesDeletionPayload.getStudentIds().contains(studentId)) {
                continue;
            }
            Map<UUID, Map<Integer, Double>> feeAssignmentAmountMap = feesAssignmentAmountEntry.getValue();
            for (Entry<UUID, Map<Integer, Double>> feeAssignmentAmountEntry : feeAssignmentAmountMap.entrySet()) {
                UUID feeId = feeAssignmentAmountEntry.getKey();
                if(!bulkStudentFeesDeletionPayload.getFeesIds().contains(feeId)) {
                    continue;
                }
                Map<Integer, Double> feeHeadAmountMap = feeAssignmentAmountEntry.getValue();
                final List<EntityFees> entityFees = new ArrayList<>();
                final List<FeeHeadAmount> feeHeadAmounts = new ArrayList<>();
                for (final Entry<Integer, Double> feeHeadAmountEntry : feeHeadAmountMap.entrySet()) {
                    feeHeadAmounts.add(new FeeHeadAmount(feeHeadAmountEntry.getKey(), feeHeadAmountEntry.getValue()));
                }
                entityFees.add(new EntityFees(studentId.toString(), FeeEntity.STUDENT, feeHeadAmounts));
                final FeeAssignmentDetails feeAssignmentDetails = new FeeAssignmentDetails(instituteId, feeId,
                        entityFees);
                feeAssignmentDetailsList.add(feeAssignmentDetails);
            }
        }

        List<String> errorDetailsList = new ArrayList<>();
        if (!deleteFeeAssignmentAtomic(instituteId, userId, feeAssignmentDetailsList)) {
            errorDetailsList.add("Error occurred while bulk deleting fee assignment. Please contact technical team for further assistance.");
        }

        return errorDetailsList;
    }

    private boolean deleteFeeAssignmentAtomic(int instituteId, UUID userId, List<FeeAssignmentDetails> deleteFeeAssignmentDetailsList) {
        /**
         * catching error which is thrown as we want to send
         */
        try {
            final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {

                    for (FeeAssignmentDetails addNewFeeAssignmentDetails : deleteFeeAssignmentDetailsList) {
                        List<FeeEntityData> feeEntityDataList = new ArrayList<>();
                        for(EntityFees entityFees : addNewFeeAssignmentDetails.getEntityFees()) {
                            feeEntityDataList.add(new FeeEntityData(entityFees.getFeeEntity(), entityFees.getEntityId()));
                        }
                        //delete fee assignment
                        final boolean isSuccess = deleteAssignedFees(instituteId, addNewFeeAssignmentDetails.getFeeId(),
                                feeEntityDataList, Module.FEES, userId);

                        if (!isSuccess) {
                            logger.error("Error while deleting fees of feeId {} for institute {}",
                                    addNewFeeAssignmentDetails.getFeeId(), instituteId);
                            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_STRUCTURE_DETAILS,
                                    "Error while deleting fees. Please try again in sometime."));
                        }
                    }
                    return true;
                }
            });
            return status;
        } catch (Exception e) {
            logger.error("Error while deleting fees for institute {}", instituteId);
            return false;
        }
    }
}
