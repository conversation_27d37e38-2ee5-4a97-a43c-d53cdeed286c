package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.configurations.LibraryPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> mittal
 */

public class LibraryFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public LibraryFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature libraryProductFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        libraryProductFeature = new ProductFeature("com.embrate.feature.library.core", "Library Preferences", "Library Preferences");
        addFeaturePreferenceGroup(libraryProductFeature, getBasicPreferences());
        return libraryProductFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return libraryProductFeature;
    }

    private IFeaturePreferenceGroupBuilder getBasicPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.basic";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                featurePreferenceEntities.add(new FeaturePreferenceEntity("enable_accession_number", "Enable Accession Number", "'When the flag is enable, institute can add the details of books as per Accession Number.", PreferenceDataType.BOOLEAN, LibraryPreferences.getConfigType(), LibraryPreferences.ENABLE_ACCESSION_NUMBER));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("accession_number_counter", "Accession Number Counter", "Enable it to give Accession Number Counter For Individual Book Details", PreferenceDataType.BOOLEAN, LibraryPreferences.getConfigType(), LibraryPreferences.ACCESSION_NUMBER_COUNTER));
                return featurePreferenceEntities;
            }
        };
    }
}
