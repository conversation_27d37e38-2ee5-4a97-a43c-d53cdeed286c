package com.embrate.cloud.core.lib.institute.onboarding.step;

import com.embrate.cloud.core.api.leave.management.LeavePaymentType;
import com.embrate.cloud.core.api.leave.management.LeaveType;
import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteSetupPayload;
import com.embrate.cloud.core.lib.institute.onboarding.IConfigureInstituteStep;
import com.embrate.cloud.core.lib.leave.management.LeaveConfigurationManager;
import com.lernen.cloud.core.api.fees.*;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.dao.tier.fees.configuration.FeeConfigurationDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */

public class LeaveModuleSetupProcessor implements IModuleSetupProcessor {

    private static final Logger logger = LogManager.getLogger(LeaveModuleSetupProcessor.class);
    private final LeaveConfigurationManager leaveConfigurationManager;

    public LeaveModuleSetupProcessor(LeaveConfigurationManager leaveConfigurationManager) {
        this.leaveConfigurationManager = leaveConfigurationManager;
    }

    @Override
    public Module getModule() {
        return Module.LEAVE_MANAGEMENT;
    }

    @Override
    public List<IConfigureInstituteStep> getSteps(InstituteSetupPayload instituteSetupPayload) {
        int instituteId = instituteSetupPayload.getInstituteBasicSetupResult().getInstituteId();
        List<IConfigureInstituteStep> instituteSetupStepsList = new ArrayList<>();
        instituteSetupStepsList.add(getStudentLeaveTypeStep(instituteId));
        return instituteSetupStepsList;
    }

    private IConfigureInstituteStep getStudentLeaveTypeStep(int instituteId){
        return new IConfigureInstituteStep() {
            @Override
            public String getName() {
                return "Student Leave Type";
            }

            @Override
            public boolean execute() {
                LeaveType leaveType = new LeaveType(0, "Student Leave Apply", "SLA",
                        LeavePaymentType.UNPAID, UserType.STUDENT, "Leave Type specific to student for applying leave", null);
                return leaveConfigurationManager.createLeaveType(instituteId, leaveType, false);
            }
        };
    }
}
