package com.embrate.cloud.core.lib.attendance.service.mantra;

import com.embrate.cloud.core.api.attendance.AttendanceDeviceServiceProviderType;
import com.embrate.cloud.core.api.attendance.DeviceAttendanceResult;
import com.embrate.cloud.core.api.attendance.DeviceEventResponse;
import com.embrate.cloud.core.api.attendance.DeviceUserUpdateResult;
import com.embrate.cloud.core.api.attendance.service.camsunit.CamsUnitAttendancePayload;
import com.embrate.cloud.core.api.attendance.service.mantra.MantraAttendancePayload;
import com.embrate.cloud.core.api.attendance.service.mantra.MantraAttendanceTransaction;
import com.embrate.cloud.core.lib.attendance.UserAttendanceManager;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.units.qual.A;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */

public class MantraDeviceManager {

	private static final Logger logger = LogManager.getLogger(MantraDeviceManager.class);

	private final UserAttendanceManager userAttendanceManager;

	public MantraDeviceManager(UserAttendanceManager userAttendanceManager) {
		this.userAttendanceManager = userAttendanceManager;
	}

	public List<DeviceEventResponse> handleEvent(String payload) {
		if (StringUtils.isBlank(payload)) {
			logger.error("Blank payload {}", payload);
			return Arrays.asList(new DeviceEventResponse(new DeviceAttendanceResult(true, null, null, null, null, null, null, null, false, false), null));
		}

		MantraAttendancePayload mantraAttendancePayload = null;
		try {
			mantraAttendancePayload = SharedConstants.OBJECT_MAPPER.readValue(payload, MantraAttendancePayload.class);
		} catch (IOException e) {
			logger.warn("Unable to parse the payload {} ", payload, e);
			return Arrays.asList(new DeviceEventResponse(new DeviceAttendanceResult(true, null, null, null, null, null, null, null, false, false), null));
		}
		if (CollectionUtils.isNotEmpty(mantraAttendancePayload.getTransactionList())) {
			// Downstream method supports single event at a time. So converting this list into single event
			List<DeviceEventResponse> deviceEventResponseList = new ArrayList<>();
			for (MantraAttendanceTransaction mantraAttendanceTransaction : mantraAttendancePayload.getTransactionList()) {
				DeviceAttendanceResult deviceAttendanceResult = userAttendanceManager.markUserAttendance(AttendanceDeviceServiceProviderType.MANTRA, new MantraAttendancePayload(mantraAttendanceTransaction.getDvcId(), Arrays.asList(mantraAttendanceTransaction)));
				deviceEventResponseList.add(new DeviceEventResponse(deviceAttendanceResult, null));
			}

			return deviceEventResponseList;
		}
		logger.warn("Ignoring event for payload {}", payload);
		return Arrays.asList(new DeviceEventResponse(new DeviceAttendanceResult(true, null, null, null, null, null, null, null, false, false), null));

	}
}
