package com.embrate.cloud.core.lib.service.payment.gateway;

import com.embrate.cloud.core.api.payment.PaymentApplicableDiscount;
import com.embrate.cloud.core.api.payment.PaymentApplicableFine;
import com.embrate.cloud.core.api.payment.PaymentRequest;
import com.embrate.cloud.core.api.service.payment.gateway.*;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantDetails;
import com.embrate.cloud.core.api.service.payment.gateway.utils.PGPlatform;
import com.embrate.cloud.core.api.service.payment.gateway.utils.PGUser;
import com.embrate.cloud.core.api.wallet.WalletTransactionCategory;
import com.embrate.cloud.core.lib.service.payment.gateway.merchant.PaymentGatewayMerchantManager;
import com.embrate.cloud.core.lib.wallet.UserWalletManager;
import com.embrate.cloud.dao.tier.service.payment.gateway.PaymentGatewayTransactionsDao;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.PaymentGatewayRuntimeException;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.RegistrationStudent;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.TaggedActions;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.RestrictionUtils;
import com.lernen.cloud.core.utils.RetryableCallable;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.rest.RestAPIHandler;
import com.lernen.cloud.dao.tier.student.StudentRegistrationDao;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

import static com.embrate.cloud.core.api.service.payment.gateway.PGTransactionType.STUDENT_FEE_PAYMENT;
import static com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayAmountTxnResponse.PAYMENT_GATEWAY_AMOUNT_TXN_RESPONSE;
import static com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayTransactionStatus.SUCCESS;
import static com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayTransactionStatus.finalStatus;

/**
 * <AUTHOR>
 */
public class PaymentGatewayManager {

    private static final Logger logger = LogManager.getLogger(PaymentGatewayManager.class);

    public static final String PAYMENT_GATEWAY_WEBHOOK_DATA = "webhook_data";

    public static final int SUCCESS_CODE = 0;
    public static final int PAYMENT_SUCCESS_TXN_FAILURE_WALLET_DEPOSIT_SUCCESS = 10001;
    public static final int PAYMENT_SUCCESS_TXN_FAILURE_WALLET_DEPOSIT_FAILURE = 10002;
    public static final int PAYMENT_NOT_SUCCESS_STATE = 10003;

    public static final int INVALID_PAYLOAD = 1;
    public static final int PG_DISABLED = 2;
    public static final int PG_PROVIDER_NOT_CONFIGURED = 3;
    public static final int PG_TOKEN_ERROR = 4;
    public static final int REQUESTED_WALLET_AMOUNT_NOT_AVAILABLE = 5;
    public static final int REQUEST_VALIDATION_FAILED = 6;
    public static final int ACTIVE_MERCHANT_NOT_AVAILABLE = 7;
    public static final int SIGNATURE_VALIDATION_FAILURE = 8;
    public static final int INVALID_TRANSACTION_ID = 9;
    public static final int INVALID_PAYMENT_AMOUNT_REQUEST = 10;
    public static final int INTERNAL_ERROR = 11;
    public static final int UNKNOWN_ERROR = 12;
    public static final int TRANSACTION_ALREADY_PROCESSED = 13;
    public static final int TASK_HANDLER_NOT_FOUND = 14;
    public static final int UNABLE_TO_GET_TRANSACTION_LOCK = 15;
    public static final int EMPTY_EMAIL_OR_MOBILE_NUMBER = 16;
    public static final int FUTURE_SESSION_PAYMENT_RESTRICTED = 17;
    public static final int STUDENT_PAYMENT_PAYMENT_RESTRICTED = 18;
    private final PaymentGatewayServiceProviderFactory paymentGatewayServiceProviderFactory;
    private final UserPreferenceSettings userPreferenceSettings;
    private final UserWalletManager userWalletManager;
    private final PaymentGatewayMerchantManager paymentGatewayMerchantManager;
    private final PaymentGatewayTransactionHandlerFactory paymentGatewayTransactionHandlerFactory;
    private final FeePaymentManager feePaymentManager;
    private final TransactionTemplate transactionTemplate;
    private final UserManager userManager;
    private final PaymentGatewayTransactionsDao paymentGatewayTransactionsDao;
    private final PaymentGatewayTaskHandlerFactory paymentGatewayTaskHandlerFactory;
    private final PaymentGatewayWebhookProvider paymentGatewayWebhookProvider;
    private final StudentManager studentManager;

    private final RestAPIHandler restAPIHandler;

    private final StudentRegistrationDao studentRegistrationDao;

    private final InstituteManager instituteManager;

    public PaymentGatewayManager(PaymentGatewayServiceProviderFactory paymentGatewayServiceProviderFactory,
                                 UserPreferenceSettings userPreferenceSettings, UserWalletManager userWalletManager,
                                 PaymentGatewayMerchantManager paymentGatewayMerchantManager,
                                 PaymentGatewayTransactionHandlerFactory paymentGatewayTransactionHandlerFactory,
                                 FeePaymentManager feePaymentManager, TransactionTemplate transactionTemplate, UserManager userManager,
                                 PaymentGatewayTransactionsDao paymentGatewayTransactionsDao, PaymentGatewayTaskHandlerFactory paymentGatewayTaskHandlerFactory,
                                 PaymentGatewayWebhookProvider paymentGatewayWebhookProvider,StudentManager studentManager,
                                 RestAPIHandler restAPIHandler, StudentRegistrationDao studentRegistrationDao, InstituteManager instituteManager) {
        this.paymentGatewayServiceProviderFactory = paymentGatewayServiceProviderFactory;
        this.userPreferenceSettings = userPreferenceSettings;
        this.userWalletManager = userWalletManager;
        this.paymentGatewayMerchantManager = paymentGatewayMerchantManager;
        this.paymentGatewayTransactionHandlerFactory = paymentGatewayTransactionHandlerFactory;
        this.feePaymentManager = feePaymentManager;
        this.transactionTemplate = transactionTemplate;
        this.userManager = userManager;
        this.paymentGatewayTransactionsDao = paymentGatewayTransactionsDao;
        this.paymentGatewayTaskHandlerFactory = paymentGatewayTaskHandlerFactory;
        this.paymentGatewayWebhookProvider = paymentGatewayWebhookProvider;
        this.studentManager = studentManager;
        this.restAPIHandler = restAPIHandler;
        this.studentRegistrationDao = studentRegistrationDao;
        this.instituteManager = instituteManager;
    }


    public UserPaymentGatewayMetadata getPaymentGatewayTransactionMetadata(UUID userId, int instituteId, boolean walletAmount, PaymentRequest paymentRequest) {
        Double currentWalletAmount = null;
        if (walletAmount) {
            currentWalletAmount = userWalletManager.getWalletAmount(userId, null);
        }
        MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
        boolean autoComputePaymentDiscountAndFine = metaDataPreferences.isAutoComputePaymentDiscountAndFine();
        PaymentApplicableDiscount paymentApplicableDiscount = new PaymentApplicableDiscount(false, null, null, null, null);
        PaymentApplicableFine paymentApplicableFine = new PaymentApplicableFine(false, null, null, null);
        if(paymentRequest != null && autoComputePaymentDiscountAndFine){
            paymentApplicableDiscount = feePaymentManager.getApplicableDiscount(instituteId, paymentRequest);
            paymentApplicableFine = feePaymentManager.getApplicableFine(instituteId, paymentRequest);
        }

        boolean restrictFutureSessionPayment = false;
        if(paymentRequest != null){
            restrictFutureSessionPayment = feePaymentManager.restrictSessionFeesPayment(
                    instituteId, paymentRequest.getAcademicSessionId(), paymentRequest.getStudentId());

//            final Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, paymentRequest.getAcademicSessionId(), paymentRequest.getStudentId());
//             currently, we are restricting student from paying school fees.
//            if(paymentRequest.getPaymentType() == PaymentType.FEES){
//                RestrictionUtils.validateRestrictionActions(student, TaggedActions.FEE_PAYMENT_RESTRICTED);
//            }
        }

        boolean restrictStudentUseWalletFeesPayment = metaDataPreferences.isRestrictStudentUseWalletFeesPayment();

        //TODO : Get from configs
        return new UserPaymentGatewayMetadata(currentWalletAmount, true, "Txn and Convenience Charges :- Please verify actual charges after selecting payment method. For Credit Cards: 2% of Net amount, for Debit Cards: 1.2% of Net amount, for UPI: 0.5% of Net amount, for Net Banking: up to INR 25, for Wallets: 2% of Net amount, GST as applicable", paymentApplicableDiscount, paymentApplicableFine, restrictFutureSessionPayment, restrictStudentUseWalletFeesPayment);

    }

    public List<PaymentGatewayTransactionData> getTransactions(int instituteId, UUID userId, PGTransactionType transactionType, boolean includeCancelledTransaction){
       return paymentGatewayTransactionsDao.getTransactions(instituteId, userId, transactionType, includeCancelledTransaction);
    }

    /**
     * Initiates the payment gateway transaction. Generates token and logs the transaction in db with all requested params
     *
     * @param instituteId
     * @param initiateTransactionPayload
     * @return
     */
    public PGInitiateTransactionResponse initiatePaymentTransaction(int instituteId, PGInitiateTransactionPayload initiateTransactionPayload,
                                                                    PGPlatform pgPlatform) {

        Institute institute = instituteManager.getInstitute(instituteId);
        String instituteName = institute.getInstituteName();

        MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);

        try {
            InitiateTransactionDataWrapper initiateTransactionDataWrapper = getInitiateTransactionDataWrapper(instituteId,
                    initiateTransactionPayload, metaDataPreferences);

            UUID transactionId = UUID.randomUUID();

            PGUser user = initiateTransactionDataWrapper.getUser();
            String webhookURL = paymentGatewayWebhookProvider.getWebhookURL(instituteId, initiateTransactionDataWrapper.getServiceProvider());

            final PGTransactionTokenResponse transactionTokenResponse = executeInitiateTransactionRequest(instituteId, initiateTransactionPayload, transactionId, initiateTransactionDataWrapper,
                    pgPlatform, user, webhookURL);
            //TODO : enforce email and mobile number

            return new PGInitiateTransactionResponse(true, SUCCESS_CODE, null, initiateTransactionDataWrapper.getServiceProvider(),
                    transactionTokenResponse.getClientId(), transactionId, transactionTokenResponse.getTransactionToken(),
                    initiateTransactionPayload.getPaymentGatewayAmount(), initiateTransactionPayload.getWalletAmount(), null, user.getName(), user.getEmail(), user.getContactNumber(), webhookURL , null, null,
                    instituteName, transactionTokenResponse.getMetadata());

        } catch (PaymentGatewayRuntimeException pge) {
            logger.error("Error while initiating payment transaction for institute {}, initiateTransactionPayload {} ",
                    instituteId, initiateTransactionPayload, pge);
            return getInitiateTransactionFailureResponse(pge.getErrorCode(), pge.getMessage(), pge.getRequestedWalletAmount(), pge.getCurrentWalletAmount(), initiateTransactionPayload.getPaymentGatewayAmount());
        } catch (final Exception e) {
            logger.error("Exception while initiating payment transaction for institute {}, user {} ",
                    instituteId, initiateTransactionPayload, e);
            return getInitiateTransactionFailureResponse(INTERNAL_ERROR, "Error while initiating payment request");
        }
    }


    /**
     * Process the payment gateway transaction. Once the payload is received from client application, it is validated and corresponding
     * task handler is invoked to process the transaction and updates the transaction status.
     *
     * @param instituteId
     * @param processTransactionPayload
     * @return
     */
    public PGProcessTransactionResponse processTransaction(int instituteId, PGProcessTransactionPayload processTransactionPayload, boolean includeSystemTransactionDetails) {
        logger.info(processTransactionPayload);

        UUID transactionId = null;
        try {
            MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);

            ProcessTransactionDataWrapper processTransactionDataWrapper = getProcessTransactionData(instituteId, processTransactionPayload, metaDataPreferences);
            PaymentGatewayServiceProvider paymentGatewayServiceProvider = processTransactionDataWrapper.getServiceProvider();
            IPaymentGatewayTransactionHandler paymentGatewayTransactionHandler = processTransactionDataWrapper.getPaymentGatewayTransactionHandler();

            transactionId = paymentGatewayTransactionHandler.getTransactionId();
            if (transactionId == null) {
                logger.error("Invalid transaction id for {}, payload {}", paymentGatewayServiceProvider, processTransactionPayload);
                return PGProcessTransactionResponse.forFailure(INVALID_TRANSACTION_ID, "Invalid transaction id");
            }

            return executeProcessTransaction(instituteId, paymentGatewayServiceProvider, paymentGatewayTransactionHandler, transactionId, includeSystemTransactionDetails);

        } catch (PaymentGatewayRuntimeException pge) {

            logger.error("Error while processing payment transaction for institute {}, processTransactionPayload {} ",
                    instituteId, processTransactionPayload, pge);

            return PGProcessTransactionResponse.forFailure(pge.getErrorCode(), pge.getMessage());

        } catch (Exception e) {

            logger.error("Error while processing the txn {}, instituteId {}, processTransactionPayload {}", transactionId, instituteId, processTransactionPayload, e);
        }

        return PGProcessTransactionResponse.forFailure(INTERNAL_ERROR, "Internal error occurred");
    }

    private ProcessTransactionDataWrapper getProcessTransactionData(int instituteId, PGProcessTransactionPayload processTransactionPayload, MetaDataPreferences metaDataPreferences) {
        if (!metaDataPreferences.isPaymentGatewayServiceEnabled()) {
            logger.error("Payment gateway service not enabled for institute {}", instituteId);
            throw new PaymentGatewayRuntimeException(PG_DISABLED, "Payment gateway service not enabled for institute");
        }

        PaymentGatewayServiceProvider paymentGatewayServiceProvider = metaDataPreferences.getPaymentGatewayServiceProvider();
        if (paymentGatewayServiceProvider == null) {
            logger.error("No payment gateway service provider configured for institute {}", instituteId);
            throw new PaymentGatewayRuntimeException(PG_PROVIDER_NOT_CONFIGURED, "No payment gateway service provider configured for institute");
        }

        PGMerchantDetails merchantDetails = paymentGatewayMerchantManager.getPaymentMerchant(instituteId, paymentGatewayServiceProvider);
        if (merchantDetails == null) {
            logger.error("No active merchant found for request, {}, {}", instituteId, paymentGatewayServiceProvider);
            throw new PaymentGatewayRuntimeException(ACTIVE_MERCHANT_NOT_AVAILABLE, "No active merchant found");
        }

        IPaymentGatewayTransactionHandler paymentGatewayTransactionHandler = paymentGatewayTransactionHandlerFactory.getPaymentGatewayTransactionHandler(paymentGatewayServiceProvider, processTransactionPayload, merchantDetails,
                restAPIHandler);
        if (paymentGatewayTransactionHandler == null) {
            logger.error("No payment gateway transaction handler configured for {}, {}", instituteId, paymentGatewayServiceProvider);
            throw new PaymentGatewayRuntimeException(PG_PROVIDER_NOT_CONFIGURED, "No payment gateway service provider configured for institute");
        }

        if (!paymentGatewayTransactionHandler.validTransactionSignature()) {
            logger.error("Invalid transaction signature for {}, payload {}", paymentGatewayServiceProvider, processTransactionPayload);
            throw new PaymentGatewayRuntimeException(SIGNATURE_VALIDATION_FAILURE, "Invalid transaction signature");
        }
        
        return new ProcessTransactionDataWrapper(paymentGatewayServiceProvider, paymentGatewayTransactionHandler);
    }

    /**
     * This method fetches the details of transaction, takes lock on transaction,
     * executes the system txn associated with task, logs the update
     * for transaction and releases the lock
     *
     * @param instituteId
     * @param paymentGatewayServiceProvider
     * @param paymentGatewayTransactionHandler
     * @param transactionId
     * @return
     * @throws InterruptedException
     */
    private PGProcessTransactionResponse executeProcessTransaction(int instituteId, PaymentGatewayServiceProvider paymentGatewayServiceProvider,
                                                                   IPaymentGatewayTransactionHandler paymentGatewayTransactionHandler,
                                                                   UUID transactionId, boolean includeSystemTransactionDetails) throws InterruptedException {

        PaymentGatewayTransactionStatus currentRequestStatus = paymentGatewayTransactionHandler.getTransactionStatus();

        if (currentRequestStatus == null) {
            logger.error("Unable to get transaction status for txt {}, instituteId {}, paymentGatewayServiceProvider {}, payload {}", transactionId, instituteId, paymentGatewayServiceProvider, paymentGatewayTransactionHandler.getTransactionData());
            return PGProcessTransactionResponse.forFailure(INVALID_PAYLOAD, "Invalid transaction status");
        }

        /**
         * Acquires the lock as multiple clients can attempt this transaction concurrently (webhooks, api calls etc).
         * This step is must and no other process should interact with payment gateway transaction without acquiring lock.
         */
        PGProcessTransactionResponse acquireLockProcessTransactionResponse = processOrAcquireLock(instituteId, paymentGatewayTransactionHandler, transactionId);
        if (acquireLockProcessTransactionResponse != null) {
            logger.error("Skipping transaction handling for transaction {}", transactionId);
            return acquireLockProcessTransactionResponse;
        }

        logger.info("Lock acquired on transaction {}. Proceeding with handling the transaction.", transactionId);

        PaymentGatewayTransactionData paymentGatewayTransactionData = paymentGatewayTransactionsDao.getTransaction(transactionId, null);
        PaymentGatewayTransactionStatus savedTransactionStatus = paymentGatewayTransactionData.getTransactionStatus();

        AbstractPaymentGatewayTaskHandler paymentGatewayTaskHandler = paymentGatewayTaskHandlerFactory.getPaymentGatewayTaskHandler(paymentGatewayTransactionData.getTransactionType());
        if (finalStatus(savedTransactionStatus) || currentRequestStatus == savedTransactionStatus) {
            logger.info("Transaction already in final state. Skipping the new update for txn {}, payload {}", transactionId, paymentGatewayTransactionHandler.getTransactionData());
            PaymentGatewaySystemTransactionDetails paymentGatewaySystemTransactionDetails = null;
            if(savedTransactionStatus == SUCCESS && includeSystemTransactionDetails){
                paymentGatewaySystemTransactionDetails = paymentGatewayTaskHandler.getSystemTransactionDetails(paymentGatewayTransactionData);
            }

            // Release the acquired lock.
            releaseLogTransactionLock(transactionId);

            return new PGProcessTransactionResponse(true, paymentGatewayTransactionData.getErrorCode(), paymentGatewayTransactionData.getTransactionId(), paymentGatewayTransactionData.getTransactionToken(), paymentGatewayTransactionData.getTransactionStatus(),
                    paymentGatewayTransactionData.getServiceProvider(), paymentGatewayTransactionData.getTransactionType(), paymentGatewayTransactionData.getDescription(),
                    paymentGatewayTransactionData.getPaymentGatewayAmount(), paymentGatewayTransactionData.getWalletAmount(),
                    null, paymentGatewaySystemTransactionDetails);
        }

        PaymentGatewayAmountTxnResponse paymentGatewayAmountTxnResponse = null;
        if(currentRequestStatus == SUCCESS){
            paymentGatewayAmountTxnResponse = handlePaymentGatewayTask(paymentGatewayTransactionData, savedTransactionStatus, paymentGatewayTaskHandler, currentRequestStatus, transactionId, paymentGatewayServiceProvider, paymentGatewayTransactionHandler);
        }else{
            logger.info("Current request {} status is not success : {}. Not performing system txn", transactionId, currentRequestStatus);
            paymentGatewayAmountTxnResponse = new PaymentGatewayAmountTxnResponse(paymentGatewayTransactionData.getTransactionType(), false, null, null, PAYMENT_NOT_SUCCESS_STATE, "Not success status received", null);
        }

        Map<String, Object> metadata = getUpdatedTransactionMetadata(paymentGatewayTransactionData, paymentGatewayAmountTxnResponse,
                paymentGatewayTransactionHandler.isWebhook(), paymentGatewayTransactionHandler.getWebhookData());

        /**
         * Log transaction processing updates and release the lock
         */
        logTransactionUpdate(paymentGatewayTransactionData, currentRequestStatus, transactionId, paymentGatewayAmountTxnResponse, metadata);

        if (paymentGatewayAmountTxnResponse.getErrorCode() == SUCCESS_CODE && includeSystemTransactionDetails){
            logger.info("Transaction handled successfully, {} with code {}. Fetching system transaction details", transactionId, paymentGatewayAmountTxnResponse.getErrorCode());
            PaymentGatewaySystemTransactionDetails paymentGatewaySystemTransactionDetails = paymentGatewayTaskHandler.getSystemTransactionDetails(paymentGatewayTransactionData);
            return new PGProcessTransactionResponse(true, paymentGatewayAmountTxnResponse.getErrorCode(), transactionId, paymentGatewayTransactionData.getTransactionToken(), currentRequestStatus, paymentGatewayServiceProvider, paymentGatewayAmountTxnResponse.getTransactionType(), paymentGatewayAmountTxnResponse.getMessage(), paymentGatewayTransactionData.getPaymentGatewayAmount(), paymentGatewayTransactionData.getWalletAmount(), paymentGatewayAmountTxnResponse, paymentGatewaySystemTransactionDetails);
        }

        if (paymentGatewayAmountTxnResponse.getErrorCode() == SUCCESS_CODE ||
                paymentGatewayAmountTxnResponse.getErrorCode() == PAYMENT_SUCCESS_TXN_FAILURE_WALLET_DEPOSIT_SUCCESS ||
                paymentGatewayAmountTxnResponse.getErrorCode() == PAYMENT_SUCCESS_TXN_FAILURE_WALLET_DEPOSIT_FAILURE ||
                paymentGatewayAmountTxnResponse.getErrorCode()  == PAYMENT_NOT_SUCCESS_STATE) {
            logger.info("Transaction handled successfully, {} with code {}", transactionId, paymentGatewayAmountTxnResponse.getErrorCode());
            return new PGProcessTransactionResponse(true, paymentGatewayAmountTxnResponse.getErrorCode(), transactionId, paymentGatewayTransactionData.getTransactionToken(), currentRequestStatus, paymentGatewayServiceProvider, paymentGatewayAmountTxnResponse.getTransactionType(), paymentGatewayAmountTxnResponse.getMessage(), paymentGatewayTransactionData.getPaymentGatewayAmount(), paymentGatewayTransactionData.getWalletAmount(), paymentGatewayAmountTxnResponse, null);
        }

        logger.info("Transaction handling failed {} with code {}", transactionId, paymentGatewayAmountTxnResponse.getErrorCode());
        return PGProcessTransactionResponse.forFailure(paymentGatewayAmountTxnResponse.getErrorCode(), paymentGatewayAmountTxnResponse.getMessage());
    }

    private PaymentGatewayAmountTxnResponse handlePaymentGatewayTask(PaymentGatewayTransactionData paymentGatewayTransactionData, PaymentGatewayTransactionStatus savedTransactionStatus, AbstractPaymentGatewayTaskHandler paymentGatewayTaskHandler, PaymentGatewayTransactionStatus currentRequestStatus, UUID transactionId, PaymentGatewayServiceProvider paymentGatewayServiceProvider, IPaymentGatewayTransactionHandler paymentGatewayTransactionHandler) {
        if (paymentGatewayTaskHandler == null) {
            logger.error("No task handler found for paymentGatewayTransactionData {}", paymentGatewayTransactionData);
            //TODO : if status is success should we add amount in wallet?
            return new PaymentGatewayAmountTxnResponse(paymentGatewayTransactionData.getTransactionType(), false, null, null, TASK_HANDLER_NOT_FOUND, "No task handler found", null);
        }
        return paymentGatewayTaskHandler.handleSuccessResponse(paymentGatewayTransactionData);
    }

    private PGProcessTransactionResponse processOrAcquireLock(int instituteId, IPaymentGatewayTransactionHandler paymentGatewayTransactionHandler, UUID transactionId) throws InterruptedException {
        int count = 0;
        int maxCount = 5;
        long durationMilliSeconds = 2000;

        while (count < maxCount) {
            try {
                return acquireLockInTransaction(transactionId, instituteId, paymentGatewayTransactionHandler);
            } catch (EmbrateRunTimeException e) {
                logger.error("Unable to get lock on resource {} : ", transactionId, e);
            } catch (Exception e) {
                logger.error("Error while getting lock on resource {}", transactionId, e);
            }
            if (count < maxCount - 1) {
                Thread.sleep(durationMilliSeconds);
            }
            count++;
        }
        logger.error("Could not get lock on resource {} after attempting {} times with duration {}. Skipping request...", transactionId, count, durationMilliSeconds);
        return PGProcessTransactionResponse.forFailure(UNABLE_TO_GET_TRANSACTION_LOCK, "Unable to get transaction lock");
    }

    private PGProcessTransactionResponse acquireLockInTransaction(UUID transactionId, int instituteId, IPaymentGatewayTransactionHandler paymentGatewayTransactionHandler) {
        return transactionTemplate.execute(new TransactionCallback<PGProcessTransactionResponse>() {
            @Override
            public PGProcessTransactionResponse doInTransaction(TransactionStatus status) {
                PaymentGatewayTransactionData paymentGatewayTransactionData = paymentGatewayTransactionsDao.getTransaction(transactionId, DBLockMode.FOR_UPDATE);
                if (paymentGatewayTransactionData == null || paymentGatewayTransactionData.getTransactionId() == null || paymentGatewayTransactionData.getInstituteId() <= 0 || paymentGatewayTransactionData.getUserId() == null || paymentGatewayTransactionData.getUserType() == null || paymentGatewayTransactionData.getMetadata() == null) {
                    logger.error("Invalid transaction for instituteId {}, id {}, {}", instituteId, transactionId, paymentGatewayTransactionData);
                    return PGProcessTransactionResponse.forFailure(INVALID_TRANSACTION_ID, "Invalid transaction id");
                }

                boolean lockAcquired = paymentGatewayTransactionData.isLockAcquired();
                if (lockAcquired) {
                    logger.info("Lock already acquired on the resource {} by some other client", transactionId);
                    throw new EmbrateRunTimeException("Lock already acquired on the resource by some other client");
                }
                if (paymentGatewayTransactionsDao.updateTransactionLock(transactionId, true)) {
                    return null;
                }
                logger.info("Unable to get lock on the resource {}", transactionId);
                throw new EmbrateRunTimeException("Unable to get lock on the resource");
            }
        });
    }

    private void logTransactionUpdate(PaymentGatewayTransactionData paymentGatewayTransactionData, PaymentGatewayTransactionStatus currentRequestStatus, UUID transactionId, PaymentGatewayAmountTxnResponse paymentGatewayAmountTxnResponse, Map<String, Object> metadata) {

        RetryableCallable<Boolean> updateTransactionState = new RetryableCallable<Boolean>() {
            @Override
            public Boolean retryableCall() throws Exception {
                boolean logSuccess = paymentGatewayTransactionsDao.updateTransaction(transactionId, currentRequestStatus, paymentGatewayAmountTxnResponse.getErrorCode(), paymentGatewayAmountTxnResponse.getMessage(), metadata);
                if(!logSuccess){
                    logger.error("Unable to log transaction update for user {}, institute {}, txn id {}, data {}, paymentGatewayAmountTxnResponse {}", paymentGatewayTransactionData.getUserId(), paymentGatewayTransactionData.getInstituteId(), paymentGatewayTransactionData.getTransactionId(), paymentGatewayTransactionData, paymentGatewayAmountTxnResponse);
                    throw new EmbrateRunTimeException("Unable to log transaction update");
                }
                logger.info("Successfully logged transaction update for user {}, institute {}, txn id {}, paymentGatewayAmountTxnResponse {}", paymentGatewayTransactionData.getUserId(), paymentGatewayTransactionData.getInstituteId(), paymentGatewayTransactionData.getTransactionId(), paymentGatewayAmountTxnResponse);
                return true;
            }
        };

        try {
            updateTransactionState.call();
        } catch (Exception e) {
            /**
             * If transaction state is not successfully logged in db, entire transaction need not to be reverted
             * bcz lock acquire state of transaction is also not modified and no other process can work on this transaction
             * until lock is released
             */
            logger.error("Unable to log transaction update for user {}, institute {}, txn id {}, data {}, paymentGatewayAmountTxnResponse {}", paymentGatewayTransactionData.getUserId(), paymentGatewayTransactionData.getInstituteId(), paymentGatewayTransactionData.getTransactionId(), paymentGatewayTransactionData, paymentGatewayAmountTxnResponse, e);
        }
    }

    private void releaseLogTransactionLock(UUID transactionId) {

        RetryableCallable<Boolean> updateTransactionState = new RetryableCallable<Boolean>() {
            @Override
            public Boolean retryableCall() throws Exception {
                boolean logSuccess = paymentGatewayTransactionsDao.updateTransactionLock(transactionId, false);
                if(!logSuccess){
                    logger.error("Unable to release lock on transaction for txn id {}", transactionId);
                    throw new EmbrateRunTimeException("Unable to release lock");
                }
                logger.info("Successfully released lock for transaction txn id {}", transactionId);
                return true;
            }
        };

        try {
            updateTransactionState.call();
        } catch (Exception e) {
            logger.error("Unable to release lock on transaction for txn id {}", transactionId, e);
        }
    }

    private Map<String, Object> getUpdatedTransactionMetadata(PaymentGatewayTransactionData paymentGatewayTransactionData,
                                                              PaymentGatewayAmountTxnResponse paymentGatewayAmountTxnResponse,
                                                              boolean webhook, String webhookData) {
        Map<String, Object> metadata = paymentGatewayTransactionData.getMetadata();
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        if (!metadata.containsKey(PAYMENT_GATEWAY_AMOUNT_TXN_RESPONSE)) {
            metadata.put(PAYMENT_GATEWAY_AMOUNT_TXN_RESPONSE, new ArrayList<>());
        }


        List<String> txnResponseList = (List<String>) metadata.get(PAYMENT_GATEWAY_AMOUNT_TXN_RESPONSE);
        txnResponseList.add(SharedConstants.GSON.toJson(paymentGatewayAmountTxnResponse));

        if(webhook){
            if (!metadata.containsKey(PAYMENT_GATEWAY_WEBHOOK_DATA)) {
                metadata.put(PAYMENT_GATEWAY_WEBHOOK_DATA, new ArrayList<>());
            }

            List<String> webhookList = (List<String>) metadata.get(PAYMENT_GATEWAY_WEBHOOK_DATA);
            webhookList.add(webhookData);
        }

        return metadata;
    }

    private InitiateTransactionDataWrapper getInitiateTransactionDataWrapper(int instituteId, PGInitiateTransactionPayload initiateTransactionPayload, MetaDataPreferences metaDataPreferences) {
        if (!isValidTransactionInitiateRequest(instituteId, initiateTransactionPayload)) {
            logger.error("Invalid payment request for institute {}, {}", instituteId, initiateTransactionPayload);
            throw new PaymentGatewayRuntimeException(INVALID_PAYLOAD, "Invalid payment request");
        }

        if (!metaDataPreferences.isPaymentGatewayServiceEnabled()) {
            logger.error("Payment gateway service not enabled for institute {}", instituteId);
            throw new PaymentGatewayRuntimeException(PG_DISABLED, "Payment gateway service not enabled for institute");
        }

        PaymentGatewayServiceProvider paymentGatewayServiceProvider = metaDataPreferences.getPaymentGatewayServiceProvider();
        if (paymentGatewayServiceProvider == null) {
            logger.error("No payment gateway service provider configured for institute {}", instituteId);
            throw new PaymentGatewayRuntimeException(PG_PROVIDER_NOT_CONFIGURED, "No payment gateway service provider configured for institute");
        }

        if(initiateTransactionPayload.getTransactionType() == STUDENT_FEE_PAYMENT){

            //currently, we are only restricting the student from paying school fees.
            final Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, initiateTransactionPayload.getStudentFeePaymentMetadata().getAcademicSessionId(), initiateTransactionPayload.getUserId());
            boolean restrictionAction = RestrictionUtils.isActionRestricted(student, TaggedActions.FEE_PAYMENT_RESTRICTED, false);
            if (restrictionAction) {
                logger.error("Payment restricted for student {}, institute {}", initiateTransactionPayload.getUserId(), instituteId);
                throw new PaymentGatewayRuntimeException(STUDENT_PAYMENT_PAYMENT_RESTRICTED, "Payment restricted for this student.");
            }

            boolean restrictFutureSessionPayment = feePaymentManager.restrictSessionFeesPayment(
                    instituteId, initiateTransactionPayload.getStudentFeePaymentMetadata().getAcademicSessionId(), initiateTransactionPayload.getUserId());

            if(restrictFutureSessionPayment) {
                logger.error("Cannot make future session payment when past session dues are present for user {}, institute {}", initiateTransactionPayload.getUserId(), instituteId);
                throw new PaymentGatewayRuntimeException(FUTURE_SESSION_PAYMENT_RESTRICTED, "Cannot make future session payment when past session dues are present.");
            }

            boolean walletAmountUsed = initiateTransactionPayload.getWalletAmount() != 0;
            boolean restrictStudentUseWalletFeesPayment = metaDataPreferences.isRestrictStudentUseWalletFeesPayment();

            if(restrictStudentUseWalletFeesPayment && walletAmountUsed) {
                logger.error("Cannot use wallet amount to pay school fees for user {}, institute {}", initiateTransactionPayload.getUserId(), instituteId);
                throw new PaymentGatewayRuntimeException(FUTURE_SESSION_PAYMENT_RESTRICTED, "Cannot use wallet amount to pay school fees.");
            }

        }

        PGUser pgUser = null;
        String mobileNumber = null;
        String email = null;

        switch (initiateTransactionPayload.getTransactionType()) {
            case STUDENT_REGISTRATION:
                RegistrationStudent registrationStudent = studentRegistrationDao.getRegistrationStudent(initiateTransactionPayload.getUserId());
                pgUser = new PGUser(registrationStudent.getStudentRegistrationId(), UserType.REGISTRATION_STUDENT, registrationStudent.getStudentBasicInfo().getName(), registrationStudent.getStudentBasicInfo().getPrimaryEmail(), registrationStudent.getStudentBasicInfo().getPrimaryContactNumber());
                break;
            default:
                User user = userManager.getUser(initiateTransactionPayload.getUserId());
                if (user == null) {
                    logger.error("Invalid user {}, {}, {}", initiateTransactionPayload.getUserId(), instituteId, paymentGatewayServiceProvider);
                    throw new PaymentGatewayRuntimeException(INVALID_PAYLOAD, "Invalid user");
                }

                if (user.getUserType() == UserType.STUDENT) {
                    Student student = studentManager.getStudentWithoutSession(user.getUuid());
                    if (student == null) {
                        logger.error("Invalid student {}, {}, {}", initiateTransactionPayload.getUserId(), instituteId, paymentGatewayServiceProvider);
                        throw new PaymentGatewayRuntimeException(INVALID_PAYLOAD, "Invalid user");
                    }
                    pgUser = new PGUser(student.getStudentId(), UserType.STUDENT, student.getStudentBasicInfo().getName(), student.getStudentBasicInfo().getPrimaryEmail(),  student.getStudentBasicInfo().getPrimaryContactNumber());
                } else {
                    pgUser = new PGUser(user.getUuid(), user.getUserType(), user.getFullName(), user.getEmail(), user.getPhoneNumber());
                }
                break;
        }


        if(pgUser == null || StringUtils.isBlank(pgUser.getContactNumber()) || StringUtils.isBlank(pgUser.getEmail())){
            logger.error("Mobile number or email address is empty for user id {}, instituteId {}", initiateTransactionPayload.getUserId(), instituteId);
            throw new PaymentGatewayRuntimeException(EMPTY_EMAIL_OR_MOBILE_NUMBER, "Mobile number or email address is not set.");
        }

        PGMerchantDetails merchantDetails = paymentGatewayMerchantManager.getPaymentMerchant(instituteId, paymentGatewayServiceProvider);
        if (merchantDetails == null) {
            logger.error("No active merchant found for request, {}, {}", instituteId, paymentGatewayServiceProvider);
            throw new PaymentGatewayRuntimeException(ACTIVE_MERCHANT_NOT_AVAILABLE, "No active merchant found");
        }

        AbstractPaymentGatewayTaskHandler paymentGatewayTaskHandler = paymentGatewayTaskHandlerFactory.getPaymentGatewayTaskHandler(initiateTransactionPayload.getTransactionType());
        if (paymentGatewayTaskHandler == null) {
            logger.error("No task handler found for request, {}, {}, type {}", instituteId, paymentGatewayServiceProvider, initiateTransactionPayload.getTransactionType());
            throw new PaymentGatewayRuntimeException(TASK_HANDLER_NOT_FOUND, "No task handler found");
        }

        if (!paymentGatewayTaskHandler.validInitiateTransactionPayload(instituteId, initiateTransactionPayload)) {
            logger.error("Task handler validation failed for request, {}, {}", instituteId, initiateTransactionPayload);
            throw new PaymentGatewayRuntimeException(INVALID_PAYLOAD, "Invalid request");
        }

        // TODO : Check how to handle non integer payment? Fee wont accept any greater amount even in fractions. So need to round/ceil accordingly
        return new InitiateTransactionDataWrapper(paymentGatewayServiceProvider, pgUser, merchantDetails, paymentGatewayTaskHandler, mobileNumber, email);
    }

    private PGTransactionTokenResponse executeInitiateTransactionRequest(int instituteId, PGInitiateTransactionPayload initiateTransactionPayload, UUID transactionId, InitiateTransactionDataWrapper initiateTransactionDataWrapper,
                                                                         PGPlatform pgPlatform, PGUser user, String webhookURL) {
        final PGTransactionTokenResponse transactionTokenResponse = transactionTemplate.execute(new TransactionCallback<PGTransactionTokenResponse>() {
            @Override
            public PGTransactionTokenResponse doInTransaction(TransactionStatus status) {
                double walletAmount = initiateTransactionPayload.getWalletAmount();
                if (Double.compare(walletAmount, 0d) > 0) {
                    validateWalletAmount(instituteId, walletAmount, initiateTransactionPayload);
                }

                IPaymentGatewayService paymentGatewayService = paymentGatewayServiceProviderFactory.getPaymentGatewayService(initiateTransactionDataWrapper.getServiceProvider());

                PGTransactionTokenResponse transactionTokenResponse = paymentGatewayService.generateTransactionToken(
                        new PGTransactionTokenPayload(transactionId.toString(), initiateTransactionPayload.getPaymentGatewayAmount(),
                                initiateTransactionPayload.getCurrency(), initiateTransactionPayload.getPaymentGatewayStage(),
                                new PGUserData(user.getUserId().toString(), user.getName(), user.getEmail(),
                                        user.getContactNumber()), webhookURL, initiateTransactionPayload.getCallbackUrl()),
                        initiateTransactionDataWrapper.getMerchantDetails(), pgPlatform);
                if (transactionTokenResponse == null || !transactionTokenResponse.isSuccess()) {
                    logger.error("Error response to generate token from payment gateway service {} for payload {}", initiateTransactionDataWrapper.getServiceProvider(), initiateTransactionPayload);
                    throw new PaymentGatewayRuntimeException(PG_TOKEN_ERROR, "Unable to generate token for the request");
                }

                Map<String, Object> metadata = new HashMap<>();

                AbstractPaymentGatewayTaskHandler paymentGatewayTaskHandler = initiateTransactionDataWrapper.getPaymentGatewayTaskHandler();
                paymentGatewayTaskHandler.updateInitiateTransactionMetadata(metadata, initiateTransactionPayload);

                PaymentGatewayTransactionData paymentGatewayTransactionData = new PaymentGatewayTransactionData(instituteId,
                        initiateTransactionDataWrapper.getServiceProvider(),
                        initiateTransactionPayload.getUserId(), initiateTransactionDataWrapper.getUser().getUserType(),
                        transactionId,
                        initiateTransactionDataWrapper.getMerchantDetails().getMerchantId(),
                        initiateTransactionPayload.getTransactionType(),
                        initiateTransactionPayload.getPaymentGatewayAmount(),
                        initiateTransactionPayload.getWalletAmount(),
                        initiateTransactionPayload.getInstantDiscountAmount(),
                        initiateTransactionPayload.getFineAmount(),
                        initiateTransactionPayload.getCurrency(),
                        initiateTransactionPayload.getPaymentGatewayStage(),
                        transactionTokenResponse.getTransactionToken(),
                        PaymentGatewayTransactionStatus.INITIATED,
                        metadata,
                        initiateTransactionPayload.getDescription());

                boolean transactionAdded = paymentGatewayTransactionsDao.addTransaction(paymentGatewayTransactionData);

                if (!transactionAdded) {
                    logger.error("Error while adding the transaction for payload {}", paymentGatewayTransactionData);
                    throw new PaymentGatewayRuntimeException(INTERNAL_ERROR, "Error while adding transaction data");
                }

                return transactionTokenResponse;
            }

        });
        return transactionTokenResponse;
    }

    private void validateWalletAmount(int instituteId, double walletAmount, PGInitiateTransactionPayload initiateTransactionPayload) {
        Double currentWalletAmount = userWalletManager.getWalletAmount(initiateTransactionPayload.getUserId(), null);
        if (currentWalletAmount == null) {
            logger.error("Unable to get wallet amount for student. instituteId {}, {}", instituteId, initiateTransactionPayload);
            throw new PaymentGatewayRuntimeException(INTERNAL_ERROR, "Requested wallet amount not available");
        }
        if (Double.compare(walletAmount, currentWalletAmount.doubleValue()) > 0) {
            logger.error("Requested wallet amount {} is greater than available amount {}. instituteId {}, {}", walletAmount, currentWalletAmount, instituteId, initiateTransactionPayload);
            throw new PaymentGatewayRuntimeException(REQUESTED_WALLET_AMOUNT_NOT_AVAILABLE,
                    "Requested wallet amount not available", initiateTransactionPayload.getWalletAmount(),
                    currentWalletAmount.doubleValue(),
                    initiateTransactionPayload.getPaymentGatewayAmount());
        }
    }


    private WalletTransactionCategory getWalletTransactionCategory(PGTransactionType transactionType) {
        switch (transactionType) {
            case STUDENT_FEE_PAYMENT:
                return WalletTransactionCategory.FEE_PAYMENT_WITHDRAWAL_FROM_WALLET;
            case WALLET_RECHARGE:
                return WalletTransactionCategory.RECHARGE;
        }
        return null;
    }

    private PGInitiateTransactionResponse getInitiateTransactionFailureResponse(int errorCode, String failureReason) {
        return PGInitiateTransactionResponse.forFailure(errorCode, failureReason);
    }

    private PGInitiateTransactionResponse getInitiateTransactionFailureResponse(int errorCode, String failureReason, Double requestedWalletAmount, Double currentWalletAmount, Double paymentGatewayAmount) {
        return PGInitiateTransactionResponse.forFailure(errorCode, failureReason, requestedWalletAmount, currentWalletAmount, paymentGatewayAmount);
    }

    private boolean isValidTransactionInitiateRequest(int instituteId, PGInitiateTransactionPayload initiateTransactionPayload) {
        return !(instituteId <= 0 || initiateTransactionPayload == null || initiateTransactionPayload.getUserId() == null ||
                initiateTransactionPayload.getTransactionType() == null || initiateTransactionPayload.getCurrency() == null ||
                initiateTransactionPayload.getPaymentGatewayStage() == null ||
                Double.compare(initiateTransactionPayload.getPaymentGatewayAmount(), 0d) <= 0 ||
                Double.compare(initiateTransactionPayload.getWalletAmount(), 0d) < 0);
    }

    private class InitiateTransactionDataWrapper {
        private final PaymentGatewayServiceProvider serviceProvider;
        private final PGUser user;
        private final PGMerchantDetails merchantDetails;
        private final AbstractPaymentGatewayTaskHandler paymentGatewayTaskHandler;
        private final String mobileNumber;
        private final String email;

        public InitiateTransactionDataWrapper(PaymentGatewayServiceProvider serviceProvider, PGUser user, PGMerchantDetails merchantDetails, AbstractPaymentGatewayTaskHandler paymentGatewayTaskHandler, String mobileNumber, String email) {
            this.serviceProvider = serviceProvider;
            this.user = user;
            this.merchantDetails = merchantDetails;
            this.paymentGatewayTaskHandler = paymentGatewayTaskHandler;
            this.mobileNumber = mobileNumber;
            this.email = email;
        }

        public PaymentGatewayServiceProvider getServiceProvider() {
            return serviceProvider;
        }

        public PGUser getUser() {
            return user;
        }

        public PGMerchantDetails getMerchantDetails() {
            return merchantDetails;
        }

        public AbstractPaymentGatewayTaskHandler getPaymentGatewayTaskHandler() {
            return paymentGatewayTaskHandler;
        }

        public String getMobileNumber() {
            return mobileNumber;
        }

        public String getEmail() {
            return email;
        }
    }

    private class ProcessTransactionDataWrapper {
        private final PaymentGatewayServiceProvider serviceProvider;
        private final IPaymentGatewayTransactionHandler paymentGatewayTransactionHandler;

        public ProcessTransactionDataWrapper(PaymentGatewayServiceProvider serviceProvider, IPaymentGatewayTransactionHandler paymentGatewayTransactionHandler) {
            this.serviceProvider = serviceProvider;
            this.paymentGatewayTransactionHandler = paymentGatewayTransactionHandler;
        }

        public PaymentGatewayServiceProvider getServiceProvider() {
            return serviceProvider;
        }

        public IPaymentGatewayTransactionHandler getPaymentGatewayTransactionHandler() {
            return paymentGatewayTransactionHandler;
        }
    }

    public PaymentGatewayTransactionData getTransactions(UUID transactionId) {
        return paymentGatewayTransactionsDao.getTransaction(transactionId, null);
    }

    public PaymentGatewayTransactionData getTransactionsByToken(String token) {
        return paymentGatewayTransactionsDao.getTransactionByToken(token, null);
    }

}
