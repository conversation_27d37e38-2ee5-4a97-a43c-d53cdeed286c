
package com.embrate.cloud.core.lib.examination;

import com.embrate.cloud.core.api.courses.config.CloneCoursesResponse;
import com.embrate.cloud.core.api.courses.config.CoursesCloneStatus;
import com.embrate.cloud.core.api.examination.config.ExamStructureCloneStatus;
import com.embrate.cloud.core.api.examination.config.ExamStructureCoursesResponse;
import com.embrate.cloud.core.utils.institute.StandardUtils;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.examination.config.CloneClassExamRequest;
import com.lernen.cloud.core.api.examination.config.DeleteExamStructureRequest;
import com.lernen.cloud.core.api.examination.config.UpdateExamParentRequest;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.embrate.cloud.core.lib.courses.CourseManager;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.lib.examination.ExaminationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.embrate.cloud.core.utils.EMapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.Map.Entry;


/**
 * <AUTHOR>
 */
public class ExamConfigManager {
    private static final Logger logger = LogManager.getLogger(ExamConfigManager.class);

    private final ExaminationManager examinationManager;
    private final CourseManager courseManager;
    private final InstituteManager instituteManager;

    private final TransactionTemplate transactionTemplate;

    public ExamConfigManager(ExaminationManager examinationManager, CourseManager courseManager, InstituteManager instituteManager,
                             TransactionTemplate transactionTemplate) {
        this.examinationManager = examinationManager;
        this.courseManager = courseManager;
        this.instituteManager = instituteManager;
        this.transactionTemplate = transactionTemplate;
    }

    public ExamStructureCoursesResponse cloneClassExamStructure(CloneClassExamRequest cloneClassExamRequest) {
        int srcInstituteId = cloneClassExamRequest.getSrcInstituteId();
        int destInstituteId = cloneClassExamRequest.getDestInstituteId();
        if (srcInstituteId <= 0 || destInstituteId <= 0) {
            logger.error("Invalid srcInstituteId {} or destInstituteId {} ", srcInstituteId, destInstituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid src or dest institute id"));

        }
        int srcAcademicSessionId = cloneClassExamRequest.getSrcAcademicSessionId();
        int destAcademicSessionId = cloneClassExamRequest.getDestAcademicSessionId();
        if (srcAcademicSessionId <= 0 || destAcademicSessionId <= 0) {
            logger.error("Invalid src {} or dest {} session", srcAcademicSessionId, destAcademicSessionId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid src or dest session id"));

        }
        Map<String, Standard> srcStandardMap = getStandardMap(srcInstituteId, srcAcademicSessionId);
        UUID srcStandardId = srcStandardMap.get(cloneClassExamRequest.getSrcStandardName().toLowerCase()).getStandardId();
        if(srcStandardId == null){
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Src Standard Name Doesn't Exist"));
        }
        Map<String, Standard> destStandardMap = getStandardMap(destInstituteId, destAcademicSessionId);
        Map<UUID, String> destStandardNameResponse = new HashMap<>();
        List<UUID> destStandardIdList = new ArrayList<>();
        for(String DestStandardName : cloneClassExamRequest.getDestStandardName()){
            UUID standardId = destStandardMap.get(DestStandardName.toLowerCase()).getStandardId();
            if(standardId == null){
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Dest Standard Name Doesn't Exist"));
            }
            destStandardIdList.add(standardId);
            destStandardNameResponse.put(standardId, DestStandardName);
        }
        List<ExamNode> srcClassExamsForest = examinationManager.getClassExamsForest(srcStandardId, srcAcademicSessionId, srcInstituteId, false);
        if (CollectionUtils.isEmpty(srcClassExamsForest)) {
            logger.warn("No exam configured in source class. srcInstituteId {}, cloneClassExamRequest {}", srcInstituteId, cloneClassExamRequest);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No exam configured in source class"));
        }



        // Assuming single root node only ("exam structure")
        ExamNode srcRootNode = srcClassExamsForest.get(0);
        if (CollectionUtils.isEmpty(srcRootNode.getChildren())) {
            logger.warn("No exams configured under src system root node {}", srcRootNode);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No exams configured under src system root node"));
        }

        List<ExamDetails> srcExamDetailsList = examinationManager.getExamDetails(srcInstituteId, srcAcademicSessionId, srcStandardId);

        //This is to check Dimension are present in dest institutes
        List<ExamDimension> destExamDimensionsList = examinationManager.getExamDimensions(destInstituteId);

        HashMap<String, ExamDimension> dimensionNameMap = new HashMap<>();
        for (ExamDimension examDimension : destExamDimensionsList) {
            dimensionNameMap.put(examDimension.getDimensionName().toLowerCase(), examDimension);
        }

        Map<UUID, ExamDetails> examDetailsMap = new HashMap<>();
        for (ExamDetails examDetails : srcExamDetailsList) {
            if(srcInstituteId != destInstituteId) {
                Map<CourseType, List<ExamCourse>> examCourseMap = examDetails.getCourseMarksMatrix();
                for (Entry<CourseType, List<ExamCourse>> courses : examCourseMap.entrySet()) {
                    if (!CollectionUtils.isEmpty(courses.getValue())) {
                        for (ExamCourse examCourse : courses.getValue()) {
                            for (ExamDimensionValues examDimensionValues : examCourse.getExamDimensionValues()) {
                                String dimensionName = examDimensionValues.getExamDimension().getDimensionName().toLowerCase();
                                if (!dimensionNameMap.containsKey(dimensionName)) {
                                    logger.error("Dimension not found {}", examDimensionValues.getExamDimension().getDimensionName());
                                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Dimension are not same in source and the destination institute"));
                                }
                                examDimensionValues.setExamDimension(dimensionNameMap.get(dimensionName.toLowerCase()));
                            }
                        }
                    }
                }
                for (Entry<CourseType, List<ExamDimensionValues>> entry : examDetails.getExamDimensionValues().entrySet()) {
                    for (ExamDimensionValues examDimensionValues : entry.getValue()) {
                        if (!dimensionNameMap.containsKey(examDimensionValues.getExamDimension().getDimensionName().toLowerCase())) {
                            logger.error("Dimension not found {}", examDimensionValues.getExamDimension().getDimensionName());
                            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Dimension are not same in source and the destination institute"));
                        }
                        examDimensionValues.setExamDimension(dimensionNameMap.get(examDimensionValues.getExamDimension().getDimensionName().toLowerCase()));
                    }
                }
            }
            examDetailsMap.put(examDetails.getExamMetaData().getExamId(), examDetails);
        }

        List<String> errorClasses = new ArrayList<>();
        List<String> successClasses = new ArrayList<>();
        List<String> examStructureAlreadyPresentClasses = new ArrayList<>();


        for (UUID destStandardId : destStandardIdList) {
            String destStandardName = destStandardNameResponse.get(destStandardId);
            try {
                List<ExamNode> destClassExamsForest = examinationManager.getClassExamsForest(destStandardId, destAcademicSessionId, destInstituteId, false);
                if (CollectionUtils.isEmpty(destClassExamsForest)) {
                    logger.warn("No exam configured in dest class. destInstituteId {}, cloneClassExamRequest {}", destInstituteId, cloneClassExamRequest);
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No exam configured in destination class"));
                }
                ExamNode destRootNode = destClassExamsForest.get(0);
                if (CollectionUtils.isNotEmpty(destRootNode.getChildren())) {
                    logger.warn("Exams already configured under dest system root node {}", destRootNode);
                    examStructureAlreadyPresentClasses.add(destStandardName);
                }

                for (ExamNode srcChild : srcRootNode.getChildren()) {
                    Boolean success =cloneExam(destInstituteId, destAcademicSessionId, destStandardId, srcChild, destRootNode.getExamMetaData().getExamId(), examDetailsMap, cloneClassExamRequest.isCloneOnlyForScholasticSourceCourses(), cloneClassExamRequest.isCloneOnlyForCoScholasticSourceCourses());
                    if (success != null && success) {
                        successClasses.add(destStandardName);
                    } else {
                        errorClasses.add(destStandardName);
                    }
                }
            } catch (Exception e) {
                logger.error("Error while copying the courses for standardId {}, destInstituteId {}, request {}", destStandardId, destInstituteId, cloneClassExamRequest);
                errorClasses.add(destStandardName);
            }

        }

        boolean failures = CollectionUtils.isNotEmpty(errorClasses);
        boolean success = CollectionUtils.isNotEmpty(successClasses);
        ExamStructureCloneStatus status = null;
        if (failures && success) {
            status = ExamStructureCloneStatus.PARTIAL_SUCCESS;
        } else if (success) {
            status = ExamStructureCloneStatus.COMPLETE_SUCCESS;
        } else {
            status = ExamStructureCloneStatus.COMPLETE_FAILURE;
        }

        return new ExamStructureCoursesResponse(status, cloneClassExamRequest, successClasses, examStructureAlreadyPresentClasses, errorClasses);
    }

    public boolean updateExamParent(int instituteId, UpdateExamParentRequest request) {
        if (instituteId <= 0 || request == null || request.getAcademicSessionId() <= 0 ||
                MapUtils.isEmpty(request.getNewExamMapping()) || StringUtils.isBlank(request.getClassName())) {
            logger.error("Invalid request for instituteId {}, request {}", instituteId, request);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request to update exam parent"));
        }
        int academicSessionId = request.getAcademicSessionId();
        Standard standard = getStandard(instituteId, academicSessionId, request.getClassName());
        if (standard == null) {
            logger.error("Invalid standard provided for instituteId {}, request {}", instituteId, request);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid class name to update exam parent"));
        }

        Set<String> newParents = new HashSet<>();
        for(List<String> examParents : request.getNewExamMapping().values()){
            if(CollectionUtils.isEmpty(examParents)){
                logger.error("No parent exam defined for instituteId {}, request {}", instituteId, request);
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No parent exam defined"));
            }
            newParents.addAll(examParents);
        }

        Set<String> changingExams = request.getNewExamMapping().keySet();
        List<ExamMetaData> examMetaDataList = examinationManager.getExamMetadataByStandard(instituteId, academicSessionId, standard.getStandardId());
        if (CollectionUtils.isEmpty(examMetaDataList)) {
            logger.error("No exams configured for instituteId {}, request {}", instituteId, request);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No exams configured for class"));
        }
        Map<String, ExamMetaData> examMetaDataMap = getExamMetadataMap(examMetaDataList);

        for (String changingExam : changingExams) {
            String key = changingExam.trim().toLowerCase();
            if (!examMetaDataMap.containsKey(key)) {
                logger.error("Invalid exam {} for instituteId {}, request {}", changingExam, instituteId, request);
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid exam `" + changingExam + "` provided for class"));
            }

            if (examMetaDataMap.get(key).getExamType() == ExamType.SYSTEM) {
                logger.error("System exam parent cannot be changed {} for instituteId {}, request {}", changingExam, instituteId, request);
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "System exam parent cannot be changed `" + changingExam + "` provided for class"));
            }
        }

        for (String newParent : newParents) {
            String key = newParent.trim().toLowerCase();
            if (!examMetaDataMap.containsKey(key)) {
                logger.error("Invalid exam {} for instituteId {}, request {}", newParent, instituteId, request);
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid exam `" + newParent + "` provided for class"));
            }
            ExamMetaData examMetaData = examMetaDataMap.get(key);
            // TODO : We can avoid checking the marks for already existing parent in the list as effectively it wont be changing
            List<StudentExamMarksDetails> existingMarks = examinationManager.getClassMarks(instituteId, examMetaData.getExamId(), null, false);
            if (CollectionUtils.isNotEmpty(existingMarks)) {
                boolean marksFilled = isMarksFilled(existingMarks);
                if(marksFilled){
                    logger.error("Marks already filled in new parent exam {} for instituteId {}, request {}", newParent, instituteId, request);
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Marks already filled in `" + newParent + "` provided for class"));
                }
            }
        }
        boolean success = true;
        for (Entry<String, List<String>> entry : request.getNewExamMapping().entrySet()) {
            try {
                ExamMetaData childExam = examMetaDataMap.get(entry.getKey().trim().toLowerCase());
                List<UUID> parentExamIds = new ArrayList<>();
                for(String parentExamName : entry.getValue()){
                    ExamMetaData parentExam = examMetaDataMap.get(parentExamName.trim().toLowerCase());
                    parentExamIds.add(parentExam.getExamId());
                }
                success = examinationManager.updateParentMapping(instituteId, childExam.getExamId(), parentExamIds) && success;
            } catch (Exception e) {
                logger.error("Error while updating parent exam id for institute {} child {}, parent {}", instituteId, entry.getKey(), entry.getValue(), e);
            }
        }

        return success;
    }

    public boolean deleteClassExamStructure(int instituteId, DeleteExamStructureRequest request) {
        if (instituteId <= 0 || request == null || request.getAcademicSessionId() <= 0 ||
                StringUtils.isBlank(request.getClassName())) {
            logger.error("Invalid request for instituteId {}, request {}", instituteId, request);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request to delete exam structure"));
        }

        int academicSessionId = request.getAcademicSessionId();
        Standard standard = getStandard(instituteId, academicSessionId, request.getClassName());
        if (standard == null) {
            logger.error("Invalid standard provided for instituteId {}, request {}", instituteId, request);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid class name to delete exam structure"));
        }

        List<ExamNode> examNodeList = examinationManager.getClassExamsForest(standard.getStandardId(), academicSessionId, instituteId, false);
        if (CollectionUtils.isEmpty(examNodeList)) {
            logger.error("No exam structure is setup for standard {}, academicSessionId {}, instituteId {}. Skipping delete", standard.getStandardId(), academicSessionId, instituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No exam structure is setup to delete"));
        }

        //Assuming only single default exam structure node in forest
        ExamNode rootNode = examNodeList.get(0);
        if (CollectionUtils.isEmpty(rootNode.getChildren())) {
            logger.error("No exam structure children is setup for standard {}, academicSessionId {}, instituteId {}. Skipping delete", standard.getStandardId(), academicSessionId, instituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No exam structure children is setup to delete"));
        }

        validateMarksFilledForGraph(instituteId, rootNode);

        try {
            return transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus transactionStatus) {
                    deleteExamGraph(instituteId, rootNode);
                    return true;
                }
            });
        } catch (EmbrateRunTimeException e) {
            logger.error("Internal error while deleting exam graph for instituteId {}, rootNode {}", instituteId, rootNode.getExamMetaData().getExamId(), e);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, e.getMessage()));
        } catch (Exception e) {
            logger.error("Error while deleting  exam graph for instituteId {}, rootNode {}", instituteId, rootNode.getExamMetaData().getExamId(), e);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, e.getMessage()));
        }
    }

    private void validateMarksFilledForGraph(int instituteId, ExamNode node) {
        List<StudentExamMarksDetails> existingMarks = examinationManager.getClassMarks(instituteId, node.getExamMetaData().getExamId(), null, false);
        boolean marksFilled = CollectionUtils.isEmpty(existingMarks) ? false : isMarksFilled(existingMarks);
        if(marksFilled){
            logger.error("Marks already filled in exam {} for instituteId {}, request {}", node.getExamMetaData().getExamName(), instituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Marks already filled in `" + node.getExamMetaData().getExamName() + "` provided for class"));
        }

        if (CollectionUtils.isEmpty(node.getChildren())) {
            // Leaf node
            return;
        }

        for (ExamNode child : node.getChildren()) {
            validateMarksFilledForGraph(instituteId, child);
        }
    }

    private void deleteExamGraph(int instituteId, ExamNode node) {
        if (CollectionUtils.isNotEmpty(node.getChildren())) {
            for (ExamNode child : node.getChildren()) {
                deleteExamGraph(instituteId, child);
            }
        }

        if(node.getExamMetaData().getExamType() != ExamType.SYSTEM){
            if(!examinationManager.deleteExamNonAtomic(instituteId, node.getExamMetaData().getExamId())){
                logger.error("Unable to delete exam {}, instituteId {}", node.getExamMetaData().getExamId(), instituteId);
                throw new EmbrateRunTimeException("Unable to delete exam");
            }
        }
    }


    private static boolean isMarksFilled(List<StudentExamMarksDetails> existingMarks) {
        boolean marksFilled = false;
        for(StudentExamMarksDetails studentExamMarksDetails : existingMarks){
            for(ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesMarks()){
                for(ExamDimensionObtainedValues edv : examCourseMarks.getExamDimensionObtainedValues()){
                    if(edv.getObtainedMarks() != null || !(edv.getObtainedGrade() == null || edv.getObtainedGrade().getGradeId() == null) || edv.getAttendanceStatus() != null){
                        marksFilled = true;
                        break;
                    }
                }

            }
        }
        return marksFilled;
    }

    private Map<String, ExamMetaData> getExamMetadataMap(List<ExamMetaData> examMetaDataList) {
        return EMapUtils.getMap(examMetaDataList, new EMapUtils.MapFunction<ExamMetaData, String, ExamMetaData>() {
            @Override
            public String getKey(ExamMetaData entry) {
                return entry.getExamName().trim().toLowerCase();
            }

            @Override
            public ExamMetaData getValue(ExamMetaData entry) {
                return entry;
            }
        });
    }

    private Standard getStandard(int instituteId, int academicSessionId, String standardName) {
        Map<String, Standard> standardMap = getStandardMap(instituteId, academicSessionId);
        return standardMap.get(standardName.trim().toLowerCase());
    }

    private Map<String, Standard> getStandardMap(int instituteId, int academicSessionId) {
        List<Standard> standardList = instituteManager.getInstituteStandardDetails(instituteId, academicSessionId);
        return StandardUtils.getStandardMap(standardList);
    }

    private Boolean cloneExam(int destInstituteId, int destAcademicSessionId, UUID destStandardId, ExamNode node, UUID parentId, Map<UUID, ExamDetails> examDetailsMap, boolean cloneOnlyForScholasticSourceCourses, boolean cloneOnlyForCoScholasticSourceCourses) {
        UUID destExamId = createExamPayload(destInstituteId, destAcademicSessionId, destStandardId, examDetailsMap, node, parentId, cloneOnlyForScholasticSourceCourses, cloneOnlyForCoScholasticSourceCourses);
        if (destExamId == null) {
            logger.error("Unable to create exam skipping the flow");
            throw new EmbrateRunTimeException("Unable to create exam skipping the flow");
        }

        if (CollectionUtils.isEmpty(node.getChildren())) {
            // Leaf node
            // If the node is a leaf node, return true
            return true;
        }

        for (ExamNode child : node.getChildren()) {
            Boolean childSuccess = cloneExam(destInstituteId, destAcademicSessionId, destStandardId, child, destExamId, examDetailsMap, cloneOnlyForScholasticSourceCourses, cloneOnlyForCoScholasticSourceCourses);
            if ( childSuccess == null || !childSuccess) {
                return false; // If any child fails, return false
            }
        }
        return true; // All children succeeded
    }

    private UUID createExamPayload(int destInstituteId, int destAcademicSessionId, UUID destStandardId, Map<UUID, ExamDetails> examDetailsMap, ExamNode srcNode, UUID parentId, boolean cloneOnlyForScholasticSourceCourses, boolean cloneOnlyForCoScholasticSourceCourses) {
        try {
            ExamDetails examDetails = examDetailsMap.get(srcNode.getExamMetaData().getExamId());
            Map<CourseType, List<ExamDimensionValuesPayload>> courseTypeDimensions = new HashMap<>();
            for (Entry<CourseType, List<ExamDimensionValues>> entry : examDetails.getExamDimensionValues().entrySet()) {
                List<ExamDimensionValuesPayload> examDimensionValuesPayloadList = getExamDimensionValuesPayloads(entry.getValue());
                courseTypeDimensions.put(entry.getKey(), examDimensionValuesPayloadList);
            }

            Map<CourseType, List<ExamCourse>> examCourseMap = examDetails.getCourseMarksMatrix();
            Map<String, List<ExamDimensionValuesPayload>> courseNameExamDimensionMap = new HashMap<>();
            Map<CourseType, Map<Integer, List<ExamDimensionValues>>> defaultDimensionStoreMap = new HashMap<>();

            for (Entry<CourseType, List<ExamCourse>> entry : examCourseMap.entrySet()) {
                for (ExamCourse examCourse : entry.getValue()) {
                    String courseName = examCourse.getCourse().getCourseName().trim().toLowerCase();
                    courseNameExamDimensionMap.put(courseName, getExamDimensionValuesPayloads(examCourse.getExamDimensionValues()));
                    for (ExamDimensionValues examDimensionValues : examCourse.getExamDimensionValues()) {
                        int dimensionId = examDimensionValues.getExamDimension().getDimensionId();
                        if (!defaultDimensionStoreMap.containsKey(entry.getKey())) {
                            defaultDimensionStoreMap.put(entry.getKey(), new HashMap<>());
                        }
                        if (!defaultDimensionStoreMap.get(entry.getKey()).containsKey(dimensionId)) {
                            defaultDimensionStoreMap.get(entry.getKey()).put(dimensionId, new ArrayList<>());
                        }
                        defaultDimensionStoreMap.get(entry.getKey()).get(dimensionId).add(examDimensionValues);
                    }
                }
            }

            Map<CourseType, Map<Integer, ExamDimensionValuesPayload>> courseTypeFinalDefaultDimensionMap = getExamDimensionValuesPayloadMap(defaultDimensionStoreMap);

            Map<CourseType, List<ExamDimensionValuesPayload>> defaultDimensionsMap = new HashMap<>();
            for (Entry<CourseType, List<ExamDimensionValuesPayload>> courseEntry : courseTypeDimensions.entrySet()) {
                CourseType courseType = courseEntry.getKey();
                List<ExamDimensionValuesPayload> courseTypeDimensionList = new ArrayList<>();
                for (ExamDimensionValuesPayload examDimensionValuesPayloadEntry : courseEntry.getValue()) {
                    int dimensionId = examDimensionValuesPayloadEntry.getDimensionId();
                    Map<Integer, ExamDimensionValuesPayload> finalDefaultDimensionMap = courseTypeFinalDefaultDimensionMap.get(courseType);
                    if (finalDefaultDimensionMap == null) {
                        continue;
                    }
                    ExamDimensionValuesPayload examDimensionValuesPayload = finalDefaultDimensionMap.get(dimensionId);
                    if (examDimensionValuesPayload == null) {
                        continue;
                    }
                    courseTypeDimensionList.add(new ExamDimensionValuesPayload(dimensionId,
                            examDimensionValuesPayload.getMaxMarks(), examDimensionValuesPayload.getMinMarks(),
                            examDimensionValuesPayload.getMaxGrade(), examDimensionValuesPayload.getMinGrade()));
                }
                defaultDimensionsMap.put(courseType, courseTypeDimensionList);
            }

            ExamCreationPayload examCreationPayload = new ExamCreationPayload(destAcademicSessionId, destStandardId,
                    srcNode.getExamMetaData().getExamName(), parentId, srcNode.getExamMetaData().getExamType(),
                    srcNode.getExamMetaData().getOperation(), null, null,
                    null, null,
                    srcNode.getExamMetaData().getScholasticExamMarksDisplayType(),
                    srcNode.getExamMetaData().getCoScholasticExamMarksDisplayType(), courseTypeDimensions);
            UUID examId = examinationManager.createExam(examCreationPayload, destInstituteId);
            List<Course> courses = courseManager.getClassCoursesByStandardId(destInstituteId, destStandardId, destAcademicSessionId);
            List<ExamCoursesPayload> scholasticExamCoursesPayloads = new ArrayList<>();
            List<ExamCoursesPayload> coScholasticExamCoursesPayloads = new ArrayList<>();

            List<ExamCourse> examCourseList = examCourseMap.get(CourseType.SCHOLASTIC);
            boolean noScholasticCourseFound = true;
            boolean noCoScholasticCourseFound = true;
            for (Course course : courses) {
                String courseName = course.getCourseName().trim().toLowerCase();
                List<ExamDimensionValuesPayload> courseExamDimensionValuesList = null;
                if (courseNameExamDimensionMap.containsKey(courseName)) {
                    courseExamDimensionValuesList = courseNameExamDimensionMap.get(courseName);
                    if (course.getCourseType() == CourseType.SCHOLASTIC) {
                        noScholasticCourseFound = false;
                    } else if (course.getCourseType() == CourseType.COSCHOLASTIC) {
                        noCoScholasticCourseFound = false;
                    }
                } else {
                    // If only src course needs to be copied then skip other courses if not found
                    if (course.getCourseType() == CourseType.SCHOLASTIC && cloneOnlyForScholasticSourceCourses) {
                        continue;
                    }

                    if (course.getCourseType() == CourseType.COSCHOLASTIC && cloneOnlyForCoScholasticSourceCourses) {
                        continue;
                    }
                }

                List<ExamDimensionValuesPayload> defaultCourseExamDimensionValuesList =
                        defaultDimensionsMap.get(course.getCourseType());

                if (courseExamDimensionValuesList == null && defaultCourseExamDimensionValuesList != null) {
                    courseExamDimensionValuesList = defaultCourseExamDimensionValuesList;
                }

                if (courseExamDimensionValuesList == null) {
                    courseExamDimensionValuesList = courseTypeDimensions.get(course.getCourseType());
                }

                if (course.getCourseType() == CourseType.SCHOLASTIC) {
                    scholasticExamCoursesPayloads.add(new ExamCoursesPayload(course.getCourseId(), courseExamDimensionValuesList));
                } else if (course.getCourseType() == CourseType.COSCHOLASTIC) {
                    coScholasticExamCoursesPayloads.add(new ExamCoursesPayload(course.getCourseId(), courseExamDimensionValuesList));
                }

            }

            // Requirement : If cloneOnlyForSrcCourses is true then only the matching course is copied
            // if cloneOnlyForSrcCourses is not true then if any one course is enabled in the specific course type then
            // only all the new courses will be copied otherwise no course would be added.

            if (!noScholasticCourseFound && CollectionUtils.isNotEmpty(scholasticExamCoursesPayloads)) {
                boolean success = examinationManager.addCoursesInExam(destInstituteId, examId, CourseType.SCHOLASTIC, scholasticExamCoursesPayloads, true);
            }
            if (!noCoScholasticCourseFound && CollectionUtils.isNotEmpty(coScholasticExamCoursesPayloads)) {
                boolean success = examinationManager.addCoursesInExam(destInstituteId, examId, CourseType.COSCHOLASTIC, coScholasticExamCoursesPayloads, true);
            }
            return examId;
        } catch (Exception e) {
            logger.error("Error while creating exam for payload destInstituteId {}, destAcademicSessionId {}, standardId {}, srcNode {}, parentId {}", destInstituteId, destAcademicSessionId, destStandardId, srcNode, parentId, e);
        }
        return null;
    }

    private static List<ExamDimensionValuesPayload> getExamDimensionValuesPayloads(List<ExamDimensionValues> examDimensionValues) {
        List<ExamDimensionValuesPayload> examDimensionValuesPayloadList = new ArrayList<>();
        for (ExamDimensionValues edv : examDimensionValues) {
            examDimensionValuesPayloadList.add(new ExamDimensionValuesPayload(edv.getExamDimension().getDimensionId(),
                    edv.getMaxMarks(), edv.getMinMarks(), edv.getMaxGrade() == null ? null : edv.getMaxGrade().getGradeId(),
                    edv.getMinGrade() == null ? null : edv.getMinGrade().getGradeId()));
        }
        return examDimensionValuesPayloadList;
    }

    private Map<CourseType, Map<Integer, ExamDimensionValuesPayload>> getExamDimensionValuesPayloadMap(Map<CourseType, Map<Integer, List<ExamDimensionValues>>> defaultDimensionStoreMap) {
        Map<CourseType, Map<Integer, ExamDimensionValuesPayload>> courseTypeFinalDefaultDimensionMap = new HashMap<>();
        for (Entry<CourseType, Map<Integer, List<ExamDimensionValues>>> courseTypeEntry : defaultDimensionStoreMap.entrySet()) {
            CourseType courseType = courseTypeEntry.getKey();
            Map<Integer, ExamDimensionValuesPayload> finalDefaultDimensionMap = new HashMap<>();
            for (Entry<Integer, List<ExamDimensionValues>> entry : courseTypeEntry.getValue().entrySet()) {
                Map<Double, Integer> maxMap = new HashMap<>();
                Map<Double, Integer> minMap = new HashMap<>();
                Map<Integer, Integer> gradeMap = new HashMap<>();
                for (ExamDimensionValues examDimensionValues : entry.getValue()) {
                    if (examDimensionValues.getMaxMarks() != null) {
                        if (!maxMap.containsKey(examDimensionValues.getMaxMarks())) {
                            maxMap.put(examDimensionValues.getMaxMarks(), 1);
                        }
                        maxMap.put(examDimensionValues.getMaxMarks(), maxMap.get(examDimensionValues.getMaxMarks()) + 1);
                    }
                    if (examDimensionValues.getMinMarks() != null) {
                        if (!minMap.containsKey(examDimensionValues.getMinMarks())) {
                            minMap.put(examDimensionValues.getMinMarks(), 1);
                        }
                        minMap.put(examDimensionValues.getMinMarks(), minMap.get(examDimensionValues.getMinMarks()) + 1);
                    }
                    if (examDimensionValues.getMaxGrade() != null && examDimensionValues.getMaxGrade().getGradeId() != null) {
                        Integer gradeId = examDimensionValues.getMaxGrade().getGradeId();
                        if (!gradeMap.containsKey(gradeId)) {
                            gradeMap.put(gradeId, 1);
                        }
                        gradeMap.put(gradeId, gradeMap.get(gradeId) + 1);
                    }
                }
                Double maxMarks = getMaxEntry(maxMap);
                Double minMarks = getMaxEntry(minMap);
                Integer finalGradeId = getMaxEntry(gradeMap);

                finalDefaultDimensionMap.put(entry.getKey(), new ExamDimensionValuesPayload(entry.getKey(), maxMarks, minMarks, finalGradeId, null));
            }
            courseTypeFinalDefaultDimensionMap.put(courseType, finalDefaultDimensionMap);
        }
        return courseTypeFinalDefaultDimensionMap;
    }

    private <T> T getMaxEntry(Map<T, Integer> dataMap) {
        if (MapUtils.isEmpty(dataMap)) {
            return null;
        }
        Entry<T, Integer> maxVal = null;
        for (Entry<T, Integer> maxEntry : dataMap.entrySet()) {
            if (maxVal == null || maxVal.getValue() < maxEntry.getValue()) {
                maxVal = maxEntry;
            }
        }
        return maxVal.getKey();
    }

}
