package com.embrate.cloud.core.lib.templates.notification;

import com.embrate.cloud.core.api.audio.AudioFileFormat;
import com.embrate.cloud.core.api.audio.AudioFileProperties;
import com.embrate.cloud.core.api.service.communication.templates.*;
import com.embrate.cloud.core.lib.filesystem.S3FileSystem;
import com.embrate.cloud.dao.tier.service.communication.templates.ITemplateDao;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.configurations.EntityValue;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.notification.NotificationDetails;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.core.utils.audio.AudioUtils;
import com.lernen.cloud.core.utils.communication.service.CommunicationServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;

import java.io.ByteArrayOutputStream;
import java.io.StringWriter;
import java.util.*;

import static com.embrate.cloud.core.api.service.communication.templates.CommunicationTemplate.AUDIO_METADATA;
import static com.lernen.cloud.core.utils.StringConstants.DOT;

/**
 * <AUTHOR>
 */
public class NotificationTemplateManager {
    private static final Logger logger = LogManager.getLogger(NotificationTemplateManager.class);
    private static final String AUDIO_FILE_PATH = "audio-templates/entity_id=%s/delivery_mode=%s/template_type=%s/%s";

    private final S3FileSystem s3FileSystem;
    private final ITemplateDao<CommunicationTemplate> templateDao;
    private final String s3BucketName;

    public NotificationTemplateManager(S3FileSystem s3FileSystem, ITemplateDao<CommunicationTemplate> templateDao, String s3BucketName) {
        this.s3FileSystem = s3FileSystem;
        this.templateDao = templateDao;
        this.s3BucketName = s3BucketName;
    }

    public UUID createSMSTemplate(UUID userId, CommunicationTemplatePayload communicationTemplatePayload){

        if(userId == null){
            logger.error("Invalid user id for payload {}" , communicationTemplatePayload);
            return null;
        }

        if(communicationTemplatePayload == null || communicationTemplatePayload.getEntityName() == null ||
                StringUtils.isBlank(communicationTemplatePayload.getEntityId()) ||
                StringUtils.isBlank(communicationTemplatePayload.getTemplateName()) ||
                StringUtils.isBlank(communicationTemplatePayload.getTemplateValue()) ||
                communicationTemplatePayload.getTemplateType() == null){
            logger.error("Invalid template for payload {}, user {}", communicationTemplatePayload, userId);
            return null;
        }

        if(!validTemplate(communicationTemplatePayload.getTemplateValue(), communicationTemplatePayload.getTemplateVariableDetails(), communicationTemplatePayload.getTemplateType())){
            logger.error("Template not valid as for payload {}", communicationTemplatePayload);
            return null;
        }

        CommunicationTemplate communicationTemplate = new CommunicationTemplate(communicationTemplatePayload.getEntityName(), communicationTemplatePayload.getEntityId(),
                communicationTemplatePayload.getDeliveryMode(), communicationTemplatePayload.getTemplateType(),
                1, communicationTemplatePayload.getTemplateValue(), communicationTemplatePayload.getTemplateName(),
                communicationTemplatePayload.getLocale(), TemplateStatus.PENDING,
                communicationTemplatePayload.getTemplateVariableDetails(), communicationTemplatePayload.getMetadata(),
                userId, communicationTemplatePayload.getDescription());

        UUID templateId =  templateDao.addTemplate(communicationTemplate);
        if(templateId == null){
            logger.error("Unable to add template to database for {}, for payload {}, user {}",communicationTemplate, communicationTemplatePayload, userId);
            return null;
        }

        return templateId;
    }

    public UUID uploadAudioTemplate(FileData audioFile, Entity entityName, String entityId, DeliveryMode deliveryMode, TemplateType templateType, String templateName, String locale, Map<String, Object> metadata, String description, UUID userId){

        if(audioFile == null){
            logger.error("Invalid audio file for entityId {}, deliveryMode {}", entityId, deliveryMode);
            return null;
        }
        //TODO : validate the audio file and extract attributes;
        final String fileExtension = FilenameUtils.getExtension(audioFile.getFileName());
        Float duration = AudioUtils.getAudioFileDuration(audioFile.getContent());
        String filePath = getAudioFilePath(entityId, deliveryMode, templateType, UUID.randomUUID() + DOT + fileExtension);
        logger.info("Audio file path for storage {}, entityId {}, deliveryMode {}, templateType {}", filePath, entityId, deliveryMode, templateType);
        if(metadata == null){
            metadata = new HashMap<>();
        }
        metadata.put(AUDIO_METADATA, new AudioFileProperties(duration, AudioFileFormat.getAudioFileFormat(fileExtension)));
        CommunicationTemplate communicationTemplate = new CommunicationTemplate(entityName, entityId, deliveryMode, templateType, 1, filePath, templateName, locale, TemplateStatus.PENDING, null, metadata, userId, description);

        UUID templateId =  templateDao.addTemplate(communicationTemplate);
        if(templateId == null){
            logger.error("Unable to add template to database for payload {}",communicationTemplate);
            return null;
        }

        try {
            if(s3FileSystem.writeFile(s3BucketName, filePath, audioFile)){
                logger.info("Successfully uploaded the audio file to path {}", filePath);
            }else{
                logger.error("Error while uploading the audio file to path {}", filePath);
                return null;
            }
        }catch (Exception e){
            logger.error("Exception while uploading the audio file to path {}", filePath, e);
            return null;
        }

        return templateId;
    }

    private boolean validTemplate(String template, TemplateVariableDetails templateVariableDetails, TemplateType templateType){
        final VelocityContext context = new VelocityContext();
        if(templateVariableDetails == null || CollectionUtils.isEmpty(templateVariableDetails.getTemplateVariableList())){
            //Commenting this logic as custom teplat might not have any variables.
//            if(templateType == TemplateType.CUSTOM){
//                logger.error("No template variable found for template {}", template);
//                return false;
//            }
            return true;
        }
        for(TemplateVariable templateVariable : templateVariableDetails.getTemplateVariableList()){
            if(templateVariable == null || templateVariable.getTemplateVariableType() == null || StringUtils.isBlank(templateVariable.getVarName())){
                logger.error("Invalid template variable {} for template {}", templateVariable, template);
                return false;
            }

            context.put(templateVariable.getVarName(), "test_value");
        }

        final StringWriter templateContent = new StringWriter();
        try{
            Velocity.evaluate(context, templateContent, template, template);
            return true;
        }catch (Exception e){
            logger.error("Error while rendering template {} with var {}", template, templateVariableDetails, e);
        }
        return false;
    }

    public boolean addDLTTemplateMapping(DLTContentTemplateRegistrationPayload dltContentTemplateRegistrationPayload){
        if(dltContentTemplateRegistrationPayload == null || dltContentTemplateRegistrationPayload.getTemplateId() == null
                || StringUtils.isBlank(dltContentTemplateRegistrationPayload.getDltTemplateId()) ||
                dltContentTemplateRegistrationPayload.getDeliveryMode() == null || dltContentTemplateRegistrationPayload.getTemplateStatus() == null){
            logger.error("Invalid dltContentTemplateRegistrationPayload {}", dltContentTemplateRegistrationPayload);
            return false;
        }

        CommunicationServiceProvider communicationServiceProvider = null;
        switch (dltContentTemplateRegistrationPayload.getDeliveryMode()){
            case SMS:
                communicationServiceProvider = CommunicationServiceProvider.DLT_SMS;
                break;
            case WHATSAPP:
                communicationServiceProvider = CommunicationServiceProvider.WEBPAY_WHATSAPP;
                break;
            default:
                break;
        }
        if(communicationServiceProvider == null){
            logger.error("DLT service provider not supported for {}, dltContentTemplateRegistrationPayload {}", dltContentTemplateRegistrationPayload.getDeliveryMode(), dltContentTemplateRegistrationPayload);
            return false;
        }
        Map<String, Object> metadata = new HashMap<>();
        ExternalServiceTemplateData externalServiceTemplateData = new ExternalServiceTemplateData(communicationServiceProvider, dltContentTemplateRegistrationPayload.getTemplateId(), dltContentTemplateRegistrationPayload.getDltTemplateId(), dltContentTemplateRegistrationPayload.getTemplateStatus(), metadata);
        return  addExternalTemplateMapping(externalServiceTemplateData);
    }

    public boolean updateDLTTemplateMapping(DLTContentTemplateUpdatePayload dltContentTemplateUpdatePayload){
        if(dltContentTemplateUpdatePayload == null
                || StringUtils.isBlank(dltContentTemplateUpdatePayload.getDltTemplateId()) ||
                dltContentTemplateUpdatePayload.getDeliveryMode() == null || dltContentTemplateUpdatePayload.getTemplateStatus() == null){
            logger.error("Invalid dltContentTemplateUpdatePayload {}", dltContentTemplateUpdatePayload);
            return false;
        }

        CommunicationServiceProvider communicationServiceProvider = null;
        switch (dltContentTemplateUpdatePayload.getDeliveryMode()){
            case SMS:
                communicationServiceProvider = CommunicationServiceProvider.DLT_SMS;
                break;
            case WHATSAPP:
                communicationServiceProvider = CommunicationServiceProvider.WEBPAY_WHATSAPP;
                break;
            default:
                break;
        }
        if(communicationServiceProvider == null){
            logger.error("DLT service provider not supported for {}, dltContentTemplateUpdatePayload {}", dltContentTemplateUpdatePayload.getDeliveryMode(), dltContentTemplateUpdatePayload);
            return false;
        }
        List<ExternalServiceTemplateData> externalServiceTemplateDataList = getExternalTemplateMapping(dltContentTemplateUpdatePayload.getDltTemplateId());
        if(CollectionUtils.isEmpty(externalServiceTemplateDataList)){
            logger.error("No template mapping found for request {}", dltContentTemplateUpdatePayload);
            return false;
        }
        //TODO : Filter the template by service provider and if status is approved, approval all internal templates
       return true;
    }

    public boolean addExternalTemplateMapping(ExternalServiceTemplateData externalServiceTemplateData){
        if(templateDao.addExternalTemplateMapping(externalServiceTemplateData)){
            logger.info("Successfully added the external template mapping for externalServiceTemplateData {}", externalServiceTemplateData);
            return true;
        }
        logger.error("Error while adding the external template mapping for externalServiceTemplateData", externalServiceTemplateData);
        return false;
    }

    public List<CommunicationTemplate> getCommunicationTemplates(int instituteId, boolean approvedOnly, Set<TemplateType> templateTypeSet, Set<DeliveryMode> deliveryModesSet){
        final List<EntityValue> entityValues =  getHierarchy(Entity.INSTITUTE, String.valueOf(instituteId));
        List<CommunicationTemplate> communicationTemplateList = templateDao.getTemplates(entityValues, approvedOnly ? new HashSet<>(Arrays.asList(TemplateStatus.APPROVED)) : null, deliveryModesSet);
        if(communicationTemplateList == null){
            return null;
        }

        List<CommunicationTemplate> filteredCommunicationTemplateList = new ArrayList<>();
        for(CommunicationTemplate communicationTemplate : communicationTemplateList) {
            if(communicationTemplate.getDeliveryMode() == DeliveryMode.CALL){
                communicationTemplate.setCredits(NotificationDetails.getVoiceCallCredits(CommunicationServiceUtils.getAudioFileProperties(communicationTemplate).getDuration()));
            }

            if(CollectionUtils.isEmpty(templateTypeSet) || templateTypeSet.contains(communicationTemplate.getTemplateType())){
                filteredCommunicationTemplateList.add(communicationTemplate);
            }
        }
        Collections.sort(filteredCommunicationTemplateList, new Comparator<CommunicationTemplate>() {
            @Override
            public int compare(CommunicationTemplate o1, CommunicationTemplate o2) {
              return StringHelper.compareIgnoreCase(o1.getTemplateName(), o2.getTemplateName(), true);
            }
        });

        return filteredCommunicationTemplateList;
    }

    public List<CommunicationTemplate> getCommunicationTemplates(Set<TemplateStatus> templateStatuses){
        List<CommunicationTemplate> communicationTemplateList = templateDao.getTemplates(templateStatuses);
        if(communicationTemplateList == null){
            return null;
        }

        for(CommunicationTemplate communicationTemplate : communicationTemplateList){
            if(communicationTemplate.getDeliveryMode() == DeliveryMode.CALL){
                communicationTemplate.setCredits(NotificationDetails.getVoiceCallCredits(CommunicationServiceUtils.getAudioFileProperties(communicationTemplate).getDuration()));
            }
        }

        Collections.sort(communicationTemplateList, new Comparator<CommunicationTemplate>() {
            @Override
            public int compare(CommunicationTemplate o1, CommunicationTemplate o2) {
                return StringUtils.compareIgnoreCase(o1.getTemplateName(), o2.getTemplateName());
            }
        });

        return communicationTemplateList;
    }

    public CommunicationTemplate getCommunicationTemplate(UUID templateId){
        return templateDao.getTemplate(templateId);
    }

    public boolean updateCommunicationTemplateStatus(UUID templateId, TemplateStatus templateStatus){
        return templateDao.updateCommunicationTemplateStatus(templateId, templateStatus);
    }

    public ExternalServiceTemplateData getExternalTemplateMapping(CommunicationServiceProvider serviceProvider, UUID templateId){
        return templateDao.getExternalTemplateMapping(serviceProvider, templateId);
    }

    public List<ExternalServiceTemplateData> getExternalTemplateMapping(UUID templateId){
        return templateDao.getExternalTemplateMapping(templateId);
    }

    public List<ExternalServiceTemplateData> getExternalTemplateMapping(String externalTemplateId){
        return templateDao.getExternalTemplateMapping(externalTemplateId);
    }

    public boolean updateExternalTemplateStatus(UUID templateId, String externalTemplateId, TemplateStatus templateStatus){
        return templateDao.updateExternalTemplateStatus(templateId, externalTemplateId, templateStatus);
    }

    public ByteArrayOutputStream getTemplateFile(UUID templateId){
        if(templateId == null){
            logger.error("Invalid template id");
            return null;
        }
        CommunicationTemplate communicationTemplate = templateDao.getTemplate(templateId);
        if(communicationTemplate == null){
            logger.error("No template found for template id {}", templateId);
            return null;
        }
        return getTemplateFile(communicationTemplate);
    }

    public DownloadDocumentWrapper<CommunicationTemplate> getAudioTemplate(int instituteId, UUID templateId){
        if(templateId == null){
            logger.error("Invalid template id");
            return null;
        }
        CommunicationTemplate communicationTemplate = templateDao.getTemplate(templateId);
        if(communicationTemplate == null){
            logger.error("No template found for template id {}", templateId);
            return null;
        }
        return new DownloadDocumentWrapper<>(communicationTemplate, getTemplateFile(communicationTemplate));
    }


    public ByteArrayOutputStream getTemplateFile(CommunicationTemplate communicationTemplate){
        String templatePath = communicationTemplate.getTemplateValue();
        return  s3FileSystem.readFile(s3BucketName, templatePath);
    }

    private String getAudioFilePath(String entityId, DeliveryMode deliveryMode, TemplateType templateType, String fileName){
        return String.format(AUDIO_FILE_PATH, entityId, deliveryMode,templateType, fileName);
    }

    private List<EntityValue> getHierarchy(Entity entity, String entityId) {
        final List<EntityValue> entityValues = new LinkedList<>();
        entityValues.add(new EntityValue(Entity.GLOBAL, Entity.GLOBAL.name()));
        if (entity == Entity.GLOBAL) {
            return entityValues;
        } else if (entity == Entity.INSTITUTE) {
            entityValues.add(new EntityValue(Entity.INSTITUTE, entityId));
            return entityValues;
        }
        throw new EmbrateRunTimeException(entity.name() + " is not supported");
    }
}
