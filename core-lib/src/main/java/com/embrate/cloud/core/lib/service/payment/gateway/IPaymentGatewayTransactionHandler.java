package com.embrate.cloud.core.lib.service.payment.gateway;

import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayTransactionStatus;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public interface IPaymentGatewayTransactionHandler {

    public UUID getTransactionId();

    public PaymentGatewayTransactionStatus getTransactionStatus();

    public boolean validTransactionSignature();

    public Map<String, String> getTransactionData();

    boolean isWebhook();

    String getWebhookData();
}
