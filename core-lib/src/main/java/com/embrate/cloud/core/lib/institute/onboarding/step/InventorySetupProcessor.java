package com.embrate.cloud.core.lib.institute.onboarding.step;

import com.embrate.cloud.core.api.inventory.v2.InventoryCounterData;
import com.embrate.cloud.core.api.inventory.v2.InventoryCounterType;
import com.embrate.cloud.core.api.inventory.v2.category.InventoryCategory;
import com.embrate.cloud.core.api.inventory.v2.outlet.InventoryOutlet;
import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteSetupPayload;
import com.embrate.cloud.core.lib.inventory.v2.InventoryOutletManager;
import com.embrate.cloud.core.lib.institute.onboarding.IConfigureInstituteStep;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.inventory.InventoryPreferences;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.configs.ConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 */

public class InventorySetupProcessor implements IModuleSetupProcessor {

    private static final Logger logger = LogManager.getLogger(InventorySetupProcessor.class);
    private final InstituteManager instituteManager;

    private final ConfigurationManager configurationManager;

    private final InventoryOutletManager inventoryOutletManager;

    private static final Map<InventoryCounterType, String> INVENTORY_COUNTER_TYPE_PREFIX = new EnumMap<>(InventoryCounterType.class);

    static {
        INVENTORY_COUNTER_TYPE_PREFIX.put(InventoryCounterType.SALE, "S-");
        INVENTORY_COUNTER_TYPE_PREFIX.put(InventoryCounterType.PURCHASE, "P-");
        INVENTORY_COUNTER_TYPE_PREFIX.put(InventoryCounterType.PURCHASE_RETURN, "PR-");
        INVENTORY_COUNTER_TYPE_PREFIX.put(InventoryCounterType.SALE_RETURN, "SR-");
        INVENTORY_COUNTER_TYPE_PREFIX.put(InventoryCounterType.ISSUE, "I-");
    }
    public InventorySetupProcessor(InstituteManager instituteManager, ConfigurationManager configurationManager, InventoryOutletManager inventoryOutletManager) {
        this.instituteManager = instituteManager;
        this.configurationManager = configurationManager;
        this.inventoryOutletManager = inventoryOutletManager;
    }

    @Override
    public Module getModule() {
        return Module.STORE;
    }

    @Override
    public List<IConfigureInstituteStep> getSteps(InstituteSetupPayload instituteSetupPayload) {
        UUID organisationId = instituteSetupPayload.getOrganisationId();
        Set<Integer> instituteIds = instituteSetupPayload.getInventoryInstitutes();

        List<IConfigureInstituteStep> instituteSetupStepsList = new ArrayList<>();
        instituteSetupStepsList.add(getInventorySetupStep(organisationId, instituteIds));

        return instituteSetupStepsList;
    }

    private IConfigureInstituteStep getInventorySetupStep(UUID organisationId, Set<Integer> instituteIds) {
        return new IConfigureInstituteStep() {
            @Override
            public String getName() {
                return "Inventory setup";
            }

            @Override
            public boolean execute() {
                return setupOutlet(instituteIds, organisationId, true);
            }
        };
    }

    public boolean setupOutlet(Set<Integer> instituteIds, UUID organisationId, boolean createCategory) {
        int instituteId = instituteIds.iterator().next();
        String outletName = instituteId + " Inventory";

        InventoryOutlet inventoryOutlet = new InventoryOutlet(null, outletName, organisationId, instituteIds, null);
        UUID outletId = inventoryOutletManager.addOutlet(inventoryOutlet);
        if (outletId == null) {
            logger.error("Unable to create outlet for organisation {}, instituteIds {}", organisationId, instituteIds);
            return false;
        }
        logger.info("Outlet created {}, for organisation {}, instituteIds {}", outletId, organisationId, instituteIds);

        Map<String, String> configKVs = new HashMap<>();
        configKVs.put(InventoryPreferences.INVENTORY_OUTLET, outletId.toString());

        for (Integer institute : instituteIds) {
            configurationManager.upsertConfiguration(Entity.INSTITUTE, String.valueOf(institute), InventoryPreferences.getConfigType(), configKVs);
        }

        logger.info("Outlet configured {}, for organisation {}, instituteIds {}", outletId, organisationId, instituteIds);

        if(createCategory){
            List<String> categories = Arrays.asList("Stationery", "Books", "Clothing", "Foot Wear", "Note Book", "Art & Craft",
                    "Personal Care", "Bedding", "Energy Drinks", "Accessories", "Furniture", "Electronics", "Sports");

            for (String category : categories) {
                inventoryOutletManager.addCategory(outletId, new InventoryCategory(0, category, null));
            }

            logger.info("Outlet categories created for organisation {}, instituteIds {}", outletId, organisationId, instituteIds);
        }

        for (InventoryCounterType inventoryCounterType : InventoryCounterType.values()) {
            String prefix = INVENTORY_COUNTER_TYPE_PREFIX.get(inventoryCounterType);
            inventoryOutletManager.insertCounter(outletId, new InventoryCounterData(inventoryCounterType, 1, prefix));
        }

        logger.info("Outlet counters created for organisation {}, instituteIds {}", outletId, organisationId, instituteIds);

        return true;
    }
}
