package com.embrate.cloud.core.lib.dashboards.admission;

import com.embrate.cloud.core.api.dashboards.admission.StudentAdmTCCount;
import com.embrate.cloud.core.api.dashboards.admission.StudentAdmissionOrgStats;
import com.embrate.cloud.core.api.dashboards.common.InstituteValue;
import com.embrate.cloud.dao.tier.dashboard.admission.AdmissionDashboardDao;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.student.StudentSessionSummary;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.embrate.cloud.core.utils.institute.DashboardUtils.*;

/**
 * <AUTHOR>
 **/
public class AdmissionDashboardManager {

	private static final Logger logger = LogManager.getLogger(AdmissionDashboardManager.class);

	private final InstituteManager instituteManager;

	private final AdmissionDashboardDao admissionDashboardDao;

	public AdmissionDashboardManager(InstituteManager instituteManager, AdmissionDashboardDao admissionDashboardDao) {
		this.instituteManager = instituteManager;
		this.admissionDashboardDao = admissionDashboardDao;
	}

	public StudentAdmissionOrgStats getStudentAdmissionOrgStats(UUID organizationId, String selectedInstituteIds,
																UUID userId, int startDate, int endDate) {

		validatePayload(organizationId, selectedInstituteIds, userId, startDate, endDate);
		List<Integer> institutes = getInstituteIds(selectedInstituteIds);
		Map<Integer, List<AcademicSession>> instituteSessionMap = instituteManager.getInstituteAcademicSessionMap(institutes);
		Set<Integer> requiredAcademicSessions = getRequiredSessions(instituteSessionMap, startDate, endDate);
		List<StudentSessionSummary> sessionSummaryList = admissionDashboardDao.getStudentCountBySessionStatus(institutes, requiredAcademicSessions);
		List<StudentAdmTCCount> newAdmissionsAndTCCountList = admissionDashboardDao.getNewAdmissionsAndTCCountByDateRange(institutes, startDate, endDate);
		List<InstituteValue> instituteStudents = new ArrayList<>();
		for (StudentSessionSummary studentSessionSummary : sessionSummaryList) {
			if (studentSessionSummary.getSessionStatus() == StudentStatus.ENROLLED) {
				instituteStudents.add(new InstituteValue(studentSessionSummary.getInstituteId(), studentSessionSummary.getStudentCount()));
			}
		}
		List<InstituteValue> newAdmissions = new ArrayList<>();
		List<InstituteValue> tcIssued = new ArrayList<>();
		int totalNewAdmission = 0;
		int totalTCIssued = 0;
		for (StudentAdmTCCount studentAdmTCCount : newAdmissionsAndTCCountList) {
			newAdmissions.add(new InstituteValue(studentAdmTCCount.getInstituteId(), studentAdmTCCount.getAdmissionCount()));
			tcIssued.add(new InstituteValue(studentAdmTCCount.getInstituteId(), studentAdmTCCount.getRelieveCount()));
			totalNewAdmission += studentAdmTCCount.getAdmissionCount();
			totalTCIssued += studentAdmTCCount.getRelieveCount();
		}

		return new StudentAdmissionOrgStats(instituteStudents, newAdmissions, totalNewAdmission, tcIssued, totalTCIssued);
	}
}