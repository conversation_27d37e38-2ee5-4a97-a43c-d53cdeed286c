package com.lernen.cloud.dev.tools.transport.assignment;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Options;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.TransportArea;
import com.lernen.cloud.core.api.transport.TransportAssignmentPayload;
import com.lernen.cloud.core.api.transport.TransportConfiguredFeeData;
import com.lernen.cloud.core.api.transport.TransportFeeConfigData;
import com.lernen.cloud.core.api.transport.TransportHistoryFeeIdAmount;
import com.lernen.cloud.core.api.transport.TransportServiceRouteResponse;
import com.lernen.cloud.core.api.transport.TransportServiceRouteStoppagesResponse;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportConfigurationManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportFeeConfigurationManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransportAssignmentCreator {
//	private static final String INSTITUTE_ID = "i";
//	private static final String ACADEMIC_SESSION_ID = "s";
//	private static final String FILE_PATH = "f";
//	private static final String FILE_HEADER = "h";
//	private static final String UPDATE = "u";
//	private static final String FILE_DELIMITER = "\\|";
//
//	private final TransportAssignmentManager transportAssignmentManager;
//	private final TransportConfigurationManager transportConfigurationManager;
//	private final TransportFeeConfigurationManager transportFeeConfigurationManager;
//	private final StudentManager studentManager;
//
//	public TransportAssignmentCreator(TransportAssignmentManager transportAssignmentManager,
//			TransportConfigurationManager transportConfigurationManager,
//			TransportFeeConfigurationManager transportFeeConfigurationManager, StudentManager studentManager) {
//		this.transportAssignmentManager = transportAssignmentManager;
//		this.transportConfigurationManager = transportConfigurationManager;
//		this.transportFeeConfigurationManager = transportFeeConfigurationManager;
//		this.studentManager = studentManager;
//	}
//
//	public static void main(String args[]) {
//		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
//		final TransportAssignmentCreator transportAssignmentCreator = context.getBean(TransportAssignmentCreator.class);
//		Options options = buildOptions();
//		CommandLine cmdLine = null;
//		try {
//			final CommandLineParser parser = new DefaultParser();
//			cmdLine = parser.parse(options, args);
//		} catch (final Exception pex) {
//			System.err.print("Unable to parse arguments.");
//			pex.printStackTrace();
//			final HelpFormatter formatter = new HelpFormatter();
//			formatter.printHelp("TransportAssignmentCreator ", options);
//			return;
//		}
//
//		if (cmdLine == null) {
//			System.err.print("Unable to parse arguments from cmdline.");
//			return;
//		}
//
//		int instituteId = 0;
//		if (cmdLine.hasOption(INSTITUTE_ID)) {
//			instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
//		} else {
//			System.err.print("No institute id passed. Exitting");
//			return;
//		}
//		int academicSessionId = 0;
//		if (cmdLine.hasOption(ACADEMIC_SESSION_ID)) {
//			academicSessionId = Integer.parseInt(cmdLine.getOptionValue(ACADEMIC_SESSION_ID));
//		} else {
//			System.err.print("No academicSessionId passed. Exitting");
//			return;
//		}
//
//		String filePath = null;
//		if (cmdLine.hasOption(FILE_PATH)) {
//			filePath = cmdLine.getOptionValue(FILE_PATH);
//		} else {
//			System.err.print("No file path passed. Exitting");
//			return;
//		}
//		boolean fileHeader = false;
//		if (cmdLine.hasOption(FILE_HEADER)) {
//			fileHeader = true;
//		}
//		boolean updateAssignment = false;
//		if (cmdLine.hasOption(UPDATE)) {
//			updateAssignment = true;
//		}
//		System.out.println("Running for institute = " + instituteId + " , session = " + academicSessionId
//				+ ", header = " + fileHeader + " , assign = " + updateAssignment);
//		transportAssignmentCreator.assignTransportFromFile(instituteId, academicSessionId, filePath, fileHeader,
//				updateAssignment);
//	}
//
//	public void assignTransportFromFile(int instituteId, int academicSessionId, String filePath, boolean fileHeader,
//			boolean updateAssignment) {
//		List<TransportAssignmentFilePayload> transportAssignmentFilePayloads = readAssignmentFile(filePath, fileHeader);
//		System.out.println("Total students in file = " + transportAssignmentFilePayloads.size());
//
//		for (TransportAssignmentFilePayload transportAssignmentFilePayload : transportAssignmentFilePayloads) {
//			System.out.println(transportAssignmentFilePayload.toString());
//		}
//
//		List<TransportAssignmentPayload> transportAssignmentPayloads = getTransportAssignmentPayload(instituteId,
//				academicSessionId, transportAssignmentFilePayloads);
//		boolean totalSuccess = true;
//		int successCount = 0;
//		int failCount = 0;
//		System.out.println("Assigning transport for total records = " + transportAssignmentPayloads.size());
//		for (TransportAssignmentPayload transportAssignmentPayload : transportAssignmentPayloads) {
//			System.out.println("Dry run payload = " + transportAssignmentPayload.toString());
//			if (!updateAssignment) {
//				continue;
//			}
//			if (transportAssignmentManager.addTransport(transportAssignmentPayload, null)) {
//				System.out.println("Assigned transport ammount successfully.");
//				successCount++;
//			} else {
//				System.out.println("ERROR : Assigned transport ammount not success.");
//				totalSuccess = false;
//				failCount++;
//			}
//		}
//		if (totalSuccess) {
//			System.out.println("All records are assigned successfully.");
//		} else {
//			System.out.println("ERROR : All records are not assigned  successfully.");
//		}
//		System.out.println(
//				"Total = " + (successCount + failCount) + ", success = " + successCount + " , fail = " + failCount);
//	}
//
//	private final List<TransportAssignmentPayload> getTransportAssignmentPayload(int instituteId, int academicSessionId,
//			List<TransportAssignmentFilePayload> transportAssignmentFilePayloads) {
//		List<TransportAssignmentPayload> transportAssignmentPayloads = new ArrayList<>();
//		List<Student> enrolledStudents = studentManager.getStudentsInAcademicSesison(instituteId, academicSessionId);
//		List<TransportArea> transportAreas = transportConfigurationManager.getTransportAreas(instituteId);
//		List<TransportServiceRouteResponse> transportServiceRouteResponses = transportConfigurationManager
//				.getTransportServiceRoutes(instituteId, academicSessionId);
//
//		Map<String, Student> studentMap = new HashMap<>();
//		Map<String, TransportArea> transportAreaMap = new HashMap<>();
//		Map<String, TransportServiceRouteResponse> transportServiceRouteResponseMap = new HashMap<>();
//
//		for (Student studentResponse : enrolledStudents) {
//			studentMap.put(studentResponse.getStudentBasicInfo().getAdmissionNumber().toLowerCase(), studentResponse);
//		}
//
//		for (TransportArea transportArea : transportAreas) {
//			transportAreaMap.put(transportArea.getAreaKey().toLowerCase(), transportArea);
//		}
//
//		for (TransportServiceRouteResponse transportServiceRouteResponse : transportServiceRouteResponses) {
//			transportServiceRouteResponseMap.put(transportServiceRouteResponse.getServiceRouteName().toLowerCase(),
//					transportServiceRouteResponse);
//		}
//
//		TransportFeeConfigData transportFeeConfigData = transportFeeConfigurationManager
//				.getTransportFeeConfigData(instituteId, academicSessionId);
//		System.out.println(transportFeeConfigData);
//		List<TransportConfiguredFeeData> transportConfiguredFeeDatas1 = transportFeeConfigData
//				.getTransportConfiguredFeeDatas();
//		System.out.println("Is exam configured = " + transportFeeConfigData.isConfigured());
//		if (CollectionUtils.isEmpty(transportConfiguredFeeDatas1)) {
//			System.err.println("Invalid fee configured. Exitting");
//			return null;
//		}
//		for (TransportAssignmentFilePayload transportAssignmentFilePayload : transportAssignmentFilePayloads) {
//			System.out.println(transportAssignmentFilePayload);
//			Student studentResponse = studentMap.get(transportAssignmentFilePayload.getAdmissionNumber().toLowerCase());
//			TransportArea transportArea = transportAreaMap
//					.get(transportAssignmentFilePayload.getAreaKey().toLowerCase());
//			TransportServiceRouteResponse transportServiceRouteResponse = transportServiceRouteResponseMap
//					.get(transportAssignmentFilePayload.getRouteName().toLowerCase());
//
//			if (studentResponse == null || transportArea == null || transportServiceRouteResponse == null) {
//				System.err.println("Invalid student, or transport area or route. Exitting "
//						+ transportAssignmentFilePayload.toString());
//				return null;
//			}
//			// System.out.println(studentResponse);
//			// System.out.println(transportArea);
//			// System.out.println(transportServiceRouteResponse);
//			//
//			TransportAssignmentPayload transportAssignmentPayload = new TransportAssignmentPayload();
//			transportAssignmentPayload.setInstituteId(instituteId);
//			transportAssignmentPayload.setStudentId(studentResponse.getStudentId());
//			transportAssignmentPayload.setAcademicSessionId(academicSessionId);
//			transportAssignmentPayload.setAreaId(transportArea.getAreaId());
//			transportAssignmentPayload.setServiceRouteId(transportServiceRouteResponse.getServiceRouteId());
//			transportAssignmentPayload.setCompleteSession(true);
//
//			List<TransportHistoryFeeIdAmount> transportHistoryFeeIdAmountList = new ArrayList<>();
//			Double totalAmount = null;
//			for (TransportServiceRouteStoppagesResponse transportServiceRouteStoppagesResponse : transportServiceRouteResponse
//					.getStoppagesList()) {
//				if (transportServiceRouteStoppagesResponse.getTransportArea().getAreaId() == transportArea
//						.getAreaId()) {
//					totalAmount = transportServiceRouteStoppagesResponse.getAssignedAmount();
//					break;
//				}
//			}
//			if (totalAmount == null) {
//				System.err.println("Invalid total assigned amount for fees. Exitting");
//				return null;
//			}
//			for (TransportConfiguredFeeData transportConfiguredFeeData : transportConfiguredFeeDatas1) {
//				TransportHistoryFeeIdAmount transportHistoryFeeIdAmount = new TransportHistoryFeeIdAmount();
//				transportHistoryFeeIdAmount.setFeeId(transportConfiguredFeeData.getFeeConfigurationResponse()
//						.getFeeConfigurationBasicInfo().getFeeId());
//				transportHistoryFeeIdAmount.setAmount(
//						totalAmount * transportConfiguredFeeData.getModuleFeeProportion().getFeeProportion());
//				transportHistoryFeeIdAmountList.add(transportHistoryFeeIdAmount);
//			}
//			transportAssignmentPayload.setTransportHistoryFeeIdAmountList(transportHistoryFeeIdAmountList);
//			transportAssignmentPayloads.add(transportAssignmentPayload);
//		}
//		return transportAssignmentPayloads;
//	}
//
//	private List<TransportAssignmentFilePayload> readAssignmentFile(String fileName, boolean header) {
//		List<TransportAssignmentFilePayload> transportAssignmentFilePayloads = new ArrayList<>();
//		try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
//			String line = null;
//			while ((line = br.readLine()) != null) {
//				if (header) {
//					header = false;
//					continue;
//				}
//				if (StringUtils.isBlank(line)) {
//					continue;
//				}
//				String[] columns = line.split(FILE_DELIMITER, 6);
//				if (columns.length != 6) {
//					System.out.println(
//							"Columns are not " + 6 + ". Skipping it as its not for assignment. Entry = " + line);
//					continue;
//				}
//				String admissionNumber = columns[0].trim();
//				String routeName = columns[4].trim();
//				String areaName = columns[5].trim();
//				if (StringUtils.isBlank(routeName) || StringUtils.isBlank(areaName)) {
//					System.out.println("Not valid entry for assignment. Entry = " + line);
//					continue;
//				}
//				String areaKey = areaName;
//				if (areaName.contains("(")) {
//					areaKey = areaName.split("\\(")[1].split("\\)")[0].trim();
//				}
//				transportAssignmentFilePayloads
//						.add(new TransportAssignmentFilePayload(admissionNumber, routeName, areaKey));
//			}
//			return transportAssignmentFilePayloads;
//		} catch (final Exception e) {
//			e.printStackTrace();
//		}
//		return null;
//	}
//
//	private static Options buildOptions() {
//		final Options options = new Options();
//		options.addOption(INSTITUTE_ID, true, "specify the institute id");
//		options.addOption(ACADEMIC_SESSION_ID, true, "Academic session id for institute");
//		options.addOption(FILE_PATH, true, "Transport assignment csv file path");
//		options.addOption(FILE_HEADER, false, "File has header");
//		options.addOption(UPDATE, false, "Update the assignments");
//		return options;
//	}
//
//	private class TransportAssignmentFilePayload {
//		private final String admissionNumber;
//		private final String routeName;
//		private final String areaKey;
//
//		public TransportAssignmentFilePayload(String admissionNumber, String routeName, String areaKey) {
//			this.admissionNumber = admissionNumber;
//			this.routeName = routeName;
//			this.areaKey = areaKey;
//		}
//
//		public String getAdmissionNumber() {
//			return admissionNumber;
//		}
//
//		public String getRouteName() {
//			return routeName;
//		}
//
//		public String getAreaKey() {
//			return areaKey;
//		}
//
//		@Override
//		public String toString() {
//			return "TransportAssignmentFilePayload [admissionNumber=" + admissionNumber + ", routeName=" + routeName
//					+ ", areaKey=" + areaKey + "]";
//		}
//
//	}
}
