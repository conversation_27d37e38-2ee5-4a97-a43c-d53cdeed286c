SET @institute_id := 10115;
SET @academic_session_id := 55;
SET @course_type := "SCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.905, 1.1, '91-100' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.745, 0.905, '75-90' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.745, '61-74' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 6, 0.445, 0.605, '45-60' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 5, 0.0 , 0.445, 'Below 45' from standards where institute_id = @institute_id;


-- Setting CO-SCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.905, 1.1, '91-100' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.745, 0.905, '75-90' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 7, 0.605, 0.745, '61-74' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 6, 0.445, 0.605, '45-60' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 5, 0.0 , 0.445, 'Below 45' from standards where institute_id = @institute_id;
