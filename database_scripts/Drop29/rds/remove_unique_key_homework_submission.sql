desc homework_submission_details;
--get indexs
SHOW INDEX FROM homework_submission_details;
--get foreign key name
SELECT TABLE_NAME,COLUMN_NAME,CONSTRAINT_NAME, REFERENCED_TABLE_NAME,REFERENCED_COLUMN_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_NAME = 'homework_submission_details';
--select distinct CONSTRAINT_NAME from information_schema.TABLE_CONSTRAINTS where table_name = 'homework_submission_details' and constraint_type = 'UNIQUE';
--Remove FOREIGN key
ALTER TABLE homework_submission_details DROP FOREIGN KEY homework_submission_details_ibfk_1;
-- remove unique key
ALTER TABLE homework_submission_details DROP INDEX homework_id;
-- add FOREIGN key
ALTER TABLE homework_submission_details ADD CONSTRAINT homework_submission_details_ibfk_1 FOREIGN KEY (homework_id) REFERENCES homework_details(homework_id);