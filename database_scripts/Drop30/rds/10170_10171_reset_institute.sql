--10170
----updating counters queries
--select * from counters where institute_id = 10170;
--insert into counters (institute_id, counter_type, count, counter_prefix) values (10170, 'SMS_COUNTER', 0, "");
----select * from counters where institute_id = 10170 and counter_type = 'ADMISSION_NUMBER';
----update counters set count = 1 where institute_id = 10170 and counter_type = 'ADMISSION_NUMBER';
----update counters set count = 1 where institute_id = 10170 and counter_type = 'FEE_INVOICE';
----update counters set count = 1 where institute_id = 10170 and counter_type = 'REGISTRATION_NUMBER';
----update counters set count = 5000 where institute_id = 10170 and counter_type = 'SMS_COUNTER';
----update counters set count = 1 where institute_id = 10170 and counter_type = 'STAFF_NUMBER';
--select * from counters where institute_id = 10170;

----Notification deletion queries
--select * from notification_status where institute_id = 10170;
----delete from notification_status where institute_id = 10170;

--Optional Course Assignment deletion queries
select * from student_course_assignment where course_id  in (select course_id from class_courses where academic_session_id = 77);
delete from student_course_assignment where course_id  in (select course_id from class_courses where academic_session_id = 77);

----Lecture deletion queries
--select * from student_lecture_view_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10170);
----delete from student_lecture_view_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10170);
--select * from student_lecture_complete_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10170);
----delete from student_lecture_complete_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10170);
--select * from lecture_details where institute_id = 10170;
----delete from lecture_details where institute_id = 10170;

----Homework deletion queries
--select * from homework_submission_details where homework_id in (select homework_id from homework_details where institute_id = 10170);
----delete from homework_submission_details where homework_id in (select homework_id from homework_details where institute_id = 10170);
--select * from homework_details where institute_id = 10170;
----delete from homework_details where institute_id = 10170;

----Discussion Fourm deletion queries
--select * from channel_details where institute_id = 10170;
----delete from channel_details where institute_id = 10170;
--select * from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10170);
----delete from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10170);
--select * from conversation_details where thread_id in (select thread_id from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10170));
----delete from conversation_details where thread_id in (select thread_id from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10170));

----Notice board deletion queries
--select * from notice_details where institute_id = 10170;
----delete from notice_details where institute_id = 10170;
--select * from notice_entity_mapping where notice_id in (select notice_id from notice_details where institute_id = 10170);
----delete from notice_entity_mapping where notice_id in (select notice_id from notice_details where institute_id = 10170);

----Income & Expense Select queries
--select * from income_expense_category where institute_id = 10170;
--select * from income_expense_entities where institute_id = 10170;
--select * from income_expense_transactions where institute_id = 10170;

--Student Attendance deletion queries
select * from student_attendance_register where institute_id = 10170;
--delete from student_attendance_register where institute_id = 10170;

--Audit Logs Select queries
--select * from audit_logs where institute_id = 10170;

----Salary Management Select queries
--select * from salary_payhead_details where pay_head_id in (select pay_head_id from pay_head_configuration where institute_id = 10170);
--select * from salary_payslip_meta_data where institute_id = 10170;
--select * from staff_advance_transaction_history where institute_id = 10170;
--select * from staff_salary_structure_meta_data where institute_id = 10170;
--select * from staff_salary_structure_payhead_details where structure_id in (select structure_id from staff_salary_structure_meta_data where institute_id = 10170);
--pay_head_configuration -- dont delete

--Staff Attendance deletion queries
--select * from staff_attendance_register where institute_id = 10170;

--Reset Password request
--select * from reset_password_details where institute_id = 10170;
--delete from reset_password_details where institute_id = 10170;

-- Bell Notificaiton
--select * from bell_notification_details where institute_id = 10170;
--delete from bell_notification_details where institute_id = 10170;

--User Management
select * from user_role_mapping where user_id in (select user_id from users where institute_id = 10170 and user_type <> 'ADMIN');
--delete from user_role_mapping where user_id in (select user_id from users where institute_id = 10170 and user_type <> 'ADMIN');
select * from users where institute_id = 10170 and user_type <> 'ADMIN';
delete from users where institute_id = 10170 and user_type <> 'ADMIN';

----Staff Manageement
--select * from staff_details where institute_id = 10170;
--delete from staff_details where institute_id = 10170;
--select * from staff_category where institute_id = 10170;
--select * from staff_department where institute_id = 10170;
--select * from staff_designation where institute_id = 10170;

--Examination Marks feeding deletion queries
select * from marks_feeding where student_id in (select student_id from students where institute_id = 10170);
--delete from marks_feeding where student_id in (select student_id from students where institute_id = 10170);


----Inventory deletion queries
--select * from transactions where institute_id = 10170;
----delete from transactions where institute_id = 10170;
--select * from product_group where institute_id = 10170;
----delete from product_group where institute_id = 10170;
--select * from vendors where institute_id = 10170;
----delete from vendors where institute_id = 10170;
--
--select * from brands where institute_id = 10170;
----delete from brands where institute_id = 10170;
--select * from brand_category_mapping where brand_id in (select brand_id from brands where institute_id = 10170);
----delete from brand_category_mapping where brand_id in (select brand_id from brands where institute_id = 10170);
--
--select * from products where institute_id = 10170;
----delete from products where institute_id = 10170;
--select * from product_group_sku_mapping where sku_id in (select sku_id from products where institute_id = 10170);
--
--select * from product_transaction_metadata where institute_id = 10170;
----delete from product_transaction_metadata where institute_id = 10170;
--select * from product_transaction_details where transaction_id in (select transaction_id from product_transaction_metadata where institute_id = 10170);
----delete from product_transaction_details where transaction_id in (select transaction_id from product_transaction_metadata where institute_id = 10170);

--Fees Assignemnt deletion
select * from fee_payment_transaction_amounts where transaction_id in (select transaction_id from fee_payment_transactions  where institute_id = 10170);
--delete from fee_payment_transaction_amounts where transaction_id in (select transaction_id from fee_payment_transactions  where institute_id = 10170);
select * from fee_payment_transactions where institute_id = 10170;
--delete from fee_payment_transactions where institute_id = 10170;
select * from student_fee_payments where institute_id = 10170;
--delete from student_fee_payments where institute_id = 10170;
select * from fee_assignment where institute_id = 10170;
delete from fee_assignment where institute_id = 10170;

--User wallet deletion
select * from user_wallet_transaction_history where institute_id = 10170;
select * from user_wallet where institute_id = 10170;
delete from user_wallet where institute_id = 10170;

--Student details deletion
select * from student_academic_session_details where academic_session_id = 77;
delete from student_academic_session_details where academic_session_id = 77;
select * from students where institute_id = 10170;
delete from students where institute_id = 10170;




--10171
--updating counters queries
--select * from counters where institute_id = 10171;
--insert into counters (institute_id, counter_type, count, counter_prefix) values (10171, 'SMS_COUNTER', 0, "");
----select * from counters where institute_id = 10171 and counter_type = 'ADMISSION_NUMBER';
----update counters set count = 1 where institute_id = 10171 and counter_type = 'ADMISSION_NUMBER';
----update counters set count = 1 where institute_id = 10171 and counter_type = 'FEE_INVOICE';
----update counters set count = 1 where institute_id = 10171 and counter_type = 'REGISTRATION_NUMBER';
----update counters set count = 5000 where institute_id = 10171 and counter_type = 'SMS_COUNTER';
----update counters set count = 1 where institute_id = 10171 and counter_type = 'STAFF_NUMBER';
--select * from counters where institute_id = 10171;

--Notification deletion queries
--select * from notification_status where institute_id = 10171;
--delete from notification_status where institute_id = 10171;

--Optional Course Assignment deletion queries
select * from student_course_assignment where course_id  in (select course_id from class_courses where academic_session_id = 78);
delete from student_course_assignment where course_id  in (select course_id from class_courses where academic_session_id = 78);

----Lecture deletion queries
--select * from student_lecture_view_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10171);
----delete from student_lecture_view_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10171);
--select * from student_lecture_complete_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10171);
----delete from student_lecture_complete_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10171);
--select * from lecture_details where institute_id = 10171;
----delete from lecture_details where institute_id = 10171;

----Homework deletion queries
--select * from homework_submission_details where homework_id in (select homework_id from homework_details where institute_id = 10171);
----delete from homework_submission_details where homework_id in (select homework_id from homework_details where institute_id = 10171);
--select * from homework_details where institute_id = 10171;
----delete from homework_details where institute_id = 10171;

----Discussion Fourm deletion queries
--select * from channel_details where institute_id = 10171;
----delete from channel_details where institute_id = 10171;
--select * from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10171);
----delete from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10171);
--select * from conversation_details where thread_id in (select thread_id from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10171));
----delete from conversation_details where thread_id in (select thread_id from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10171));

----Notice board deletion queries
--select * from notice_details where institute_id = 10171;
----delete from notice_details where institute_id = 10171;
--select * from notice_entity_mapping where notice_id in (select notice_id from notice_details where institute_id = 10171);
----delete from notice_entity_mapping where notice_id in (select notice_id from notice_details where institute_id = 10171);

----Income & Expense Select queries
--select * from income_expense_category where institute_id = 10171;
--select * from income_expense_entities where institute_id = 10171;
--select * from income_expense_transactions where institute_id = 10171;

--Student Attendance deletion queries
select * from student_attendance_register where institute_id = 10171;
--delete from student_attendance_register where institute_id = 10171;

--Audit Logs Select queries
--select * from audit_logs where institute_id = 10171;

----Salary Management Select queries
--select * from salary_payhead_details where pay_head_id in (select pay_head_id from pay_head_configuration where institute_id = 10171);
--select * from salary_payslip_meta_data where institute_id = 10171;
--select * from staff_advance_transaction_history where institute_id = 10171;
--select * from staff_salary_structure_meta_data where institute_id = 10171;
--select * from staff_salary_structure_payhead_details where structure_id in (select structure_id from staff_salary_structure_meta_data where institute_id = 10171);
--pay_head_configuration -- dont delete

--Staff Attendance deletion queries
--select * from staff_attendance_register where institute_id = 10171;

--Reset Password request
--select * from reset_password_details where institute_id = 10171;
--delete from reset_password_details where institute_id = 10171;

-- Bell Notificaiton
--select * from bell_notification_details where institute_id = 10171;
--delete from bell_notification_details where institute_id = 10171;

--User Management
select * from user_role_mapping where user_id in (select user_id from users where institute_id = 10171 and user_type <> 'ADMIN');
--delete from user_role_mapping where user_id in (select user_id from users where institute_id = 10171 and user_type <> 'ADMIN');
select * from users where institute_id = 10171 and user_type <> 'ADMIN';
delete from users where institute_id = 10171 and user_type <> 'ADMIN';

----Staff Manageement
--select * from staff_details where institute_id = 10171;
--delete from staff_details where institute_id = 10171;
--select * from staff_category where institute_id = 10171;
--select * from staff_department where institute_id = 10171;
--select * from staff_designation where institute_id = 10171;

--Examination Marks feeding deletion queries
select * from marks_feeding where student_id in (select student_id from students where institute_id = 10171);
--delete from marks_feeding where student_id in (select student_id from students where institute_id = 10171);


----Inventory deletion queries
--select * from transactions where institute_id = 10171;
----delete from transactions where institute_id = 10171;
--select * from product_group where institute_id = 10171;
----delete from product_group where institute_id = 10171;
--select * from vendors where institute_id = 10171;
----delete from vendors where institute_id = 10171;
--
--select * from brands where institute_id = 10171;
----delete from brands where institute_id = 10171;
--select * from brand_category_mapping where brand_id in (select brand_id from brands where institute_id = 10171);
----delete from brand_category_mapping where brand_id in (select brand_id from brands where institute_id = 10171);
--
--select * from products where institute_id = 10171;
----delete from products where institute_id = 10171;
--select * from product_group_sku_mapping where sku_id in (select sku_id from products where institute_id = 10171);
--
--select * from product_transaction_metadata where institute_id = 10171;
----delete from product_transaction_metadata where institute_id = 10171;
--select * from product_transaction_details where transaction_id in (select transaction_id from product_transaction_metadata where institute_id = 10171);
----delete from product_transaction_details where transaction_id in (select transaction_id from product_transaction_metadata where institute_id = 10171);

--Fees Assignemnt deletion
select * from fee_payment_transaction_amounts where transaction_id in (select transaction_id from fee_payment_transactions  where institute_id = 10171);
--delete from fee_payment_transaction_amounts where transaction_id in (select transaction_id from fee_payment_transactions  where institute_id = 10171);
select * from fee_payment_transactions where institute_id = 10171;
--delete from fee_payment_transactions where institute_id = 10171;
select * from student_fee_payments where institute_id = 10171;
--delete from student_fee_payments where institute_id = 10171;
select * from fee_assignment where institute_id = 10171;
delete from fee_assignment where institute_id = 10171;

--User wallet deletion
select * from user_wallet_transaction_history where institute_id = 10171;
select * from user_wallet where institute_id = 10171;
delete from user_wallet where institute_id = 10171;

--Student details deletion
select * from student_academic_session_details where academic_session_id = 78;
delete from student_academic_session_details where academic_session_id = 78;
select * from students where institute_id = 10171;
delete from students where institute_id = 10171;