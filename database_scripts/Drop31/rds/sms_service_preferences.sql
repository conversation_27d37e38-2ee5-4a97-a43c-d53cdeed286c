select * from configuration where config_type = 'sms_preferences' and config_key = 'sms_provider';

--Institute using webpay (Prod is currently using webpay only for all institute)
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('G<PERSON><PERSON><PERSON><PERSON>', 'G<PERSON><PERSON><PERSON><PERSON>', 'sms_preferences', 'sms_sender_service_username', 'embrate');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GL<PERSON><PERSON><PERSON>', 'GLOBAL', 'sms_preferences', 'sms_sender_service_apikey', '70D92-C6585');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GL<PERSON>BAL', 'GLOBAL', 'sms_preferences', 'sms_sender_service_sender_id', 'EMBRTE');

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10065', 'sms_preferences', 'sms_sender_service_username', 'embrate');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10065', 'sms_preferences', 'sms_sender_service_apikey', '70D92-C6585');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10065', 'sms_preferences', 'sms_sender_service_sender_id', 'EMBRTE');

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '701', 'sms_preferences', 'sms_sender_service_username', 'embrate');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '701', 'sms_preferences', 'sms_sender_service_apikey', '70D92-C6585');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '701', 'sms_preferences', 'sms_sender_service_sender_id', 'EMBRTE');


--Institute using pcexpert
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'sms_preferences', 'sms_sender_service_apikey', 'vPv7rSrW9UOFXQ4UiOJWrQ');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'sms_preferences', 'sms_sender_service_sender_id', 'EMBRTE');


--Institute using Msg91
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'sms_preferences', 'sms_sender_service_apikey', '272324AqKCy0JGrQuv5cb2bf12');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'sms_preferences', 'sms_sender_service_sender_id', 'EMBRTE');
