CREATE TABLE IF NOT EXISTS gate_pass_metadata(
	institute_id int NOT NULL,
	academic_session_id int NOT NULL,
	gate_pass_id varchar(36) PRIMARY KEY NOT NULL,
	gate_pass_number varchar(255) NOT NULL,
	gate_pass_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	name  varchar(500) NOT NULL,
	contact_number varchar(255),
	email_id varchar(255),
	address varchar(500),
	reason varchar(500) NOT NULL,
	relation varchar(500),
	status varchar(255) NOT NULL,
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);

CREATE TABLE IF NOT EXISTS gate_pass_student_mapping(
	gate_pass_id varchar(36) NOT NULL,
	student_id varchar(36) NOT NULL,
	vehicle_json text,
	FOREIGN KEY (gate_pass_id) REFERENCES gate_pass_metadata(gate_pass_id),
	FOREIG<PERSON> KEY (student_id) REFERENCES students(student_id)
);

--insert into counters (institute_id, counter_type, count, counter_prefix) values (702, 'GATE_PASS_NUMBER', 1, "");
--add module in configuration of institute
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'sms_preferences', 'early_departure_sms_enabled', 'true');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'sms_preferences', 'early_departure_sms_template_id', 'a2806607-6f96-4ae6-995f-98211d0431d2');