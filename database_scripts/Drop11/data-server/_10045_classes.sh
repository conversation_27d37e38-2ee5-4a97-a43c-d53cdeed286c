
INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10045",
  "standardName": "Class V",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10045",
  "standardName": "Class VI",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10045",
  "standardName": "Class VII",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10045",
  "standardName": "Class VIII",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10045",
  "standardName": "Class IX",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10045",
  "standardName": "Class X",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : [{"sectionName" : "A"}]
}' $INSTITUTE_STANDARDS_URL


