use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code, organisation_id)
VALUES (103,'EMBRATE INTERNATIONAL COLLEGE - BBA', '', '', '', 'Rajasthan', 'India', '', '', '', '', '', '', '', UUID(), '19f0a280-a9c8-11ec-80e5-0a79d8bf09e3');

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(103, 2022, 2023, 8, 7);

insert into fee_category (institute_id, fee_category, description) values (103, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (103, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (103, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (103, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (103, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (103, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (103, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(103, (SELECT fee_category_id from fee_category where institute_id = 103 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (103,'stationery',0);
insert into categories (institute_id,category_name,genders) values (103,'books',0);
insert into categories (institute_id,category_name) values (103,'clothing');
insert into categories (institute_id,category_name) values (103,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (103,'note book',0,0);
insert into categories (institute_id,category_name) values (103,'art & craft');
insert into categories (institute_id,category_name) values (103,'personal care');
insert into categories (institute_id,category_name) values (103,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (103,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (103,'accessories');
insert into categories (institute_id,category_name) values (103,'furniture');
insert into categories (institute_id,category_name) values (103,'electronics');
insert into categories (institute_id,category_name) values (103,'sports');


insert into colors (institute_id, color_name) values (103,'maroon');
insert into colors (institute_id, color_name) values (103,'black');
insert into colors (institute_id, color_name) values (103,'brown');
insert into colors (institute_id, color_name) values (103,'white');
insert into colors (institute_id, color_name) values (103,'red');
insert into colors (institute_id, color_name) values (103,'yellow');
insert into colors (institute_id, color_name) values (103,'blue');
insert into colors (institute_id, color_name) values (103,'navy blue');
insert into colors (institute_id, color_name) values (103,'green');
insert into colors (institute_id, color_name) values (103,'dark green');
insert into colors (institute_id, color_name) values (103,'pink');
insert into colors (institute_id, color_name) values (103,'purple');
insert into colors (institute_id, color_name) values (103,'grey');
insert into colors (institute_id, color_name) values (103,'olive');
insert into colors (institute_id, color_name) values (103,'cyan');
insert into colors (institute_id, color_name) values (103,'magenta');


insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '103', 'meta_data', 'enable_permission', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '103', 'exam_admit_card_preferences', 'include_class_roll_number', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '103', 'meta_data', 'institute_name_in_sms', 'EMBRATE BBA COLLEGE');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '103', 'meta_data', 'institute_unique_code', 'ebc103');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '103', 'meta_data', 'sms_service_enabled', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '103', 'sms_preferences', 'buffer_sms_count', 0);
insert into configuration (entity, entity_id, config_type, config_key, config_value) values('INSTITUTE', '103', 'meta_data', 'module_access', '["STORE","FEES","ADMISSION","TRANSPORT","COURSES","EXAMINATION","ATTENDANCE","STAFF_MANAGEMENT","INCOME_EXPENSE","USER_MANAGEMENT","LECTURE_MANAGEMENT","HOMEWORK_MANAGEMENT","SALARY_MANAGEMENT","NOTICE_BOARD_MANAGEMENT","STUDENT_MANAGEMENT","AUDIT_LOGS", "COMMUNICATION", "TIMETABLE_MANAGEMENT","STAFF_ATTENDANCE" ]');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values('INSTITUTE', '103', 'meta_data', 'registration_counter', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values('INSTITUTE', '103', 'meta_data', 'admission_counter', 'true');

insert into counters (institute_id, counter_type, count, counter_prefix) values (103, 'FEE_INVOICE', 1, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (103, 'AUDIO_VOICE_CALL_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (103, 'SMS_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values(103, 'REGISTRATION_NUMBER', 1, '');
insert into counters (institute_id, counter_type, count, counter_prefix) values(103, 'ADMISSION_NUMBER', 1, '');

insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(103, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(103, "Grade", "SYSTEM", "GRADE", 1);

--insert into default_fee_assignment_structure_meta_data values(103, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=103 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 10105 and fee_type = "REGULAR" and academic_session_id = 44;


--add range display name also
SET @institute_id := 103;
SET @academic_session_id := 44;
SET @course_type := "SCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A1', 9, 0.905, 1.1, '91-100' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A2', 8, 0.805, 0.905, '81-90' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B1', 7, 0.705, 0.805, '71-80' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B2', 6, 0.605, 0.705, '61-70' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C1', 5, 0.505 , 0.605, '51-60' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C2', 4, 0.405 , 0.505, '41-50' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 3, 0.325 , 0.405, '33-40' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E1', 0, 0.205 , 0.325, '21-32' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E2', 0, 0.0 , 0.205, '00-20' from standards where institute_id = @institute_id;


-- Setting CO-SCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 10, 0.9, 1.1, '91-100' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 9, 0.8, 0.9, '81-90' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 8, 0.7, 0.8, '71-80' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 7, 0.6, 0.7, '61-70' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 6, 0.0, 0.6, '00-60' from standards where institute_id = @institute_id;


insert into standards_metadata select 103, 44, standard_id, 1, 0 from standards where institute_id = 103;


--select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=103 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 103 and fee_type = "REGULAR" and academic_session_id = 44;
--insert into default_fee_assignment_structure select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=103 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 103 and fee_type = "REGULAR" and academic_session_id = 44;

