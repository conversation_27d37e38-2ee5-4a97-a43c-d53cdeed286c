use lernen;

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country,zipcode, landmark, logo_url, email, phone_number) VALUES (10001,'SESOMU', 'National Highway 11', 'Sri Dungargarh', 'Bikaner', 'Rajasthan', 'India', '331803',null, null, '<EMAIL>', '01565-223025');


insert into categories (institute_id,category_name,genders) values (10001,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10001,'books',0);
insert into categories (institute_id,category_name) values (10001,'clothing');
insert into categories (institute_id,category_name) values (10001,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10001,'note book',0,0);
insert into categories (institute_id,category_name) values (10001,'art & craft');
insert into categories (institute_id,category_name) values (10001,'personal care');
insert into categories (institute_id,category_name) values (10001,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10001,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10001,'accessories');



insert into colors (institute_id, color_name) values (10001,'maroon');
insert into colors (institute_id, color_name) values (10001,'black');
insert into colors (institute_id, color_name) values (10001,'brown');
insert into colors (institute_id, color_name) values (10001,'white');
insert into colors (institute_id, color_name) values (10001,'red');
insert into colors (institute_id, color_name) values (10001,'yellow');
insert into colors (institute_id, color_name) values (10001,'blue');
insert into colors (institute_id, color_name) values (10001,'navy blue');
insert into colors (institute_id, color_name) values (10001,'green');
insert into colors (institute_id, color_name) values (10001,'dark green');
insert into colors (institute_id, color_name) values (10001,'pink');
insert into colors (institute_id, color_name) values (10001,'purple');
insert into colors (institute_id, color_name) values (10001,'grey');
insert into colors (institute_id, color_name) values (10001,'olive');
insert into colors (institute_id, color_name) values (10001,'cyan');
insert into colors (institute_id, color_name) values (10001,'magenta');


// For Demo

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country,zipcode, landmark, logo_url, email, phone_number) VALUES (700,'Lernen', 'National Highway 11', 'Sri Dungargarh', 'Bikaner', 'Rajasthan', 'India', '331803',null, null, '<EMAIL>', '9982370071');


insert into categories (institute_id,category_name,genders) values (700,'stationery',0);
insert into categories (institute_id,category_name,genders) values (700,'books',0);
insert into categories (institute_id,category_name) values (700,'clothing');
insert into categories (institute_id,category_name) values (700,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (700,'note book',0,0);
insert into categories (institute_id,category_name) values (700,'art & craft');
insert into categories (institute_id,category_name) values (700,'personal care');
insert into categories (institute_id,category_name) values (700,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (700,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (700,'accessories');



insert into colors (institute_id, color_name) values (700,'maroon');
insert into colors (institute_id, color_name) values (700,'black');
insert into colors (institute_id, color_name) values (700,'brown');
insert into colors (institute_id, color_name) values (700,'white');
insert into colors (institute_id, color_name) values (700,'red');
insert into colors (institute_id, color_name) values (700,'yellow');
insert into colors (institute_id, color_name) values (700,'blue');
insert into colors (institute_id, color_name) values (700,'navy blue');
insert into colors (institute_id, color_name) values (700,'green');
insert into colors (institute_id, color_name) values (700,'dark green');
insert into colors (institute_id, color_name) values (700,'pink');
insert into colors (institute_id, color_name) values (700,'purple');
insert into colors (institute_id, color_name) values (700,'grey');
insert into colors (institute_id, color_name) values (700,'olive');
insert into colors (institute_id, color_name) values (700,'cyan');
insert into colors (institute_id, color_name) values (700,'magenta');

