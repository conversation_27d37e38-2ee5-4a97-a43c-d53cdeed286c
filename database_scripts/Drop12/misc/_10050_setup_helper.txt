
sudo java -Dlernen_env=prod -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.ingest.student.IngestStudents10050 -i 10050 -f /tmp/10050_10051_student_data.csv -cs 13 -m hindi


sudo java -D<PERSON>nen_env=prod -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.ingest.student.IngestStudents10050 -i 10051 -f /tmp/10050_10051_student_data.csv -cs 14 -m english



sudo java -Dlernen_env=prod -cp dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.misc.MultipleDataUpdateCreator10050 -i 10050 -f /tmp/10050_student_fee_payment.csv -s 13 -h -a update_student -user "7202feca-0b01-4784-9dd5-32c149db944c"
