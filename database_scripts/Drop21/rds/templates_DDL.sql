CREATE TABLE IF NOT EXISTS communication_templates (
    template_id varchar(36) NOT NULL,
    entity_name varchar(255) NOT NULL,
    entity_id varchar(255) NOT NULL,
    delivery_mode varchar(255) NOT NULL,
    template_type varchar(255) NOT NULL,
    template_name varchar(255) NOT NULL,
    locale varchar(255) NOT NULL,
    version int NOT NULL,
    template_value LONGTEXT NOT NULL,
    status varchar(255) NOT NULL,
    metadata text,
    added_by varchar(36),
    added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    description text,
    PRIMARY KEY (template_id),
    UNIQUE KEY (entity_id, delivery_mode, template_type, template_name, locale, version)
);


CREATE TABLE IF NOT EXISTS service_provider_templates (
    service_provider varchar(255) NOT NULL,
    template_id varchar(36) NOT NULL,
    external_template_id varchar(255) NOT NULL,
    status varchar(255) NOT NULL,
    metadata text,
    added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON>AR<PERSON>EY (service_provider, template_id),
    FOREIGN KEY (template_id) REFERENCES communication_templates(template_id)
);


ALTER TABLE communication_templates ADD template_var text AFTER status;

-- ALTER TABLE communication_templates ADD added_by varchar(36) AFTER metadata;
-- ALTER TABLE communication_templates ADD added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER added_by;
-- ALTER TABLE communication_templates ADD description text AFTER added_at;
-- ALTER TABLE service_provider_templates ADD added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER metadata;
