POST : http://127.0.0.1:8080/data-server/2.0/examination/marks-report-structure/9e9673cb-db8f-4654-a836-e73cccc67455/ANNUAL?institute_id=10015&academic_session_id=7

{
  "examReportStructureMetaData": {
    "templateClass": "com.lernen.cloud.pdf.exam.reports._10001.ExamReportGenerator10001"
  },
  "examReportHeaderStructureColumns": {
    "SCHOLASTIC": [
      {
        "id": "parent",
        "title": "Exam Set",
        "examReportGridColumnType": "STATIC",
        "childExamReportHeaderStructureColumns": [
          {
            "id": "1",
            "examReportGridColumnType": "EXAM",
            "examId": "31672142-8a3c-437b-a79d-ecca53258ff6"
          },
          {
            "id": "2",
            "examReportGridColumnType": "EXAM",
            "examId": "3b696a11-ccb5-4e68-89bf-4bfd3033fe95"
          },
          {
            "id": "3",
            "title": "Total X",
            "examReportGridColumnType": "SUM",
            "computationColumns": [
              "1",
              "2"
            ]
          }
        ]
      }
    ]
  }
}




POST: http://127.0.0.1:8080/data-server/2.0/examination/marks-report-structure/9c7e1079-b25b-4e59-9352-a1e000c978cb/ANNUAL?institute_id=10015&academic_session_id=7

{
  "examReportStructureMetaData": {
    "templateClass": "com.lernen.cloud.pdf.exam.reports._10001.ExamReportGenerator10001"
  },
  "examReportHeaderStructureColumns": {
    "SCHOLASTIC": [
      {
        "id": "termI",
        "title": "Term I (100 Marks)",
        "examReportGridColumnType": "STATIC",
        "childExamReportHeaderStructureColumns": [
          {
            "id": "t11",
            "title": "PT",
            "examReportGridColumnType": "EXAM",
            "examId": "1e9b05a4-0d9c-458a-89a1-a095fb198a81"
          },
          {
            "id": "t12",
            "title": "NB",
            "examReportGridColumnType": "EXAM",
            "examId": "9542d8aa-3305-4ab3-838f-fc67c6002527"
          },
          {
            "id": "t13",
            "title": "SE",
            "examReportGridColumnType": "EXAM",
            "examId": "85763e34-10c4-4735-a130-febdcd9c8b6a"
          },
          {
            "id": "t14",
            "title": "HY",
            "examReportGridColumnType": "EXAM",
            "examId": "2461b6af-4584-4530-bd58-7143048f46c0"
          },
          {
            "id": "t1Total",
            "title": "Total",
            "examReportGridColumnType": "SUM",
            "computationColumns": [
              "t11",
              "t12",
              "t13",
              "t14"
            ]
          }
        ]
      },
      {
        "id": "termII",
        "title": "Term II (100 Marks)",
        "examReportGridColumnType": "STATIC",
        "childExamReportHeaderStructureColumns": [
          {
            "id": "t21",
            "title": "PT",
            "examReportGridColumnType": "EXAM",
            "examId": "26f47e5e-5d28-4f7b-b34d-f8c5daf78878"
          },
          {
            "id": "t22",
            "title": "NB",
            "examReportGridColumnType": "EXAM",
            "examId": "ed89afe0-57a7-4dbb-87e6-1bf56c6bfe22"
          },
          {
            "id": "t23",
            "title": "SE",
            "examReportGridColumnType": "EXAM",
            "examId": "eb3cf5e4-2f8c-4d1b-abdf-1dd6189430a5"
          },
          {
            "id": "t24",
            "title": "YL",
            "examReportGridColumnType": "EXAM",
            "examId": "31dc588a-f074-40fc-bccc-c9a7d025cf44"
          },
          {
            "id": "t2Total",
            "title": "Total",
            "examReportGridColumnType": "SUM",
            "computationColumns": [
              "t21",
              "t22",
              "t23",
              "t24"
            ]
          }
        ]
      },
     {
        "id": "overall",
        "title": "OVERALL",
        "examReportGridColumnType": "STATIC",
        "childExamReportHeaderStructureColumns": [
          {
            "id": "t1+t2",
            "title": "TI + TII",
            "examReportGridColumnType": "STATIC",
            "childExamReportHeaderStructureColumns": [
              {
                "id": "overllTotal",
                "title": "Net Total",
                "examReportGridColumnType": "SUM",
                "computationColumns": [
                  "termI",
                  "termII"
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}









{
  "examReportStructureMetaData": {
    "templateClass": "com.lernen.cloud.pdf.exam.reports._10001.ExamReportGenerator10001",
    "showCourseMaxMarks" : false
  },
  "examReportHeaderStructureColumns": {
    "SCHOLASTIC": [
      {
        "id": "termI",
        "title": "Term I (100 Marks)",
        "examReportGridColumnType": "STATIC",
        "childExamReportHeaderStructureColumns": [
          {
            "id": "t11",
            "title": "PT",
            "examReportGridColumnType": "EXAM",
            "examId": "1e9b05a4-0d9c-458a-89a1-a095fb198a81",
            "obtainedMarksDimensions" : []
          },
          {
            "id": "t12",
            "title": "NB",
            "examReportGridColumnType": "EXAM",
            "examId": "9542d8aa-3305-4ab3-838f-fc67c6002527"
          },
          {
            "id": "t13",
            "title": "SE",
            "examReportGridColumnType": "EXAM",
            "examId": "85763e34-10c4-4735-a130-febdcd9c8b6a"
          },
          {
            "id": "t14",
            "title": "HY",
            "examReportGridColumnType": "EXAM",
            "examId": "2461b6af-4584-4530-bd58-7143048f46c0"
          },
          {
            "id": "t1Total",
            "title": "Total",
            "examReportGridColumnType": "SUM",
            "computationColumns": [
              "t11",
              "t12",
              "t13",
              "t14"
            ]
          }
        ]
      },
      {
        "id": "termII",
        "title": "Term II (100 Marks)",
        "examReportGridColumnType": "STATIC",
        "childExamReportHeaderStructureColumns": [
          {
            "id": "t21",
            "title": "PT",
            "examReportGridColumnType": "EXAM",
            "examId": "26f47e5e-5d28-4f7b-b34d-f8c5daf78878"
          },
          {
            "id": "t22",
            "title": "NB",
            "examReportGridColumnType": "EXAM",
            "examId": "ed89afe0-57a7-4dbb-87e6-1bf56c6bfe22"
          },
          {
            "id": "t23",
            "title": "SE",
            "examReportGridColumnType": "EXAM",
            "examId": "eb3cf5e4-2f8c-4d1b-abdf-1dd6189430a5"
          },
          {
            "id": "t24",
            "title": "YL",
            "examReportGridColumnType": "EXAM",
            "examId": "31dc588a-f074-40fc-bccc-c9a7d025cf44"
          },
          {
            "id": "t2Total",
            "title": "Total",
            "examReportGridColumnType": "SUM",
            "computationColumns": [
              "t21",
              "t22",
              "t23",
              "t24"
            ]
          }
        ]
      },
      {
        "id": "overall",
        "title": "OVERALL",
        "examReportGridColumnType": "STATIC",
        "childExamReportHeaderStructureColumns": [
          {
            "id": "t1+t2",
            "title": "TI + TII",
            "examReportGridColumnType": "STATIC",
            "childExamReportHeaderStructureColumns": [
              {
                "id": "overllTotal",
                "title": "Net Total",
                "examReportGridColumnType": "SUM",
                "computationColumns": [
                  "termI",
                  "termII"
                ]
              },
              {
                "id": "t1+t2_percentage",
                "title": "%",
                "examReportGridColumnType": "PERCENT",
                "computationColumns": [
                  "overllTotal"
                ]
              },
              {
                "id": "t1+t2_grade",
                "title": "Grade",
                "examReportGridColumnType": "GRADE",
                "computationColumns": [
                  "overllTotal"
                ]
              }
            ]
          }
        ]
      }
    ],
    "COSCHOLASTIC": [
      {
        "id": "termI",
        "title": "Term I",
        "examReportGridColumnType": "EXAM",
        "examId" : "57a715b2-f081-4841-8048-a338de9551ba"
  	  },
      {
        "id": "termII",
        "title": "Term II",
        "examReportGridColumnType": "EXAM",
        "examId" : "f3972a6e-54d0-4886-ae8e-36eae042f79c"
  	  },
      {
        "id": "total",
        "title": "Total",
        "examReportGridColumnType": "SUM",
        "computationColumns" : ["termI", "termII"]
  	  }
    ]
  }
}












{
  "examReportStructureMetaData": {
    "templateClass": "com.lernen.cloud.pdf.exam.reports.ExamReportGenerator10005"
  },
  "examReportHeaderStructureColumns": {
    "SCHOLASTIC" : [
      {
        "id": "1",
        "title": "",
        "examReportGridColumnType": "EXAM",
        "examId" :"4b19c87e-991a-4446-8bcb-4e5bec6a31e2"
      },
      {
        "id": "2",
        "title": "",
        "examReportGridColumnType": "EXAM",
        "examId" :"0d246ec5-c1c9-42de-921f-0cea8a7aa700"
      },
      {
        "id": "HY",
        "title": "",
        "examReportGridColumnType": "EXAM",
        "examId" :"624ab39b-f5a9-4e96-a4b3-67bf6cd4bfdc"
      },
      {
        "id": "3",
        "title": "",
        "examReportGridColumnType": "EXAM",
        "examId" :"b0f36e7c-3bdb-495d-8450-78535cd5c15a"
      },
      {
        "id": "htotal",
        "title": "Total",
        "examReportGridColumnType": "SUM",
        "computationColumns" : ["1","2","HY","3"]
      },
      {
        "id": "Y",
        "title": "",
        "examReportGridColumnType": "EXAM",
        "examId" :"5db7044c-a296-4262-a383-40f3b5bfdee5"
      },
      {
        "id": "ytotal",
        "title": "Grand Total",
        "examReportGridColumnType": "SUM",
        "computationColumns" : ["htotal","Y"]
      },
      {
        "id": "grade",
        "title": "Grade",
        "examReportGridColumnType": "GRADE",
        "computationColumns" : ["ytotal"]
      }
    ],
    "COSCHOLASTIC": [
      {
        "id": "annual",
        "title": "",
        "examReportGridColumnType": "EXAM",
        "examId" :"5db7044c-a296-4262-a383-40f3b5bfdee5"
      }
    ]
  }
}