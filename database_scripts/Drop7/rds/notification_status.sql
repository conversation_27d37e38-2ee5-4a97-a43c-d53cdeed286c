CREATE TABLE IF NOT EXISTS notification_status (
notification_id varchar(36) NOT NULL PRIMARY KEY,
institute_id int NOT NULL,
user_id varchar(36) NOT NULL,
academic_session_id int,
notification_type varchar(225) NOT NULL,
delivery_mode varchar(225) NOT NULL,
delivery_destination varchar(516) NOT NULL,
batch_id varchar(36),
batch_name varchar(225),
notification_title text,
notification_content longtext,
status varchar(36),
delivered TIMESTAMP NULL,
generation TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
last_opened TIMESTAMP NULL,
last_clicked TIMESTAMP NULL,
external_unique_id text,
meta_data longtext
);

-- To support unicode charaters in notification content
-- https://mathiasbynens.be/notes/mysql-utf8mb4#utf8-to-utf8mb4
ALTER TABLE notification_status CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;