CREATE TABLE IF NOT EXISTS payment_gateway_merchant_details (
    merchant_id varchar(36) NOT NULL,
    institute_id int NOT NULL,
    payment_gateway varchar(128) NOT NULL,
    merchant_name varchar(1024) NOT NULL,
    merchant_key varchar(1024) NOT NULL,
    merchant_secret varchar(1024) NOT NULL,
    status varchar(255) NOT NULL,
    metadata text,
    added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    description text,
    PRIMARY KEY (merchant_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);
