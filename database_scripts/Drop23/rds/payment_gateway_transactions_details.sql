CREATE TABLE IF NOT EXISTS payment_gateway_transactions (
    transaction_id varchar(36) NOT NULL,
    institute_id int NOT NULL,
    payment_gateway varchar(128) NOT NULL,
    merchant_id varchar(36) NOT NULL,
    user_id varchar(36) NOT NULL,
    user_type varchar(36) NOT NULL,
    transaction_type varchar(1024) NOT NULL,
    stage varchar(36) NOT NULL,
    txn_amount double NOT NULL,
    txn_currency varchar(36) NOT NULL,
    wallet_amount double,
    status varchar(255) NOT NULL,
    token text,
    metadata text,
    added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description text,
    error_code int,
    error_reason text,
    lock_acquired boolean,
    PRIMARY KEY (transaction_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (merchant_id) REFERENCES payment_gateway_merchant_details(merchant_id)
);

CREATE INDEX payment_gateway_transactions_user_id ON payment_gateway_transactions (user_id);