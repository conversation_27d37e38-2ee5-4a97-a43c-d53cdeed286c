package com.embrate.cloud.lambda.utils.api;

/**
 * <AUTHOR>
 */
public enum AWSLambdaRequestConstants {

    HANDLER_ID("handler_id"),

    INSTITUTE_ID("institute_id"),

    ACADEMIC_SESSION_ID("academic_session_id"),

    STANDARD_ID("standard_id"),

    SECTION_IDS("section_ids"),

    STUDENT_IDS("student_ids"),

    USER_ID("user_id"),

    EXAM_ID("exam_id"),

    ADMIT_CARD_TYPE("admit_card_type"),

    REPORT_CARD_TYPE("report_card_type"),

    REPORT_DATA_NAME("report_card_name"),

    COUNT_PER_PAGE("count_per_page"),

    HPC_EXAM_TYPE("hpc_exam_type");

    private final String id;

    AWSLambdaRequestConstants(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }
}
