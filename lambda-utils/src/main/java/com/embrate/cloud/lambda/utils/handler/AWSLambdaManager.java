package com.embrate.cloud.lambda.utils.handler;

import com.embrate.cloud.core.lib.filesystem.S3FileSystem;
import com.embrate.cloud.lambda.utils.router.AWSLambdaRequestRouter;
import com.google.common.base.CharMatcher;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.aws.lambda.AWSLambdaInvoker;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class AWSLambdaManager {

    private static final Logger logger = LogManager.getLogger(AWSLambdaManager.class);

    private final S3FileSystem s3FileSystem;
    private final AWSLambdaInvoker awsLambdaInvoker;

    private final String pdfHandlerFunctionName;

    private final String lambdaUtilsFunctionName;

    public AWSLambdaManager(S3FileSystem s3FileSystem, AWSLambdaInvoker awsLambdaInvoker, String pdfHandlerFunctionName, String lambdaUtilsFunctionName) {
        this.s3FileSystem = s3FileSystem;
        this.awsLambdaInvoker = awsLambdaInvoker;
        this.pdfHandlerFunctionName = pdfHandlerFunctionName;
        this.lambdaUtilsFunctionName = lambdaUtilsFunctionName;
    }

    public String invokePDFFunction(Map<String, String> payload) {
        return awsLambdaInvoker.invoke(lambdaUtilsFunctionName, payload);
    }

    public String invokeLambdaUtilsFunction(Map<String, String> payload) {
        return awsLambdaInvoker.invoke(lambdaUtilsFunctionName, payload);
    }

    public DocumentOutput handlePDFOutput(String response) {
        Pair<String, ByteArrayOutputStream> output = handleOutput(response);
        if (output == null) {
            return null;
        }
        return new DocumentOutput(output.getFirst(), output.getSecond());
    }

    public ReportOutput handleReportOutput(String response) {
        Pair<String, ByteArrayOutputStream> output = handleOutput(response);
        if (output == null) {
            return null;
        }
        return new ReportOutput(output.getFirst(), output.getSecond());
    }

    private Pair<String, ByteArrayOutputStream> handleOutput(String response) {
        logger.info("response from lambda {}", response);
        if (StringUtils.isBlank(response)) {
            logger.error("Error response from lambda");
            return null;
        }
        response = CharMatcher.is('\"').trimFrom(response);
        String[] tokens = response.split("\\|");
        String status = tokens[0];
        if (status.equalsIgnoreCase(AWSLambdaRequestRouter.FAILURE_CODE)) {
            logger.error("Failure code received from lambda");
            return null;
        }
        if (tokens.length > 2) {
            String s3Path = tokens[1];
            if (StringUtils.isBlank(s3Path)) {
                return null;
            }
            String fileName = tokens[2];
            String s3Bucket = s3Path.split("s3://")[1].split("/")[0];
            String s3PathKey = s3Path.split(s3Bucket)[1].substring(1);
            ByteArrayOutputStream documentStream = s3FileSystem.readFile(s3Bucket, s3PathKey);
            s3FileSystem.deleteFile(s3Bucket, s3PathKey);
            logger.info("deleted fileName {}, s3Bucket {}, s3PathKey {} from lambda {}", fileName, s3Bucket, s3PathKey);
            return new Pair(fileName, documentStream);
        }
        return null;
    }
}
