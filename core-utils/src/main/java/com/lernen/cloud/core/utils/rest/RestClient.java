package com.lernen.cloud.core.utils.rest;

import java.net.URI;

import javax.net.ssl.SSLContext;

import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContexts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.jaxrs.json.JacksonJsonProvider;
import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.WebResource;
import com.sun.jersey.api.client.config.ClientConfig;
import com.sun.jersey.api.client.config.DefaultClientConfig;
import com.sun.jersey.client.apache4.ApacheHttpClient4Handler;

/**
 * Wrapper class for Jersey client. It can be used to create
 * {@link RestResource} for a given URI.
 *
 * <AUTHOR>
 * @version $Revision$, $Date$ *
 */
public class RestClient {
	private static final Logger logger = LoggerFactory.getLogger(RestClient.class);
	public static final RestClient REST_CLIENT = new RestClient();
	public static final RestClient REST_CLIENT_WITH_RETRY_HANDLER = new RestClient(true);
	public static final RestClient REST_CLIENT_WITH_TIMEOUT = new RestClient(true, 5000, 5000);
	private static final int MAX_RETRIES = 3;
	private final Client client;

	private RestClient() {
		client = config(false);
	}

	private RestClient(boolean withRetryHandler) {
		client = config(withRetryHandler);
	}

	private RestClient(boolean withRetryHandler, int socketTimeout, int connectionTimeout) {
		client = config(withRetryHandler, socketTimeout, connectionTimeout);
	}

	public WebResource resource(URI uri) {
		return client.resource(uri);
	}

	public Client getClient() {
		return client;
	}

	public Client config(boolean withRetryHandler) {
		int connectionTimeOut = 0;

		if (System.getProperty("connectionTimeOut") != null) {
			connectionTimeOut = Integer.valueOf(System.getProperty("connectionTimeOut"));
			logger.info("Setting connectionTimeOut value: {}", connectionTimeOut);
		}

		int socketTimeOut = 0;

		if (System.getProperty("socketTimeOut") != null) {
			socketTimeOut = Integer.valueOf(System.getProperty("socketTimeOut"));
			logger.info("Setting socketTimeOut value: {}", socketTimeOut);
		}
		return config(withRetryHandler, socketTimeOut, connectionTimeOut);
	}

	public Client config(boolean withRetryHandler, int socketTimeOut, int connectionTimeOut) {
		final int maxPerRoute = 50;
		final int maxTotal = 50;
		logger.info("Initializing RestClient with ConnectionPool maxConnections: {} maxPerRoute: {}", maxTotal,
				maxPerRoute);
		final SSLContext sslContext = SSLContexts.createSystemDefault();
		final SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext,
				NoopHostnameVerifier.INSTANCE);
		final Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder
				.<ConnectionSocketFactory> create().register("https", sslsf)
				.register("http", new PlainConnectionSocketFactory()).build();
		final PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(
				socketFactoryRegistry);
		connManager.setDefaultMaxPerRoute(maxPerRoute);
		connManager.setMaxTotal(maxTotal);

		final RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(connectionTimeOut)
				.setSocketTimeout(socketTimeOut).setCookieSpec(CookieSpecs.IGNORE_COOKIES).build();
		final CloseableHttpClient httpClient = HttpClientBuilder.create().setConnectionManager(connManager)
				.setDefaultRequestConfig(requestConfig).build();

		final ApacheHttpClient4Handler clientHandler = new ApacheHttpClient4Handler(httpClient, null, false);
		final ClientConfig config = new DefaultClientConfig();
		config.getFeatures().put("com.sun.jersey.api.json.POJOMappingFeature", true);
		config.getClasses().add(JacksonJsonProvider.class);
		final Client client = new Client(clientHandler, config);
		client.addFilter(new RestPerfFilter());
		client.addFilter(new RestLoggerFilter());
		if (withRetryHandler) {
			client.addFilter(new RestRetryFilter(MAX_RETRIES));
		}
		return client;
	}

}