package com.lernen.cloud.core.utils.attendance;

import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.attendance.AttendanceType;
import com.lernen.cloud.core.api.attendance.StudentAttendancePayload;
import com.lernen.cloud.core.api.attendance.preference.AttendancePreferences;
import com.lernen.cloud.core.utils.StringHelper;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */

public class AttendanceUtils {

    private static final Map<String, AttendanceStatus> COMPUTATION_ATTENDANCE_STATUS_MAP = new HashMap<>();

    static {
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("0,0", AttendanceStatus.PRESENT);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("0,2", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("1,2", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("3,2", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("1,0", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("1,2", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("1,1", AttendanceStatus.ABSENT);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("1,3", AttendanceStatus.LEAVE);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("2,0", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("2,2", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("2,1", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("2,3", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("3,0", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("3,2", AttendanceStatus.HALF_DAY);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("3,1", AttendanceStatus.LEAVE);
        COMPUTATION_ATTENDANCE_STATUS_MAP.put("3,3", AttendanceStatus.LEAVE);

    }
    public static List<AttendanceType> getAttendanceTypes(List<AttendanceType> attendanceTypes, UUID standardId) {
        List<AttendanceType> applicableAttendanceTypes = new ArrayList<>();
        if (CollectionUtils.isEmpty(attendanceTypes)) {
            return applicableAttendanceTypes;
        }

        for (AttendanceType attendanceType : attendanceTypes) {
            if (CollectionUtils.isEmpty(attendanceType.getStandards())) {
                applicableAttendanceTypes.add(attendanceType);
            } else if (attendanceType.getStandards().contains(standardId)) {
                applicableAttendanceTypes.add(attendanceType);
            }
        }

        return applicableAttendanceTypes;
    }

    public static AttendanceStatus computeAttendanceStatus(List<AttendanceStatus> attendanceStatusList) {
        if(CollectionUtils.isEmpty(attendanceStatusList)) {
            return null;
        }
        AttendanceStatus computedAttendanceStatus = null;
        for (AttendanceStatus attendanceStatus : attendanceStatusList) {
            computedAttendanceStatus = computeAttendanceStatus(computedAttendanceStatus, attendanceStatus);
        }
        return computedAttendanceStatus;
    }

    public static AttendanceStatus computeAttendanceStatus(AttendanceStatus attendanceStatus1, AttendanceStatus attendanceStatus2) {
        if (attendanceStatus1 == null && attendanceStatus2 == null) {
            return null;
        }

        if (attendanceStatus1 == null) {
            return attendanceStatus2;
        }

        if (attendanceStatus2 == null) {
            return attendanceStatus1;
        }

        return COMPUTATION_ATTENDANCE_STATUS_MAP.get(attendanceStatus1.getCode() + "," + attendanceStatus2.getCode());
    }

    public static boolean computeIfAttendanceSMSSend(AttendancePreferences attendancePreferences, boolean sameDayAttendance,
                                    Set<AttendanceStatus> attendanceStatuesSet, Set<Integer> attendanceTypeSet) {

        boolean attendanceMarkedForConfiguredAttendanceTypeAndStatues = false;
        Set<AttendanceStatus> configuredAttendanceStatuesSet = attendancePreferences.getStudentAttendanceStatusesToSendSMS();
        if(!CollectionUtils.isEmpty(configuredAttendanceStatuesSet)) {
            //Retaining all the statues which we have in marked attendance for
            configuredAttendanceStatuesSet.retainAll(attendanceStatuesSet);
            if(!CollectionUtils.isEmpty(configuredAttendanceStatuesSet)) {
                attendanceMarkedForConfiguredAttendanceTypeAndStatues = true;
            }
        }

        Set<Integer> configuredAttendanceTypeSet = attendancePreferences.getStudentAttendanceTypesToSendSMS();
        if(!CollectionUtils.isEmpty(configuredAttendanceTypeSet)) {
            //Retaining all the attendance types which we have in marked attendance for
            configuredAttendanceTypeSet.retainAll(attendanceTypeSet);
            if(!CollectionUtils.isEmpty(configuredAttendanceTypeSet)) {
                attendanceMarkedForConfiguredAttendanceTypeAndStatues &= true;
            }
        }
        return sameDayAttendance && attendanceMarkedForConfiguredAttendanceTypeAndStatues;
    }

    public static String getAttendanceDetails(Map<UUID, Map<AttendanceStatus, Integer>> studentAttendanceDetailsMap, UUID studentId) {
        Map<AttendanceStatus, Integer> attendanceStatusIntegerMap = studentAttendanceDetailsMap.get(studentId);
        String noOfMeetings = "-";
        String noOfPresent = "-";
        if (attendanceStatusIntegerMap != null) {
            Integer presentCount = attendanceStatusIntegerMap.get(AttendanceStatus.PRESENT);
            Integer absentCount = attendanceStatusIntegerMap.get(AttendanceStatus.ABSENT);
            Integer leaveCount = attendanceStatusIntegerMap.get(AttendanceStatus.LEAVE);
            Integer halfDayCount = attendanceStatusIntegerMap.get(AttendanceStatus.HALF_DAY);
            noOfMeetings = String.valueOf(presentCount + absentCount + leaveCount + halfDayCount);
            noOfPresent = StringHelper.removeTrailingZero(String.valueOf(presentCount + (halfDayCount / 2d)));
        }
        return noOfPresent + "/" + noOfMeetings;
    }
}
