package com.lernen.cloud.core.utils.rest;

import java.net.URI;
import java.util.Map;
import java.util.Map.Entry;

import javax.ws.rs.core.MediaType;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;
import com.sun.jersey.api.client.ClientHandlerException;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.GenericType;
import com.sun.jersey.api.client.UniformInterfaceException;
import com.sun.jersey.api.client.WebResource;

/**
 * Standard handler class for rest API calls (GET, POST, PUT, DELETE)
 * 
 * <AUTHOR>
 *
 */
public class RestAPIHandler {

	private static final Logger logger = LogManager.getLogger(RestAPIHandler.class);
	private final RestClient restClient;
	private static final Gson GSON = new Gson();

	public RestAPIHandler(RestClient restClient) {
		this.restClient = restClient;
	}

	public <T> T get(String url, Map<String, String> header, Map<String, String> queryParams, GenericType<T> responseEntity) {
		ClientResponse response = null;
		try {
			WebResource webResource = restClient.resource(new URI(url));
			webResource = addQueryParam(webResource, queryParams);
			WebResource.Builder webResourceRequestBuilder = webResource.getRequestBuilder();
			webResourceRequestBuilder = addHeader(webResourceRequestBuilder, header);
			webResourceRequestBuilder = webResourceRequestBuilder.type(MediaType.APPLICATION_JSON);
			response = webResourceRequestBuilder.get(ClientResponse.class);
			if (response.getStatus() == HttpStatus.SC_OK) {
				return response.getEntity(responseEntity);
			}
			logger.error("Get call failed with status code {} to URL {}", response.getStatus(), url);
		} catch (UniformInterfaceException | ClientHandlerException e) {
			logger.error("Unable to perform get call to URL {}", url, e);
		} catch (final Exception e) {
			logger.error("Exception occured while performing get call to URL {}", url, e);
		} finally {
			if (response != null) {
				response.close();
			}
		}
		return null;
	}

	public <T> T post(String url, Map<String, String> header, Map<String, String> queryParams, Object payload, GenericType<T> responseEntity) {
		ClientResponse response = null;
		try {
			WebResource webResource = restClient.resource(new URI(url));
			webResource = addQueryParam(webResource, queryParams);
			WebResource.Builder webResourceRequestBuilder = webResource.getRequestBuilder();
			webResourceRequestBuilder = addHeader(webResourceRequestBuilder, header);
			webResourceRequestBuilder = webResourceRequestBuilder.type(MediaType.APPLICATION_JSON);
			response = webResourceRequestBuilder.post(ClientResponse.class, GSON.toJson(payload));
			if (response.getStatus() == HttpStatus.SC_OK) {
				return response.getEntity(responseEntity);
			}
			logger.error("Post call failed with status code {} to URL {}", response.getStatus(), url);
		} catch (UniformInterfaceException | ClientHandlerException e) {
			logger.error("Unable to perform post call to URL {}", url, e);
		} catch (final Exception e) {
			logger.error("Exception occured while performing post call to URL {} for payload {}", url, payload, e);
		} finally {
			if (response != null) {
				response.close();
			}
		}
		return null;
	}

	public <T> T put(String url, Map<String, String> queryParams, Object payload, GenericType<T> responseEntity) {
		ClientResponse response = null;
		try {
			WebResource webResource = restClient.resource(new URI(url));
			webResource = addQueryParam(webResource, queryParams);
			response = webResource.type(MediaType.APPLICATION_JSON).put(ClientResponse.class, GSON.toJson(payload));
			if (response.getStatus() == HttpStatus.SC_OK) {
				return response.getEntity(responseEntity);
			}
			logger.error("PUT call failed with status code {} to URL {}", response.getStatus(), url);
		} catch (UniformInterfaceException | ClientHandlerException e) {
			logger.error("Unable to perform put call to URL {}", url, e);
		} catch (final Exception e) {
			logger.error("Exception occured while performing put call to URL {} for payload {}", url, payload, e);
		} finally {
			if (response != null) {
				response.close();
			}
		}
		return null;
	}

    public static WebResource addQueryParam(WebResource webResource, Map<String, String> queryParams) {
		if (CollectionUtils.isEmpty(queryParams)) {
			return webResource;
		}
		for (Entry<String, String> queryParamEntry : queryParams.entrySet()) {
			webResource = addQueryParam(webResource, queryParamEntry.getKey(), queryParamEntry.getValue());
		}
		return webResource;
	}

	public static WebResource addQueryParam(WebResource webResource, String key, String value) {
		if (StringUtils.isBlank(key) || StringUtils.isBlank(value)) {
			return webResource;
		}
		return webResource.queryParam(key, value);
	}

	public static WebResource.Builder addHeader(WebResource.Builder webResourceBuilder, Map<String, String> headers) {
		if (CollectionUtils.isEmpty(headers)) {
			return webResourceBuilder;
		}
		for (Entry<String, String> header : headers.entrySet()) {
			webResourceBuilder = webResourceBuilder.header(header.getKey(), header.getValue());
		}
		return webResourceBuilder;
	}

}
