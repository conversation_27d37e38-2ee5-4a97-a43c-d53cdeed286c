package com.embrate.cloud.pdf.inventory.store.v2;

import com.embrate.cloud.core.api.inventory.v2.TradeProductBatchSummary;
import com.embrate.cloud.core.api.inventory.v2.TradeProductSummary;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionSummary;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.inventory.InventoryTransactionStatus;
import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.inventory.InventoryUserType;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

import static com.lernen.cloud.core.utils.NumberUtils.*;
import static com.lernen.cloud.core.utils.NumberUtils.formatToRupees;

public class StoreInventoryInvoiceGenerator10420 extends StoreInventoryInvoiceGenerator {
    public StoreInventoryInvoiceGenerator10420(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    private static final Logger logger = LogManager.getLogger(StoreInventoryInvoiceGenerator10420.class);

    public static final float DEFAULT_LOGO_WIDTH = 40f;
    public static final float DEFAULT_LOGO_HEIGHT = 40f;
    public static final String GST_NUMBER = "GST No: 08EYGPR7149R1Z8";
    public static final String STUDENT_COPY = "Student's Copy";
    public static final String OFFICE_COPY = "Office Copy";
    public static final String CUSTOMER_COPY = "Customer's Copy";

    @Override
    public DocumentOutput generateInvoice(Institute institute, InventoryTransactionSummary transactionSummary,
                                          String documentName, boolean officeCopy) {
        int instituteId = institute.getInstituteId();
        try {
            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4.rotate() ,officeCopy);
            float contentFontSize = 8f;
            float headerFontSize = 11f;
            float defaultBorderWidth = 0.1f;
            Document document = initDocument(invoice.getContent(), documentLayoutSetup);

            return generateInvoice(institute, transactionSummary, invoice, documentLayoutSetup, contentFontSize, headerFontSize, defaultBorderWidth, document, true);
        } catch (Exception e) {
            logger.error("Unable to create invoice for transaction {}", transactionSummary.getTransactionId(), e);
        }
        return null;
    }

    public DocumentOutput generateInvoice(Institute institute, InventoryTransactionSummary transactionSummary, DocumentOutput invoice, DocumentLayoutSetup documentLayoutSetup, float contentFontSize, float headerFontSize, float defaultBorderWidth, Document document, boolean includeInstituteName) throws IOException {
        switch (transactionSummary.getTransactionType()) {
            case SALE:
            case SALES_RETURN:
                generateSalesInvoice(document, documentLayoutSetup, institute, headerFontSize, contentFontSize, transactionSummary,
                        defaultBorderWidth, includeInstituteName);
                break;
            case PURCHASE:
            case RETURN:
                generatePurchaseInvoice(document, documentLayoutSetup, institute, headerFontSize, contentFontSize, transactionSummary,
                        defaultBorderWidth, includeInstituteName);
                break;
            case ISSUE:
                generateIssueInvoice(document, documentLayoutSetup, institute, headerFontSize, contentFontSize, transactionSummary,
                        defaultBorderWidth, includeInstituteName);
                break;
            default:
                break;
        }
        if (transactionSummary.getInventoryTransactionStatus() == InventoryTransactionStatus.CANCELLED) {
            PdfPage pdfPage = document.getPdfDocument().getPage(1);
            Rectangle pagesize = pdfPage.getPageSizeWithRotation();

            float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
            float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f - 50f : pagesize.getHeight() * 0.70f - 50f;
            addWaterMark(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y);

        }
        else if (transactionSummary.getTransactionType() == InventoryTransactionType.SALES_RETURN) {
            addWaterMark(document, documentLayoutSetup,
                    ImageProvider.INSTANCE.getImage(ImageProvider.RETURNED_TEXT_IMAGE));
        }
        document.close();
        return invoice;
    }

    public void generateSalesInvoice(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, float headerFontSize,
                                     float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth, boolean includeInstituteName)
            throws IOException {
        generateSalesInvoiceHeader(document, documentLayoutSetup, transactionSummary, institute, headerFontSize, contentFontSize,
                defaultBorderWidth, includeInstituteName);
        addBlankLine(document, false, 1);
        generateItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
                defaultBorderWidth);
        generatePaymentSummary(document, documentLayoutSetup, contentFontSize, transactionSummary,
                defaultBorderWidth);
    }

    public void generateItemsContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                     InventoryTransactionSummary transactionSummary, float defaultBorderWidth) throws IOException {

        switch (transactionSummary.getTransactionType()) {
            case ISSUE:
                generateIssueItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
                        defaultBorderWidth);
                break;
            default:
                generateSalesItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
                        defaultBorderWidth);
                break;
        }

    }

    public void generateSalesItemsContent(Document document, DocumentLayoutSetup documentLayoutSetup,
                                          float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth)
            throws IOException {

        if (CollectionUtils.isEmpty(transactionSummary.getTradeProductSummaryList())) {
            return;
        }

        boolean noDiscountItem = true;
        boolean noTaxItem = true;
        for (TradeProductSummary tradeProductSummary : transactionSummary.getTradeProductSummaryList()) {
            for (TradeProductBatchSummary productBatchSummary : tradeProductSummary.getTradeProductBatchSummaryList()) {
                if (!lteZeroOrNull(productBatchSummary.getTotalDiscount())) {
                    noDiscountItem = false;
                }
                if (!lteZeroOrNull(productBatchSummary.getTotalTax())) {
                    noTaxItem = false;
                }
            }
        }

        CellLayoutSetup itemCellLayoutSetup = new CellLayoutSetup();
        itemCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
                .setBorderRight(new SolidBorder(defaultBorderWidth));


        CellLayoutSetup purchaseItemsHeaderCellLayoutSetup = new CellLayoutSetup();
        purchaseItemsHeaderCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

        CellLayoutSetup purchaseItemsCellLayoutSetup = new CellLayoutSetup();
        purchaseItemsCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

        PurchaseItemHeaderState purchaseItemHeaderState = getPurchaseItemHeaderState(noDiscountItem, noTaxItem);
        float[] headerWidthCellArray = new float[]{};
        List<CellData> headerCellArray = new ArrayList<>();
        switch (purchaseItemHeaderState) {
            case WITH_DISCOUNT_AND_TAX:
                headerWidthCellArray = DEFAULT_PURCHASE_ITEMS_HEADER_WIDTH;
                headerCellArray = Arrays.asList(new CellData("#", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Particulars", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Qty", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Unit Price", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Amt.", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Disc.", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Tax", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Total", purchaseItemsHeaderCellLayoutSetup));
                break;
            case WITHOUT_DISCOUNT_AND_TAX:
                headerWidthCellArray = PURCHASE_ITEMS_WITHOUT_DISCOUNT_TAX_HEADER_WIDTH;
                headerCellArray = Arrays.asList(new CellData("#", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Particulars", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Qty", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Unit Price", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Net Amt", purchaseItemsHeaderCellLayoutSetup));
                break;
            case WITH_TAX_ONLY:
                headerWidthCellArray = PURCHASE_ITEMS_WITH_DISCOUNT_OR_TAX_HEADER_WIDTH;
                headerCellArray = Arrays.asList(new CellData("#", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Particulars", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Qty", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Unit Price", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Total", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Tax", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Net Amt", purchaseItemsHeaderCellLayoutSetup));
                break;
            case WITH_DISCOUNT_ONLY:
                headerWidthCellArray = PURCHASE_ITEMS_WITH_DISCOUNT_OR_TAX_HEADER_WIDTH;
                headerCellArray = Arrays.asList(new CellData("#", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Particulars", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Qty", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Unit Price", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Total", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Disc.", purchaseItemsHeaderCellLayoutSetup),
                        new CellData("Net Amt", purchaseItemsHeaderCellLayoutSetup));
                break;
            default:
                break;
        }

        Table purchaseItemsTable = getPDFTable(documentLayoutSetup, headerWidthCellArray);
        addRow(purchaseItemsTable, documentLayoutSetup, headerCellArray);

        int count = 0;
        Collections.sort( transactionSummary.getTradeProductSummaryList(), new Comparator<TradeProductSummary>() {
            @Override
            public int compare(TradeProductSummary p1, TradeProductSummary p2) {
                return p1.getProductName().compareTo(p2.getProductName());
            }

        });

        for (TradeProductSummary tradeProductSummary : transactionSummary.getTradeProductSummaryList()) {
            for (TradeProductBatchSummary productBatchSummary : tradeProductSummary.getTradeProductBatchSummaryList()) {
                count++;
                String productName = tradeProductSummary.getProductName() + " (" + productBatchSummary.getBatchName() + ")";
                switch (purchaseItemHeaderState) {
                    case WITH_DISCOUNT_AND_TAX:
                        addRow(purchaseItemsTable, documentLayoutSetup,
                                Arrays.asList(new CellData(String.valueOf(count), purchaseItemsCellLayoutSetup),
                                        new CellData(productName, purchaseItemsCellLayoutSetup),
                                        new CellData(String.valueOf((int) productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice() / productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalDiscount()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalTax()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice() + productBatchSummary.getTotalTax() - productBatchSummary.getTotalDiscount()), purchaseItemsCellLayoutSetup)));
                        break;
                    case WITHOUT_DISCOUNT_AND_TAX:
                        addRow(purchaseItemsTable, documentLayoutSetup,
                                Arrays.asList(new CellData(String.valueOf(count), purchaseItemsCellLayoutSetup),
                                        new CellData(productName, purchaseItemsCellLayoutSetup),
                                        new CellData(String.valueOf((int) productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice() / productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice()), purchaseItemsCellLayoutSetup)));
                        break;
                    case WITH_TAX_ONLY:
                        addRow(purchaseItemsTable, documentLayoutSetup,
                                Arrays.asList(new CellData(String.valueOf(count), purchaseItemsCellLayoutSetup),
                                        new CellData(productName, purchaseItemsCellLayoutSetup),
                                        new CellData(String.valueOf((int) productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice() / productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalTax()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice() + productBatchSummary.getTotalTax() - productBatchSummary.getTotalDiscount()), purchaseItemsCellLayoutSetup)));
                        break;
                    case WITH_DISCOUNT_ONLY:
                        addRow(purchaseItemsTable, documentLayoutSetup,
                                Arrays.asList(new CellData(String.valueOf(count), purchaseItemsCellLayoutSetup),
                                        new CellData(productName, purchaseItemsCellLayoutSetup),
                                        new CellData(String.valueOf((int) productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice() / productBatchSummary.getQuantity()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalDiscount()), purchaseItemsCellLayoutSetup),
                                        new CellData(formatDouble(productBatchSummary.getTotalPrice() + productBatchSummary.getTotalTax() - productBatchSummary.getTotalDiscount()), purchaseItemsCellLayoutSetup)));
                        break;
                    default:
                        break;
                }
            }

        }

        document.add(purchaseItemsTable);
    }

    public void generateSalesInvoiceHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
                                           InventoryTransactionSummary transactionSummary, Institute institute, float headerFontSize,
                                           float contentFontSize, float defaultBorderWidth, boolean includeInstituteName) throws IOException {

        float instituteFontSize = headerFontSize + 1;
        int singleContentColumn = 2;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
        PdfFont boldFont = getRegularBoldFont();

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();

        CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
        headerCellLayoutSetup.setFontSize(headerFontSize - 3f);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(GST_NUMBER, headerCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                        new CellData(transactionSummary.getInventoryUserType() == InventoryUserType.STUDENT ? STUDENT_COPY : CUSTOMER_COPY, headerCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))),
                Arrays.asList(new CellData(GST_NUMBER, headerCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                        new CellData(documentLayoutSetup.isOfficeCopy() ? OFFICE_COPY : transactionSummary.getInventoryUserType() == InventoryUserType.STUDENT ? STUDENT_COPY : CUSTOMER_COPY, headerCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));

        document.add(table);
        table = getPDFTable(documentLayoutSetup,1);
        headerCellLayoutSetup.setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("THE STATIONERY EMPORIUM")),
                headerCellLayoutSetup.copy().setFontSize(instituteFontSize).setPdfFont(getRegularBoldFont()));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Vill.: Soyla, Tehsil: Bawadi, Dist: Jodhpur").setMultipliedLeading(.5f)),
                headerCellLayoutSetup);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Contact No. 9057202058")),
                headerCellLayoutSetup);


        if (transactionSummary.getTransactionType() == InventoryTransactionType.ISSUE) {
            addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Issue Receipt").setMultipliedLeading(.5f)),
                    headerCellLayoutSetup.setFontSize(headerFontSize).setPdfFont(getRegularBoldFont()));
        } else {
            addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Sales Bill").setMultipliedLeading(.7f)),
                    headerCellLayoutSetup.setFontSize(headerFontSize).setPdfFont(getRegularBoldFont()));
        }

        document.add(table);


        table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize);

        if(transactionSummary.getInventoryUserType() == InventoryUserType.STUDENT) {
            CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
            CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
            CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
            StudentLite studentLite = transactionSummary.getStudentLite();
            Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), boldFont, boldFont);
            Paragraph fatherName = getKeyValueParagraph("Father Name : ", studentLite.getFathersName(), boldFont, boldFont);
            Paragraph invoice = getKeyValueParagraph("Invoice : ",
                    transactionSummary.getInvoiceId(), boldFont, boldFont);
            Paragraph date = getKeyValueParagraph("Date : ", DateUtils.getFormattedDate((int) (transactionSummary.getTransactionDate() / 1000l)), boldFont, boldFont);
            addBlankLine(document, false, 1);
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData(invoice.setMultipliedLeading(0.7f), firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
                            new CellData(date.setMultipliedLeading(0.7f), thirdCellLayoutSetup)));
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
                            new CellData(fatherName, thirdCellLayoutSetup)));
        } else {
            table = getPDFTable(documentLayoutSetup, 1);
            cellLayoutSetup.setTextAlignment(TextAlignment.RIGHT);
            addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Invoice: ", transactionSummary.getInvoiceId())),
                    cellLayoutSetup.setFontSize(contentFontSize));
            addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Customer Name: ", transactionSummary.getBuyerName())),
                    cellLayoutSetup.setFontSize(contentFontSize));
            addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Date: ", DateUtils.getFormattedDate((int) (transactionSummary.getTransactionDate() / 1000l)))),
                    cellLayoutSetup.setFontSize(contentFontSize));
        }

        document.add(table);

    }

    public void generatePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                       float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth)
            throws IOException {
        InventoryUserType userType = transactionSummary.getInventoryUserType();
        int singleContentColumn = 2;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
        keyCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth));

        CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
                .setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(null);

        CellLayoutSetup keyBoldCellLayoutSetup = keyCellLayoutSetup.copy().setPdfFont(getRegularBoldFont());
        CellLayoutSetup valueBoldCellLayoutSetup = valueCellLayoutSetup.copy().setPdfFont(getRegularBoldFont());

        int totalUnits = 0;
        double totalPrice = 0;
        double totalDiscount = 0;
        double totalTax = 0;
        for (TradeProductSummary tradeProductSummary : transactionSummary.getTradeProductSummaryList()) {
            for (TradeProductBatchSummary productBatchSummary : tradeProductSummary.getTradeProductBatchSummaryList()) {
                totalUnits += productBatchSummary.getQuantity();
                totalPrice += productBatchSummary.getTotalPrice();
                totalDiscount += productBatchSummary.getTotalDiscount();
                totalTax += productBatchSummary.getTotalTax();
            }
        }

        double netAdditionalAmount = subtractValues(transactionSummary.getAdditionalCost(), transactionSummary.getAdditionalDiscount());
        double netAmount = addValues(subtractValues(addValues(totalPrice, totalTax), totalDiscount), netAdditionalAmount);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData("Net Amount:", keyBoldCellLayoutSetup),
                        new CellData(formatToRupees(netAmount),
                                valueBoldCellLayoutSetup)));

        if (transactionSummary.getTransactionType() == InventoryTransactionType.SALES_RETURN) {
            if (userType == InventoryUserType.STUDENT) {
                if (transactionSummary.getWalletCreditAmount() != null && transactionSummary.getWalletCreditAmount() > 0) {
                    addRow(table, documentLayoutSetup,
                            Arrays.asList(new CellData("Refund To Wallet:", keyBoldCellLayoutSetup), new CellData(
                                    formatToRupees(transactionSummary.getWalletCreditAmount()), valueBoldCellLayoutSetup)));
                }
            }
            if (lteZeroOrNull(transactionSummary.getPaidAmount())) {
                addRow(table, documentLayoutSetup,
                        Arrays.asList(new CellData("Refund (" + transactionSummary.getTransactionMode().getDisplayName() + "):", keyBoldCellLayoutSetup),
                                new CellData(formatToRupees(transactionSummary.getPaidAmount()),
                                        valueBoldCellLayoutSetup)));
            }
        } else {
            if (userType == InventoryUserType.STUDENT) {
                if (transactionSummary.getUsedWalletAmount() != null && transactionSummary.getUsedWalletAmount() > 0) {
                    addRow(table, documentLayoutSetup,
                            Arrays.asList(new CellData("Paid From Wallet:", keyBoldCellLayoutSetup), new CellData(
                                    formatToRupees(transactionSummary.getUsedWalletAmount()), valueBoldCellLayoutSetup)));
                }
                if (transactionSummary.getWalletCreditAmount() != null && transactionSummary.getWalletCreditAmount() > 0) {
                    addRow(table, documentLayoutSetup,
                            Arrays.asList(new CellData("Credit Amount:", keyBoldCellLayoutSetup), new CellData(
                                    formatToRupees(transactionSummary.getWalletCreditAmount()), valueBoldCellLayoutSetup)));
                }
            }

            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Paid (" + transactionSummary.getTransactionMode().getDisplayName() + "):", keyBoldCellLayoutSetup),
                            new CellData(formatToRupees(transactionSummary.getPaidAmount()),
                                    valueBoldCellLayoutSetup)));
        }

        document.add(table);

        addRemarkSection(document, documentLayoutSetup, contentFontSize, transactionSummary, defaultBorderWidth);

    }

    private void addRemarkSection(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize, InventoryTransactionSummary transactionSummary, float defaultBorderWidth) throws IOException {
        int singleContentColumn;
        Table table;
        singleContentColumn = 1;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);
        Paragraph remarks = getKeyValueParagraph("Remarks: ",
                transactionSummary.getDescription());

        CellLayoutSetup remarksCellLayoutSetup = new CellLayoutSetup();
        remarksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth));


        if (StringUtils.isNotBlank(transactionSummary.getReference())) {
            CellLayoutSetup refCellLayoutSetup = new CellLayoutSetup();
            refCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                    .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth)).setBorderBottom(null);
            addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Reference: ",
                    transactionSummary.getReference())), refCellLayoutSetup);

            remarksCellLayoutSetup.setBorderTop(null);
        }
        addRow(table, documentLayoutSetup, Arrays.asList(remarks), remarksCellLayoutSetup);
        document.add(table);
    }


    private PurchaseItemHeaderState getPurchaseItemHeaderState(boolean noDiscountItem, boolean noTaxItem) {
        if (noDiscountItem && noTaxItem) {
            return PurchaseItemHeaderState.WITHOUT_DISCOUNT_AND_TAX;
        }
        if (!noDiscountItem && !noTaxItem) {
            return PurchaseItemHeaderState.WITH_DISCOUNT_AND_TAX;
        }
        if (noDiscountItem) {
            return PurchaseItemHeaderState.WITH_TAX_ONLY;
        }
        return PurchaseItemHeaderState.WITH_DISCOUNT_ONLY;
    }

}

