package com.embrate.cloud.pdf.fees;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.layout.LayoutArea;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

public class GlobalFeeChallanGenerator extends FeeChallanGenerator {

	public GlobalFeeChallanGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(GlobalFeeChallanGenerator.class);
	public static final float SQUARE_BORDER_MARGIN = 15f;


	@Override
	public DocumentOutput generateFeeChallan(StudentManager studentManager, Institute institute, Student student, String documentName) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateFeeChallanCardLayoutData(institute, documentOutput);
			Document document = documentLayoutData.getDocument();

			PdfFont regularBoldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();

			generateFeeChallanDetals(document, documentLayoutData, regularBoldFont, regularFont,
					documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, 1);
			document.close();
			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating idenity cards for institute {}, student {}",
					institute.getInstituteId(), student.getStudentId(), e);
		}
		return null;
	}

	private void generateFeeChallanDetals(Document document, DocumentLayoutData documentLayoutData,
										  PdfFont regularBoldFont, PdfFont regularFont, DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student,
										  StudentManager studentManager, int pageNumber) throws IOException {
		/**
		 * BANK COPY
		 */
		generateFeeChallanDetals(document, documentLayoutData, regularBoldFont, regularFont,
				documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, pageNumber, "BANK COPY", 438f, 30);

		LayoutArea currentArea = document.getRenderer().getCurrentArea();
		Rectangle rectangle = currentArea.getBBox();
		/**
		 *  rectangle.getHeight() - gives position of last rectangle from bottom,
		 *  so if position of last rectangle is below than the middle of the page + 20f
		 *  adding next page
		 */
		if(rectangle.getHeight() < (documentLayoutSetup.getPageSize().getHeight() / 2) - 15) {
			document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
			pageNumber++;
		} else {
			while(rectangle.getHeight() > ((documentLayoutSetup.getPageSize().getHeight() / 2) + 15)) {
				addBlankLine(document, false, 1);
				currentArea = document.getRenderer().getCurrentArea();
				rectangle = currentArea.getBBox();
			}
		}

		/**
		 * CMS COPY
		 */
		generateFeeChallanDetals(document, documentLayoutData, regularBoldFont, regularFont,
				documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, pageNumber, "CMS COPY", 15f, 435);

		/**
		 * Document Border
		 */
		generateBorderLayout(documentLayoutData , 1);
		document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));

		/**
		 * STUDENT COPY
		 */
		generateFeeChallanDetals(document, documentLayoutData, regularBoldFont, regularFont,
				documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, pageNumber, "STUDENT COPY", 438f, 30);

		addBlankLine(documentLayoutData.getDocument(), false, 8);

		currentArea = document.getRenderer().getCurrentArea();
		rectangle = currentArea.getBBox();
		/**
		 *  rectangle.getHeight() - gives position of last rectangle from bottom,
		 *  so if position of last rectangle is below than the middle of the page + 20f
		 *  adding next page
		 */
		if(rectangle.getHeight() < (documentLayoutSetup.getPageSize().getHeight() / 2) - 20) {
			document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
			pageNumber++;
		} else {
			while(rectangle.getHeight() > ((documentLayoutSetup.getPageSize().getHeight() / 2) + 20)) {
				addBlankLine(document, false, 1);
				currentArea = document.getRenderer().getCurrentArea();
				rectangle = currentArea.getBBox();
			}
		}

		/**
		 * SCHOOL COPY
		 */
		generateFeeChallanDetals(document, documentLayoutData, regularBoldFont, regularFont,
				documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, pageNumber, "SCHOOL COPY", 15f, 435);

		/**
		 * Document Border
		 */
		generateBorderLayout(documentLayoutData , 2);
	}

	private void generateFeeChallanDetals(Document document, DocumentLayoutData documentLayoutData,
											 PdfFont regularBoldFont, PdfFont regularFont, DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student,
											 StudentManager studentManager, int pageNumber, String copyTitle, float signatureBottom, int logoVerticalOffset) throws IOException {

		/**
		 * Institute Header
		 */
		generateInstituteHeader(document, documentLayoutData, regularBoldFont, regularFont,
				documentLayoutSetup, institute, logoVerticalOffset);

		/**
		 * Student Details
		 */
		generateStudentInformation(documentLayoutData, student, regularBoldFont, regularFont);

		/**
		 * Fees Section
		 */
		generateFeesInformation(documentLayoutData, regularBoldFont, regularFont, copyTitle);

		/**
		 * Signature Box
		 */
		generateSignatureBox(documentLayoutData.getDocument(), documentLayoutData.getDocumentLayoutSetup(),
				documentLayoutData.getContentFontSize(), documentLayoutData.getDefaultBorderWidth(), pageNumber, signatureBottom);

	}

	protected void generateInstituteHeader(Document document,
										   DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont regularFont,
										   DocumentLayoutSetup documentLayoutSetup, Institute institute, int logoVerticalOffset) throws IOException {
		addBlankLine(documentLayoutData.getDocument(), false, 2);
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
		centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
				.setPaddingLeft(20f);

		addRow(table, documentLayoutSetup, Arrays.asList(
						getParagraph(institute.getInstituteName().toUpperCase())),
				centerCellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize() +8f).setPaddingTop(0f).setPaddingBottom(0f));

		document.add(table);
		addBlankLine(documentLayoutData.getDocument(), false, 3);

		generateDynamicImageProvider(documentLayoutData, 0, logoVerticalOffset , 1.25f, 1.25f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

	}

	protected void generateStudentInformation(DocumentLayoutData documentLayoutData, Student student, PdfFont boldFont, PdfFont regularFont) throws IOException {

		addBlankLine(documentLayoutData.getDocument(), false, 1);
		Document document = documentLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
		float contentFontSize = documentLayoutData.getContentFontSize() - 1;

		Table table = getPDFTable(documentLayoutSetup, new float[]{0.15f, 0.25f, 0.12f, 0.18f, 0.13f, 0.17f});

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderBottom(new SolidBorder(0.5f));
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup forthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderBottom(new SolidBorder(0.5f));
		CellLayoutSetup fifthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup sixthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderBottom(new SolidBorder(0.5f));

		Paragraph studentNameKey = getParagraph("STUDENT'S NAME ", boldFont);
		Paragraph studentNameValue = getParagraph(": " + student.getStudentBasicInfo().getName().toUpperCase(), regularFont);
		Paragraph fatherNameKey = getParagraph("FATHER'S NAME ", boldFont);
		Paragraph fatherNameValue = getParagraph(StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName()) ? ": " : ": " + student.getStudentFamilyInfo().getFathersName().toUpperCase(), regularFont);
		Paragraph admissionNumberKey = getParagraph("ADMISSION NO. ", boldFont);
		Paragraph admissionNumberValue = getParagraph(": " + student.getStudentBasicInfo().getAdmissionNumber().toUpperCase(), regularFont);
		Paragraph classKey = getParagraph("CLASS ", boldFont);
		Paragraph classValue = getParagraph(": " + student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection().toUpperCase(), regularFont);

		Paragraph depositDateKey = getParagraph("DEPOSIT DATE ", boldFont);
		Paragraph emptyValue = getParagraph(": ", regularFont);
		Paragraph txnIdKey = getParagraph("TXN ID ", boldFont);
		Paragraph contactNumberKey = getParagraph("CONTACT NO. ", boldFont);
		Paragraph contactNumberValue = getParagraph(student.getStudentBasicInfo().getPrimaryContactNumber() == null ? ": " : ": " + student.getStudentBasicInfo().getPrimaryContactNumber(), regularFont);
		Paragraph monthKey = getParagraph("MONTH ", boldFont);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentNameKey, firstCellLayoutSetup), new CellData(studentNameValue, secondCellLayoutSetup), new CellData(admissionNumberKey, thirdCellLayoutSetup), new CellData(admissionNumberValue, forthCellLayoutSetup), new CellData(txnIdKey, fifthCellLayoutSetup), new CellData(emptyValue, sixthCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(fatherNameKey, firstCellLayoutSetup), new CellData(fatherNameValue, secondCellLayoutSetup), new CellData(classKey, thirdCellLayoutSetup), new CellData(classValue, forthCellLayoutSetup), new CellData(depositDateKey, fifthCellLayoutSetup), new CellData(emptyValue, sixthCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(contactNumberKey, firstCellLayoutSetup), new CellData(contactNumberValue, secondCellLayoutSetup), new CellData(monthKey, thirdCellLayoutSetup), new CellData(emptyValue, forthCellLayoutSetup), new CellData(EMPTY_TEXT, fifthCellLayoutSetup), new CellData(EMPTY_TEXT, sixthCellLayoutSetup.copy().setBorder(null))));

		document.add(table);

		addBlankLine(documentLayoutData.getDocument(), false, 1);
	}

	protected void generateFeesInformation(DocumentLayoutData documentLayoutData, PdfFont boldFont, PdfFont regularFont, String copyTitle) throws IOException {

		Document document = documentLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
		float contentFontSize = documentLayoutData.getContentFontSize() - 1;
		Table table = getPDFTable(documentLayoutSetup,1);

		CellLayoutSetup titleLayoutSetup = new CellLayoutSetup();
		titleLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize + 2f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(copyTitle, titleLayoutSetup)));

		document.add(table);
		table = getPDFTable(documentLayoutSetup, new float[]{0.17f, 0.23f, 0.20f, 0.10f, 0.10f, 0.10f, 10f});

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize).setBorder(new SolidBorder(0.5f));

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorder(null);
		CellLayoutSetup forthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT).setBorderRight(null);
		CellLayoutSetup fifthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup sixthCellLayoutSetup = cellLayoutSetup.copy().setBorderLeft(null);
		CellLayoutSetup seventhCellLayoutSetup = cellLayoutSetup.copy().setBorder(null);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("CHEQUE NO. :", firstCellLayoutSetup.setBorder(null)), new CellData(EMPTY_TEXT, secondCellLayoutSetup.setBorder(null).setBorderBottom(new SolidBorder(0.5f))), new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData("500", forthCellLayoutSetup), new CellData(" x", fifthCellLayoutSetup), new CellData(EMPTY_TEXT, sixthCellLayoutSetup), new CellData(EMPTY_TEXT, seventhCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("CHQ. ISSUING BANK :", firstCellLayoutSetup.setBorder(null)), new CellData(EMPTY_TEXT, secondCellLayoutSetup.setBorder(null).setBorderBottom(new SolidBorder(0.5f))), new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData("200", forthCellLayoutSetup), new CellData(" x", fifthCellLayoutSetup), new CellData(EMPTY_TEXT, sixthCellLayoutSetup), new CellData(EMPTY_TEXT, seventhCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, firstCellLayoutSetup.setBorder(null)), new CellData(EMPTY_TEXT, secondCellLayoutSetup.setBorder(null)), new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData("100", forthCellLayoutSetup), new CellData(" x", fifthCellLayoutSetup), new CellData(EMPTY_TEXT, sixthCellLayoutSetup), new CellData(EMPTY_TEXT, seventhCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, firstCellLayoutSetup.setBorder(null)), new CellData(EMPTY_TEXT, secondCellLayoutSetup.setBorder(null)), new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData("50", forthCellLayoutSetup), new CellData(" x", fifthCellLayoutSetup), new CellData(EMPTY_TEXT, sixthCellLayoutSetup), new CellData(EMPTY_TEXT, seventhCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, firstCellLayoutSetup.setBorder(null)), new CellData(EMPTY_TEXT, secondCellLayoutSetup.setBorder(null)), new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData("20", forthCellLayoutSetup), new CellData(" x", fifthCellLayoutSetup), new CellData(EMPTY_TEXT, sixthCellLayoutSetup), new CellData(EMPTY_TEXT, seventhCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("ADMISSION :", firstCellLayoutSetup.setBorder(new SolidBorder(0.5f))), new CellData(EMPTY_TEXT, secondCellLayoutSetup.setBorder(new SolidBorder(0.5f))), new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData("10", forthCellLayoutSetup), new CellData(" x", fifthCellLayoutSetup), new CellData(EMPTY_TEXT, sixthCellLayoutSetup), new CellData(EMPTY_TEXT, seventhCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("ANNUAL FEES :", firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup), new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData("5", forthCellLayoutSetup), new CellData(" x", fifthCellLayoutSetup), new CellData(EMPTY_TEXT, sixthCellLayoutSetup), new CellData(EMPTY_TEXT, seventhCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("BUS FEES :", firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup), new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData("TOTAL", forthCellLayoutSetup), new CellData("COIN'S", fifthCellLayoutSetup.setBorderLeft(null)), new CellData(EMPTY_TEXT, sixthCellLayoutSetup), new CellData(EMPTY_TEXT, seventhCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("HOSTEL FEES :", firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup), new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData("RETURN", forthCellLayoutSetup), new CellData("AMOUNT", fifthCellLayoutSetup.setBorderLeft(null)), new CellData(EMPTY_TEXT, sixthCellLayoutSetup), new CellData(EMPTY_TEXT, seventhCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("TOTAL :", firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup), new CellData(EMPTY_TEXT, thirdCellLayoutSetup), new CellData("GRAND", forthCellLayoutSetup), new CellData("TOTAL", fifthCellLayoutSetup.setBorderLeft(null)), new CellData(EMPTY_TEXT, sixthCellLayoutSetup), new CellData(EMPTY_TEXT, seventhCellLayoutSetup)));

		document.add(table);
		addBlankLine(documentLayoutData.getDocument(), false, 3);

		table = getPDFTable(documentLayoutSetup, new float[]{0.18f, 0.82f});
		CellLayoutSetup amountLayoutSetup = new CellLayoutSetup();
		amountLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("AMOUNT (IN WORDS):", thirdCellLayoutSetup), new CellData(EMPTY_TEXT, amountLayoutSetup.setBorderBottom(new SolidBorder(0.5f)))));

		document.add(table);

		addBlankLine(documentLayoutData.getDocument(), false, 1);
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, float defaultBorderWidth, int pageNumber, float signatureBottom) throws IOException {

		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("DEPOSITOR'S SIGNATURE"), getParagraph(""),
				getParagraph("SIGNATURE")), signatureCellLayoutSetup);
		table.setFixedPosition(30f, signatureBottom, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
	}

	protected void generateBorderLayout(DocumentLayoutData documentLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(documentLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN, documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2 , documentLayoutSetup.getPageSize().getHeight() / 2 - SQUARE_BORDER_MARGIN );
		canvas.stroke();
		canvas.setLineWidth(1f);

		canvas = new PdfCanvas(documentLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.moveTo( SQUARE_BORDER_MARGIN, documentLayoutSetup.getPageSize().getHeight() / 2  + SQUARE_BORDER_MARGIN );
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN, documentLayoutSetup.getPageSize().getHeight() / 2  + SQUARE_BORDER_MARGIN);
		canvas.lineTo( documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN ,  documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN );
		canvas.lineTo( SQUARE_BORDER_MARGIN ,  documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN );
		canvas.setLineWidth(1f);
		canvas.closePathStroke();
	}
}
