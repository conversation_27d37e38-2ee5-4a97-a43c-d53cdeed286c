package com.embrate.cloud.pdf.hpc;

import com.embrate.cloud.core.api.hpc.utils.HPCExamType;
import com.embrate.cloud.core.api.hpc.layout.HPCForm;
import com.embrate.cloud.core.api.hpc.generator.HPCDocument;
import com.embrate.cloud.core.api.hpc.utils.HPCFormWithStudent;
import com.embrate.cloud.core.lib.hpc.HPCFormManager;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.properties.AreaBreakType;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 */
public class GlobalHPCGenerator extends BaseHPCGenerator {
	private static final Logger logger = LogManager.getLogger(GlobalHPCGenerator.class);

	public GlobalHPCGenerator(AssetProvider assetProvider) {
		super(assetProvider);
	}

	@Override
	public DocumentOutput generateIndividual(Institute institute, Student student, HPCExamType examType, HPCForm hpcForm,
											 UUID principalStaffId, UUID classTeacherStaffId,
											 HPCFormManager hpcFormManager, StudentManager studentManager,
											 StaffManager staffManager) {
		try {
			HPCDocument hpcDocument = initHPCDocument(student.getStudentBasicInfo().getName());
			Set<HPCExamType> requiredExamTypes = new HashSet<>(examType.getApplicableExamTypes());
			generateStudentHPCReportCard(student, hpcForm,hpcDocument, requiredExamTypes, principalStaffId, classTeacherStaffId, hpcFormManager, studentManager, staffManager);
			hpcDocument.getDocument().close();
			return hpcDocument.getOutput();
		} catch (Exception e) {
			logger.error("Exception in generating hpc document", e);
		}
		return null;

	}

	private void generateStudentHPCReportCard(Student student, HPCForm hpcForm, HPCDocument hpcDocument, Set<HPCExamType> requiredExamTypes, UUID principalStaffId, UUID classTeacherStaffId, HPCFormManager hpcFormManager,
											  StudentManager studentManager, StaffManager staffManager) throws IOException {
		generateHPCSections(student, hpcForm, hpcDocument, requiredExamTypes, principalStaffId, classTeacherStaffId, hpcFormManager, studentManager, staffManager);
	}

	@Override
	public DocumentOutput generateBulk(Institute institute, HPCExamType examType, List<HPCFormWithStudent> studentHPCFormList,
									   UUID principalStaffId, UUID classTeacherStaffId,
									   HPCFormManager hpcFormManager, StudentManager studentManager, StaffManager staffManager) {
		try {
			HPCDocument hpcDocument = initHPCDocument(examType.getDisplayName());
			Set<HPCExamType> requiredExamTypes = new HashSet<>(examType.getApplicableExamTypes());

			int pageNumber = 1;
			for (HPCFormWithStudent hpcFormWithStudent : studentHPCFormList) {

				generateStudentHPCReportCard(hpcFormWithStudent.getStudent(), hpcFormWithStudent.getForm(),
						hpcDocument, requiredExamTypes, principalStaffId, classTeacherStaffId, hpcFormManager, studentManager, staffManager);

				/**
				 * here assuming that each student report card will have 9 pages
				 */
				if (pageNumber == studentHPCFormList.size() * 9) {
					hpcDocument.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			hpcDocument.getDocument().close();
			return hpcDocument.getOutput();

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}
}
