package com.lernen.cloud.pdf.identitycard.staff;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.staff.FullStaffDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.StringConstants;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class GlobalStaffIdentityCardGenerator extends StaffIdentityCardGenerator {

	public GlobalStaffIdentityCardGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(GlobalStaffIdentityCardGenerator.class);

	@Override
	public DocumentOutput generateIdentityCard(StaffManager staffManager, Institute institute,
											   FullStaffDetails staff, String documentName, DocumentPropertiesPreferences documentPropertiesPreferences ) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
					documentLayoutData.getDocumentLayoutSetup(), institute, staff, staffManager, 1);
			document.close();
			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating idenity cards for institute {}, staff {}",
					institute.getInstituteId(), staff.getStaffId(), e);
		}
		return null;
	}

	private void generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData,
											 PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute,
											 FullStaffDetails staff, StaffManager staffManager, int pageNumber) throws IOException {

		/**
		 * Staff Details Front
		 */
		generateStaffFrontPageDetails(document, documentLayoutData, institute, pageNumber, staffManager,
				cambriaFont, cambriaBoldFont, documentLayoutSetup, staff);

		/**
		 * Add new page
		 */
		document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
		pageNumber++;

		/**
		 * Staff Details Back
		 */
		generateStaffBackDetails(document, documentLayoutData, pageNumber,
				cambriaFont, cambriaBoldFont, documentLayoutSetup, institute, staffManager, staff);

	}

	private void generateStaffBackDetails(Document document, DocumentLayoutData documentLayoutData,
										  int pageNumber, PdfFont cambriaFont, PdfFont cambriaBoldFont,
										  DocumentLayoutSetup documentLayoutSetup, Institute institute,
										  StaffManager staffManager, FullStaffDetails staff) throws IOException {

		/**
		 * Bottom bar, keep this on top as we are
		 * using canvas in generateInstituteHeader which
		 * require document to be created before it,
		 * ow error occurred
		 */
		generateBottomBar(document, cambriaBoldFont, documentLayoutSetup);

		/**
		 * Institute Header
		 */
		generateBackPageHeader(document, documentLayoutData, documentLayoutSetup, pageNumber, institute);

		/**
		 * Staff advance details
		 */
		generateStaffAdvanceDetails(document, cambriaBoldFont, cambriaFont, documentLayoutSetup, staff);

		/**
		 * signature section
		 */
		generateSignatureSection(document, institute, staff, staffManager, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, pageNumber);

		/**
		 * static footer section
		 */
		generateStaticFooterSection(document, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute);
	}

	private void generateSignatureSection(Document document, Institute institute, FullStaffDetails staff, StaffManager staffManager,
										  PdfFont cambriaBoldFont, PdfFont cambriaFont,
										  DocumentLayoutSetup documentLayoutSetup, int pageNumber) throws IOException {

//		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.05f, 0.45f, 0.45f, 0.05f});
//		table.setFixedPosition(0, 56f, documentLayoutSetup.getPageSize().getWidth());
//
//		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
//		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(6f).setTextAlignment(TextAlignment.CENTER)
//				.setPaddingLeft(12f).setPaddingTop(0f).setPaddingBottom(0f).setPaddingRight(12f);
//
//		table.addCell(new Cell().setBorder(null));
//
//		byte[] staffSignature = getStaffDocument(institute.getInstituteId(), staff, staffManager, StaffDocumentType.STAFF_SIGNATURE);
//		if(staffSignature != null) {
//			Image staffSignatureImage = new Image(ImageDataFactory.create(staffSignature));
//			staffSignatureImage.setAutoScaleHeight(true);
//			staffSignatureImage.setAutoScaleWidth(true);
//
//			Cell employeeSignatureCell = new Cell();
//			employeeSignatureCell.add(staffSignatureImage);
//			employeeSignatureCell.setBorder(null);
//			table.addCell(employeeSignatureCell);
//		} else {
//			table.addCell(new Cell().setBorder(null));
//		}
//
//		/**
//		 * Uncomment it when we have manager signature
//		 */
////		Image managerSignatureImage = new Image(ImageDataFactory.create(ImageProvider.INSTANCE.getImage(ImageProvider._702_SCHOOL_DIRECTOR_SIGN)));
////		managerSignatureImage.setAutoScaleHeight(true);
////		managerSignatureImage.setAutoScaleWidth(true);
////
////		Cell managerSignatureCell = new Cell();
////		managerSignatureCell.add(managerSignatureImage);
////		managerSignatureCell.setBorder(null);
////		table.addCell(managerSignatureCell);
//
//		/**
//		 * comment it when we have manager signature
//		 */
//		table.addCell(new Cell().setBorder(null));
//
//
//		table.addCell(new Cell().setBorder(null));
//
//		document.add(table);
//
//		table = getPDFTable(documentLayoutSetup, new float[] {0.45f, 0.55f});
//		table.setFixedPosition(0, 52f, documentLayoutSetup.getPageSize().getWidth());
//		List<Integer> rgb = ColorUtils.hex2Rgb("#351C75");
//		Paragraph employeeSignatureParagraph = getParagraph("Employee", rgb, cambriaBoldFont);
//		Paragraph schoolSignatureParagraph = getParagraph("Managing Director", rgb, cambriaBoldFont);
//		addRow(table, documentLayoutSetup, Arrays.asList(
//				new CellData(employeeSignatureParagraph, cellLayoutSetup.copy()),
//				new CellData(schoolSignatureParagraph, cellLayoutSetup.copy())));
//
//		document.add(table);

		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.moveTo(10d, 50d);
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() - 10d, 50d);

		Color color = WebColors.getRGBColor("#ffe599");
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);
		canvas.setLineWidth(.5f);
		canvas.closePathStroke();

	}

	private void generateStaticFooterSection(Document document, PdfFont cambriaBoldFont, PdfFont cambriaFont,
											 DocumentLayoutSetup documentLayoutSetup, Institute institute) {

		Table table = getPDFTable(documentLayoutSetup, 1);
		table.setFixedPosition(0, 8f, documentLayoutSetup.getPageSize().getWidth());
		List<Integer> rgb = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(6f).setTextAlignment(TextAlignment.LEFT)
				.setPaddingLeft(12f).setPaddingTop(0f).setPaddingBottom(0f).setPaddingRight(12f);

		Paragraph locationKeyValueParagraph = getKeyValueParagraph("Location : ",
				institute.getLetterHeadLine1(),
				rgb, rgb, cambriaBoldFont, cambriaFont).setTextAlignment(TextAlignment.JUSTIFIED);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(locationKeyValueParagraph, cellLayoutSetup.copy())));

		Paragraph staticParagraph = getParagraph("If found, please return to the above mentioned address.",
				rgb, cambriaBoldFont).setTextAlignment(TextAlignment.JUSTIFIED).setPaddingTop(2f);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(staticParagraph, cellLayoutSetup.copy())));
		document.add(table);

	}

	private void generateStaffAdvanceDetails(Document document, PdfFont cambriaBoldFont, PdfFont cambriaFont,
											 DocumentLayoutSetup documentLayoutSetup, FullStaffDetails staff) {

		float logoHeight = 35f;

		float pageHeight = documentLayoutSetup.getPageSize().getHeight();
		float height2 = pageHeight - (pageHeight * (1/6f)) - (logoHeight / 2) - 65;

		Table table = getPDFTable(documentLayoutSetup, 1);
		table.setFixedPosition(0, height2, documentLayoutSetup.getPageSize().getWidth());

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(7f).setTextAlignment(TextAlignment.LEFT)
				.setPaddingLeft(12f).setPaddingTop(0f).setPaddingBottom(0f).setPaddingRight(12f);

		Table table2 = getPDFTable(documentLayoutSetup, 1);

		List<Integer> rgb = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);

		Paragraph dobKeyParagraph = getKeyValueParagraph("DOB : ",
				staff.getStaffBasicDetailsWithCategoryDepartDesignation().getDateOfBirth() == null ? ""
						:  DateUtils.getFormattedDate(staff.getStaffBasicDetailsWithCategoryDepartDesignation()
						.getDateOfBirth(), DATE_FORMAT_DOT, User.DFAULT_TIMEZONE), rgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(dobKeyParagraph, cellLayoutSetup.copy())));


		Paragraph joiningDateKeyParagraph = getKeyValueParagraph("Joining Date : ",
				staff.getStaffJoiningInfo() == null ? "" : staff.getStaffJoiningInfo().getTentativeDateOfJoining() == null ?
						"" : DateUtils.getFormattedDate(staff.getStaffJoiningInfo().getTentativeDateOfJoining(), DATE_FORMAT_DOT, User.DFAULT_TIMEZONE),
				rgb, rgb, cambriaBoldFont, cambriaFont);
		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(joiningDateKeyParagraph, cellLayoutSetup.copy())));

		Paragraph addressKeyParagraph = getKeyValueParagraph("Address : ",
				getStaffAddress(staff), rgb, rgb, cambriaBoldFont, cambriaFont).setTextAlignment(TextAlignment.JUSTIFIED);
		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(addressKeyParagraph, cellLayoutSetup.copy())));

		Cell cell2 = new Cell();
		cell2.add(table2);
		cell2.setBorder(null);
		table.addCell(cell2);

		document.add(table);
	}


	private void generateBackPageHeader(Document document, DocumentLayoutData documentLayoutData,
										DocumentLayoutSetup documentLayoutSetup,
										int pageNumber, Institute institute) throws IOException {


		generateBackPageHeaderCanvas(document, documentLayoutSetup, pageNumber);

		generateBackPageHeaderImage(document, documentLayoutSetup, documentLayoutData, institute);
	}

	private void generateBackPageHeaderImage(Document document, DocumentLayoutSetup documentLayoutSetup,

											 DocumentLayoutData documentLayoutData, Institute institute) throws IOException {

		float logoWidth = 35f;
		float logoHeight = 35f;

		float pageHeight = documentLayoutSetup.getPageSize().getHeight();
		float height2 = pageHeight - (pageHeight * (5/6f));

		int horizontalOffSet = -(int)((documentLayoutSetup.getPageSize().getWidth() / 2) - (logoWidth / 2));
		int verticalOffSet = (int)(height2 - (logoHeight / 2));

		generateDynamicImageProvider(documentLayoutData, horizontalOffSet, verticalOffSet, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

	}

	private void generateBackPageHeaderCanvas(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {

		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		Color color = WebColors.getRGBColor("#ffe599");
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);

		float pageHeight = documentLayoutSetup.getPageSize().getHeight();
		float pageWidth = documentLayoutSetup.getPageSize().getWidth();
		float height2 = pageHeight - (pageHeight * (1/6f));

		canvas.moveTo(0, pageHeight);
		canvas.lineTo(pageWidth, pageHeight);
		canvas.lineTo(pageWidth, height2);
		canvas.lineTo(0, height2);
		canvas.lineTo(0, pageHeight);

		canvas.setLineWidth(5f);
		canvas.fill();
		canvas.closePathStroke();
	}

	private void generateStaffFrontPageDetails(Document document, DocumentLayoutData documentLayoutData,
											   Institute institute, int pageNumber, StaffManager staffManager,
											   PdfFont cambriaFont, PdfFont cambriaBoldFont,
											   DocumentLayoutSetup documentLayoutSetup, FullStaffDetails staff) throws IOException {

		/**
		 * Bottom bar, keep this on top as we are
		 * using canvas in generateInstituteHeader which
		 * require document to be created before it,
		 * ow error occurred
		 */
		generateBottomBar(document, cambriaBoldFont, documentLayoutSetup);

		/**
		 * Institute Header
		 */
		generateFrontPageHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute, pageNumber, staff, staffManager);

		/**
		 * Staff basic details
		 */
		generateStaffBasicDetails(document, cambriaBoldFont, cambriaFont, documentLayoutSetup, staff);
	}

	private void generateStaffBasicDetails(Document document,
										   PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
										   FullStaffDetails staff) {

//		Table table = getPDFTable(documentLayoutSetup, 1);
//		table.setFixedPosition(0, documentLayoutSetup.getPageSize().getHeight()
//				- ((documentLayoutSetup.getPageSize().getHeight() / 2) + 95), documentLayoutSetup.getPageSize().getWidth());

		String emailId = staff.getStaffBasicDetailsWithCategoryDepartDesignation().getPrimaryEmail();
		float subToTop = 0;
		if(isTextGreater(emailId, 29)) {
			subToTop += 11;
		}
		float bottom = 30 - subToTop;

		Table table = getPDFTable(documentLayoutSetup, 1).setPaddingRight(10f);
		table.setFixedPosition(0, bottom, documentLayoutSetup.getPageSize().getWidth());

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(7f)
				.setTextAlignment(TextAlignment.CENTER).setPaddingLeft(10f).setPaddingTop(0f).setPaddingBottom(0f);

		Paragraph staffNameParagraph = getParagraph(staff
				.getStaffBasicDetailsWithCategoryDepartDesignation().getName(), 33,33,33);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(staffNameParagraph,
				cellLayoutSetup.copy().setPaddingTop(2f).setFontSize(8f).setPdfFont(cambriaBoldFont))));

		String departmentDesignation = "";
		boolean first = true;
		List<String> departmentDesignationNameList = staff.getStaffBasicDetailsWithCategoryDepartDesignation().getDepartmentDesignationNameList();
		if(!CollectionUtils.isEmpty(departmentDesignationNameList)) {
			for(String name : staff.getStaffBasicDetailsWithCategoryDepartDesignation().getDepartmentDesignationNameList()) {
				if(StringUtils.isBlank(name)) {
					continue;
				}
				if(first) {
					departmentDesignation += name;
					first = false;
				} else {
					departmentDesignation += ", " + name;
				}
			}
		}

		Paragraph departmentDesignationParagraph = getParagraph(departmentDesignation, 33,33,33);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(departmentDesignationParagraph,
				cellLayoutSetup.copy().setFontSize(7f))));


		Table table2 = getPDFTable(documentLayoutSetup, new float[] {0.3f, 0.7f});

		cellLayoutSetup.setPaddingLeft(0f);

		Paragraph idKeyParagraph = getParagraph("Id # :", 33,33,33);
		Paragraph idValueParagraph = getParagraph(staff
				.getStaffBasicDetailsWithCategoryDepartDesignation().getStaffInstituteId(), 33,33,33);
		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(idKeyParagraph,
						cellLayoutSetup.copy().setFontSize(7f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.RIGHT).setPaddingTop(5f)),
				new CellData(idValueParagraph, cellLayoutSetup.copy().setFontSize(7f).setTextAlignment(TextAlignment.LEFT).setPaddingTop(5f))));

		Paragraph emailKeyParagraph = getParagraph("Email :", 33,33,33);
		Paragraph emailValueParagraph = getParagraph(staff.getStaffBasicDetailsWithCategoryDepartDesignation().getPrimaryEmail(),
				33,33,33);
		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(emailKeyParagraph,
						cellLayoutSetup.copy().setFontSize(7f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.RIGHT)),
				new CellData(emailValueParagraph, cellLayoutSetup.copy().setFontSize(7f).setTextAlignment(TextAlignment.LEFT))));

		Paragraph mobileKeyParagraph = getParagraph("Mobile :", 33,33,33);
		Paragraph mobileValueParagraph = getParagraph(staff.getStaffBasicDetailsWithCategoryDepartDesignation().getPrimaryContactNumber(),
				33,33,33);
		addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(mobileKeyParagraph,
						cellLayoutSetup.copy().setFontSize(7f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.RIGHT)),
				new CellData(mobileValueParagraph, cellLayoutSetup.copy().setFontSize(7f).setTextAlignment(TextAlignment.LEFT))));

		Cell cell1 = new Cell();
		cell1.setBorder(null);
		table.addCell(cell1);

		Cell cell2 = new Cell();
		cell2.add(table2);
		cell2.setBorder(null);
		table.addCell(cell2);

		document.add(table);
	}

	protected String getStaffAddress(FullStaffDetails staff) {
		String address1 = "";
		String address2 = "";
		String city = "";
		String state = "";
		String zipcode = "";

		if(StringUtils.isBlank(staff.getStaffAddressContactInfo().getPresentAddress1())) {
			if (StringUtils.isNotBlank(staff.getStaffAddressContactInfo().getPermanentAddress1())) {
				address1 = staff.getStaffAddressContactInfo().getPermanentAddress1().trim();
			}
			if (StringUtils.isNotBlank(staff.getStaffAddressContactInfo().getPermanentAddress2())) {
				address2 = staff.getStaffAddressContactInfo().getPermanentAddress2().trim();
			}
			if (StringUtils.isNotBlank(staff.getStaffAddressContactInfo().getPermanentCity())) {
				city = staff.getStaffAddressContactInfo().getPermanentCity().trim();
			}
			if (StringUtils.isNotBlank(staff.getStaffAddressContactInfo().getPermanentState())) {
				state = staff.getStaffAddressContactInfo().getPermanentState().trim();
			}
			if (StringUtils.isNotBlank(staff.getStaffAddressContactInfo().getPermanentZipcode())) {
				zipcode = staff.getStaffAddressContactInfo().getPermanentZipcode().trim();
			}
		} else {
			if (StringUtils.isNotBlank(staff.getStaffAddressContactInfo().getPresentAddress1())) {
				address1 = staff.getStaffAddressContactInfo().getPresentAddress1().trim();
			}
			if (StringUtils.isNotBlank(staff.getStaffAddressContactInfo().getPresentAddress2())) {
				address2 = staff.getStaffAddressContactInfo().getPresentAddress2().trim();
			}
			if (StringUtils.isNotBlank(staff.getStaffAddressContactInfo().getPresentCity())) {
				city = staff.getStaffAddressContactInfo().getPresentCity().trim();
			}
			if (StringUtils.isNotBlank(staff.getStaffAddressContactInfo().getPresentState())) {
				state = staff.getStaffAddressContactInfo().getPresentState().trim();
			}
			if (StringUtils.isNotBlank(staff.getStaffAddressContactInfo().getPresentZipcode())) {
				zipcode = staff.getStaffAddressContactInfo().getPresentZipcode().trim();
			}
		}

		return getStaffAddress(address1, address2, city, state, zipcode);
	}

	private String getStaffAddress(String addressDB1, String addressDB2, String city, String state, String zipcode) {
		StringBuilder address = new StringBuilder();
		boolean textAdded = false;
		if (StringUtils.isNotBlank(addressDB1)) {
			address.append(addressDB1);
			textAdded = true;
		}
		if (StringUtils.isNotBlank(addressDB2)) {
			if (textAdded) {
				address.append(StringConstants.COMMA).append(" ");
			}
			address.append(addressDB2);
			textAdded = true;
		}
		if (StringUtils.isNotBlank(city)) {
			if (textAdded) {
				address.append(StringConstants.COMMA).append(" ");
			}
			address.append(city);
			textAdded = true;
		}
		if (StringUtils.isNotBlank(state)) {
			if (textAdded) {
				address.append(StringConstants.COMMA).append(" ");
			}
			address.append(state);
			textAdded = true;
		}
		if (StringUtils.isNotBlank(zipcode)) {
			if (textAdded) {
				address.append(StringConstants.COMMA).append(" ");
			}
			address.append(zipcode);
			textAdded = true;
		}
		return address.toString();
	}

	private void generateBottomBar(Document document, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup) {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup bottomBarCellLayoutSetup = new CellLayoutSetup();
		bottomBarCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
				.setPaddingBottom(2f).setPaddingTop(2f)
				.setBackgroundColor("#ffe599");

		table.setFixedPosition(0f, 0f, documentLayoutSetup.getPageSize().getWidth());
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(EMPTY_TEXT, bottomBarCellLayoutSetup.copy())));

		document.add(table);
	}

	private void generateFrontPageHeader(Document document,
										 DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
										 DocumentLayoutSetup documentLayoutSetup, Institute institute, int pageNumber,
										 FullStaffDetails staff, StaffManager staffManager) throws IOException {

		generateBackgroundCanvas(document, documentLayoutSetup, pageNumber);

		generateStaffImage(document, documentLayoutSetup, institute, staffManager, staff);

		generateInstituteDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute);
	}

	private void generateInstituteDetails(Document document,
										  DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
										  DocumentLayoutSetup documentLayoutSetup, Institute institute) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
		centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT)
				.setPaddingLeft(50f).setBackgroundColor("#ffe599");

		String[] instituteNameArr = institute.getInstituteName().split(" ");
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
						getParagraph(instituteNameArr[0].toUpperCase(),53, 28, 117)),
				centerCellLayoutSetup.copy().setFontSize(10f).setPaddingTop(1f).setPaddingBottom(0f));

		int length = instituteNameArr.length;
		String[] instituteNameRestArr = Arrays.copyOfRange(instituteNameArr, 1, length);
		String instituteNameRest = String.join(" ", instituteNameRestArr);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
						getParagraph(instituteNameRest.toUpperCase(), 53, 28, 117)),
				centerCellLayoutSetup.copy().setFontSize(10f).setPaddingTop(0f).setPaddingBottom(0f));

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""),
						getParagraph(institute.getAddressLine1(), 33, 33, 33)),
				centerCellLayoutSetup.copy().setPdfFont(cambriaFont).setFontSize(7f).setPaddingTop(0f).setPaddingBottom(0f));

		document.add(table);

		generateDynamicImageProvider(documentLayoutData, -8, 4, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
	}

	private void generateBackgroundCanvas(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {

		generateBackgroundTriangle(document, documentLayoutSetup, pageNumber);

		generateImageBackground(document, documentLayoutSetup, pageNumber);

	}

	private void generateImageBackground(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {

		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		Color color = WebColors.getRGBColor("#ffe599");
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);

		canvas.moveTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.75)), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.25)), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.25)), documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) + 20));
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.75)), documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) + 20));
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.75)), documentLayoutSetup.getPageSize().getHeight());

		canvas.setLineWidth(5f);
		canvas.fill();
		canvas.closePathStroke();
	}

	private void generateBackgroundTriangle(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);

		Color color = WebColors.getRGBColor("#ffe599");
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);

		canvas.moveTo(0, documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight()
				- (documentLayoutSetup.getPageSize().getHeight() / 4));
		canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) - 10));
		canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight());
		canvas.setLineWidth(5f);
		canvas.fill();
		canvas.closePathStroke();
	}

	private void generateStaffImage(Document document, DocumentLayoutSetup documentLayoutSetup,
									Institute institute, StaffManager staffManager, FullStaffDetails staff) throws MalformedURLException {

		byte[] image = getStaffImage(institute.getInstituteId(), staff, staffManager);
		if (image == null) {
			return;
		}

		float imageWidth = (documentLayoutSetup.getPageSize().getWidth() / 2) - 10f;
		float imageHeight = (documentLayoutSetup.getPageSize().getWidth() / 2);

		float imageOffsetX = documentLayoutSetup.getPageSize().getWidth()
				- (documentLayoutSetup.getPageSize().getWidth() * (0.75f)) + 5f;
		float imageOffsetY = documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) + 20) + 5f;

		generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight,
				imageOffsetX, imageOffsetY);

	}

	protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		/**
		 * 	aadhar card size - 8.5cmx5.5cm
		 * 	in inches - 3.34646x2.16535
		 * 	1 inch  = 72 points
		 * 	3.34646*72 & 2.16535*72
		 * 	240.94512f X 155.9052f
		 */
		PageSize pageSize = new PageSize(155.9052f, 240.94512f);
		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
		float contentFontSize = 9f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		float logoWidth = 35f;
		float logoHeight = 35f;

		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
				getRegularBoldFont(),
				contentFontSize, 0f, logoWidth, logoHeight,
				LogoProvider.INSTANCE.getLogo(institute.getInstituteId()),
				null, null);

		documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
		return documentLayoutData;
	}

	public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
		return initDocumentLayoutSetup(false, defaultPageSize,
				0f, 0f, 0f, 0f);
	}

	@Override
	public DocumentOutput generateIdentityCards(StaffManager staffManager, Institute institute, List<FullStaffDetails> staffs, String documentName, DocumentPropertiesPreferences documentPropertiesPreferences ) {
		return null;
	}

}
