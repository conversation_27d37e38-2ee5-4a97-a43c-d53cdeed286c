package com.lernen.cloud.pdf.demand.notice;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.StudentDueFeesData;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class DemandNoticeGenerator10030_10031 extends GlobalDemanNoticeGenerator {
	public DemandNoticeGenerator10030_10031(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final String GUARDIAN = "Guardian";
	private static final String DN_CONTENT_PARAGRAPH_1 = "Respected %s, \n Fees for student %s, class %s is due as follows. Please clear the dues as soon as possible to avoid penalties. \n Total Amount: %s/-";
	public static final float SQUARE_BORDER_MARGIN = 5f;

	@Override
	public DocumentOutput generateDemandNotices(Institute institute, List<StudentDueFeesData> dueFeeStudents,
												boolean includeFine, String documentName) {
		try {

			PdfFont boldFont = getCambriaBoldFont();
			PdfFont regularFont = getCambriaFont();

			float contentFontSize = 12f;
			float headerFontSize = 12f;

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput, boldFont, regularFont);
			Document document = documentLayoutData.getDocument();

			int pageNumber = 1;
			for (StudentDueFeesData studentDueFeesData : dueFeeStudents) {

				generateHeader(document, documentLayoutData.getDocumentLayoutSetup(), institute, headerFontSize, boldFont, regularFont);
				generateBorderLayout(documentLayoutData, pageNumber);
				generateContent(document, documentLayoutData.getDocumentLayoutSetup(), contentFontSize, studentDueFeesData, institute, boldFont, regularFont, includeFine);
				generateSignatureBox(document, documentLayoutData.getDocumentLayoutSetup(), contentFontSize, boldFont, regularFont);


				if (pageNumber != dueFeeStudents.size()) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}


			documentLayoutData.getDocument().close();

			return getA4PortraitDemandNotice("FeeNotice", documentOutput);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	protected void generateBorderLayout(DocumentLayoutData documentLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(documentLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(.8f);
	}

	protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute, DocumentOutput documentOutput,
																PdfFont boldFont, PdfFont regularFont)
			throws IOException {

		/**
		 *
		 * 105 x 148.5 mm
		 * 	aadhar card size - 10.5cmx14.85cm
		 * 	in inches - 4.133858x5.8464567
		 * 	1 inch  = 72 points
		 * 	4.133858*72 & 5.8464567*72
		 * 	297.637776f X 420.9448824f
		 * 	290f X 415f
		 */
		PageSize pageSize = new PageSize(280f, 400f);
		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
		float contentFontSize = 12f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		float logoWidth = 30f;
		float logoHeight = 30f;

		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, regularFont,
				boldFont,
				contentFontSize, 0f, logoWidth, logoHeight,
				LogoProvider.INSTANCE.getLogo(institute.getInstituteId()),
				null, null);

		documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
		return documentLayoutData;
	}



	public Image generateImage1(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
			float height) throws MalformedURLException {
		if (image == null) {
			return null;
		}
		Image img = new Image(ImageDataFactory.create(image));
		img.scaleToFit(width, height);
		return img;
//		document.add(img);
	}

	public void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute,
			float headerFontSize, PdfFont boldFont, PdfFont regularFont) throws IOException {

		float logoWidth = 32;
		float logoHeight = 32;
		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, regularFont, boldFont, null,
				null, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);

		int horizontalOffSet = (int) ((-1) * ((documentLayoutSetup.getPageSize().getWidth() / 2) - (logoWidth)));

		generateDynamicImageProvider(documentLayoutData, horizontalOffSet,4, 1f, 1f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		addBlankLine(document, false, 5);

		Table table = getPDFTable(documentLayoutSetup, 1);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(headerFontSize);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(getParagraph(institute.getInstituteName()), cellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(getParagraph(institute.getLetterHeadLine1()), cellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(getParagraph(institute.getLetterHeadLine2()), cellLayoutSetup)));
		document.add(table);

		addBlankLine(document, false, 1);

		int singleContentColumn = 1;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("FEE NOTICE")),
				cellLayoutSetup.setFontSize(headerFontSize).setPdfFont(boldFont));
		document.add(table);

		addBlankLine(document, false, 2);

	}

	private void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			StudentDueFeesData studentDueFeesData, Institute institute, PdfFont boldFont, PdfFont regularFont, boolean includeFine) throws IOException {
		Student student = studentDueFeesData.getStudent();
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(
						getParagraph(String.format("Date %s",
								DateUtils.getFormattedDate((int) (System.currentTimeMillis() / 1000l), DATE_FORMAT,
										User.DFAULT_TIMEZONE))),
						cellLayoutSetup.setTextAlignment(TextAlignment.RIGHT))));

		document.add(table);

		addBlankLine(document, false, 1);

		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		Text t1 = new Text(String.format(DN_CONTENT_PARAGRAPH_1,
				StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName()) ? GUARDIAN
						: student.getStudentFamilyInfo().getFathersName().trim(),
				student.getStudentBasicInfo().getName(),
				student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName(),
				String.valueOf(Math.round(includeFine ? studentDueFeesData.getTotalDueAmountWithFine() : studentDueFeesData.getTotalDueAmount()))));

		Paragraph p1 = new Paragraph(t1);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(p1, cellLayoutSetup.setTextAlignment(TextAlignment.LEFT))));
		document.add(table);
	}

	private void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			PdfFont boldFont, PdfFont regularFont)
			throws IOException {
		int singleContentColumn = 3;

		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		addBlankLine(document, false, 9);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(getParagraph("Principal"), signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
				new CellData(getParagraph("Class Teacher"), signatureCellLayoutSetup),
				new CellData(getParagraph("Guardian"), signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));

		document.add(table);
	}
}
