package com.lernen.cloud.pdf.exam.reports;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.Map.Entry;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamReportGenerator10005 extends ExamReportGenerator implements IExamReportCardGenerator {

	public ExamReportGenerator10005(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10005.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	// public static final float DEFAULT_PAGE_RIGHT_MARGIN = 5f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();
	private static final List<String> SCHOLASTIC_COURSE_NAME_ORDER = Arrays.asList("English", "Hindi", "Maths",
			"Science", "S.St.", "Sanskrit", "Computer");
	public static final Integer RANK_LIMIT = 3;
	// private static final List<String> COSCHOLASTIC_COURSE_NAME_ORDER =
	// Arrays.asList("Hindi", "English", "Maths",
	// "Sanskrit", "Science", "S.St.", "Computer");

	static {
		MARKS_GRADE_MAP.put("86-100", "A+");
		MARKS_GRADE_MAP.put("61-70", "B");
		MARKS_GRADE_MAP.put("71-85", "A");
		MARKS_GRADE_MAP.put("41-60", "C");
		MARKS_GRADE_MAP.put("0-40", "D");
	}

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
										 ExamReportData examReportData, String documentName, StudentManager studentManager) {
		int instituteId = institute.getInstituteId();
		try {
			Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = examReportData
					.getCourseTypeExamReportMarksGrid();
			float contentFontSize = 12f;
			float defaultBorderWidth = 0.1f;
			float logoWidth = 78f;
			float logoHeight = 75f;
			DocumentOutput report = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
			Document document = initDocument(report.getContent(), documentLayoutSetup);
			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
					defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()));
			generateFirstPage(examReportCardLayoutData, document, documentLayoutSetup, institute, examReportData.getStudentLite(), studentManager, 1);
			document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));

			generateSecondPage(examReportCardLayoutData, document, documentLayoutSetup, institute, examReportData.getStudentLite(),
					studentManager, courseTypeExamReportMarksGrid, examReportData, reportType, 2);

			document.close();
			return report;
		} catch (Exception e) {
			logger.error("Exception while generating report card institute {}, student {}, reportType {}", institute,
					student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateSecondPage(ExamReportCardLayoutData examReportCardLayoutData, Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute,
									StudentLite student, StudentManager studentManager,
									Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid,
									ExamReportData examReportData, String reportType, int pageNumber) throws IOException {
		float contentFontSize = 12f;
		float defaultBorderWidth = 0.1f;

		float watermarkImageHeightWidth = 400f;
		//Institute watermark
		generateWatermark(examReportCardLayoutData, institute.getInstituteId(), watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
				watermarkImageHeightWidth / 2);

		generateDynamicImageProvider(examReportCardLayoutData, documentLayoutSetup.getPageSize().getWidth() / 2 - examReportCardLayoutData.getLogoWidth() / 2,
							documentLayoutSetup.getPageSize().getHeight() * 0.785f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		generateHeader(document, documentLayoutSetup, student, institute);
		generateStudentInformation(document, documentLayoutSetup, contentFontSize, student);
		if (courseTypeExamReportMarksGrid.containsKey(CourseType.SCHOLASTIC)) {
			addBlankLine(document, false, 1);
			generateScholasticMarksGrid(document, documentLayoutSetup, contentFontSize, defaultBorderWidth,
					courseTypeExamReportMarksGrid.get(CourseType.SCHOLASTIC), examReportData);
			addBlankLine(document, false, 1);
		}
		if (courseTypeExamReportMarksGrid.containsKey(CourseType.COSCHOLASTIC)) {
			generateCoScholasticMarksGridSection(document, documentLayoutSetup, contentFontSize, defaultBorderWidth,
					courseTypeExamReportMarksGrid.get(CourseType.COSCHOLASTIC), false, examReportData);
		}
		generateResultSummary(document, documentLayoutSetup, contentFontSize, examReportData, reportType);

		if(reportType.equalsIgnoreCase("ANNUAL")) {
			generateSignatureBox(document, documentLayoutSetup, contentFontSize);
		} else {
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, pageNumber);
		}
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	private void generateFirstPage(ExamReportCardLayoutData examReportCardLayoutData, Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute,
								   StudentLite student, StudentManager studentManager, int pageNumber) throws IOException {
		int instituteId = institute.getInstituteId();
		float logoWidth = examReportCardLayoutData.getLogoWidth();
		float logoHeight = examReportCardLayoutData.getLogoHeight();
		float contentFontSize = 14f;
		generateDynamicImageProvider(examReportCardLayoutData, 40f, documentLayoutSetup.getPageSize().getHeight() * 0.81f, 
									instituteId, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
		generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider.GODESS_SARASWATI),
				logoWidth, logoHeight, documentLayoutSetup.getPageSize().getWidth() - 40f - logoWidth,
				documentLayoutSetup.getPageSize().getHeight() * 0.81f);

		generateFirstPageHeader(document, documentLayoutSetup, student, institute);
		generateFirstPageStudentInformation(document, documentLayoutSetup, contentFontSize, student);

		byte[] studentImage = getStudentImage(instituteId, student.getStudentId(), studentManager);
		if (studentImage != null) {
			generateImage(document, documentLayoutSetup, studentImage, 100f, 125f,
					documentLayoutSetup.getPageSize().getWidth() * 0.75f,
					documentLayoutSetup.getPageSize().getHeight() * 0.5f);
		}
		generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider._10005_BUILDING),
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2, 250f, SQUARE_BORDER_MARGIN,
				SQUARE_BORDER_MARGIN);
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}


	private void generateFirstPageHeader(Document document, DocumentLayoutSetup documentLayoutSetup, StudentLite student,
										 Institute institute) throws IOException {

		int headlineContentColumn = 2;
		Table headlineTable = getPDFTable(documentLayoutSetup, headlineContentColumn);
		headlineTable.setFixedPosition(SQUARE_BORDER_MARGIN + 4,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN * 2 + 8),
				documentLayoutSetup.getPageSize().getWidth() - documentLayoutSetup.getSideMargin() - 5);

		CellLayoutSetup headlineCellLayoutSetup = new CellLayoutSetup();
		headlineCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(11f).setTextAlignment(TextAlignment.LEFT);

		addRow(headlineTable, documentLayoutSetup,
				Arrays.asList(new CellData("Reg.No:- 141/BKN/2014-15", headlineCellLayoutSetup),
						new CellData("Affiliation:- 2018-19/470-473",
								headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));

		document.add(headlineTable);

		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

		addBlankLine(document, true, 1);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase())),
				cellLayoutSetup.copy().setFontSize(28f));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph("Phone : 9667622322, Email : <EMAIL>")), cellLayoutSetup);
		document.add(table);

		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		addBlankLine(document, true, 1);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Report Card")),
				cellLayoutSetup.copy().setFontSize(18f));
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph("Class: LKG - VIII ("
						+ student.getStudentSessionData().getShortYearDisplayName()
						+ ")")),
				cellLayoutSetup);

		document.add(table);
		addBlankLine(document, true, 1);

	}

	private void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, StudentLite student,
								Institute institute) throws IOException {
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase())),
				cellLayoutSetup.copy().setFontSize(28f));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup);

		document.add(table);

		addBlankLine(document, true, 3);

		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Session "
						+ student.getStudentSessionData().getShortYearDisplayName())),
				cellLayoutSetup);
		document.add(table);
		addBlankLine(document, false, 1);

	}

	private void generateFirstPageStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
													 float contentFontSize, StudentLite student) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.7f, 0.3f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);

		Paragraph admissionNumber = getKeyValueParagraph("Admission No. ",
				student.getAdmissionNumber());

		Paragraph studentName = getKeyValueParagraph("Student Name - ", student.getName());

		Paragraph fatherName = getKeyValueParagraph("Father Name - ", student.getFathersName());

		Paragraph motherName = getKeyValueParagraph("Mother Name - ", student.getMothersName());

		Paragraph session = getKeyValueParagraph("Session - ",
				student.getStudentSessionData().getAcademicSessionName());

		Paragraph classValue = getKeyValueParagraph("Class - ",
				student.getStudentSessionData().getStandardNameWithSection());

		Paragraph dob = getKeyValueParagraph("DOB - ",
				student.getDateOfBirth() == null
						|| student.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(student.getDateOfBirth(),
						DATE_FORMAT, User.DFAULT_TIMEZONE));

		Paragraph rollNumber = getKeyValueParagraph("Roll No. - ", StringUtils.isBlank(student.getStudentSessionData().getRollNumber()) ? "" : student.getStudentSessionData().getRollNumber());
		Paragraph address = getKeyValueParagraph("Address - ", student.getPermanentAddress());
		Paragraph totalAttendance = getKeyValueParagraph("Total Working Attendance - ", String.valueOf(320));
		Paragraph totalAttended = getKeyValueParagraph("Total Attendance - ", String.valueOf(290));

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				cellLayoutSetup);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("Student Profile", cellLayoutSetup.copy().setFontSize(18f)),
						new CellData(new Paragraph(""), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT), getParagraph(EMPTY_TEXT)),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentName, cellLayoutSetup),
				new CellData(new Paragraph(""), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT), getParagraph(EMPTY_TEXT)),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(fatherName, cellLayoutSetup),
				new CellData(new Paragraph(""), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT), getParagraph(EMPTY_TEXT)),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(motherName, cellLayoutSetup),
				new CellData(new Paragraph(""), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT), getParagraph(EMPTY_TEXT)),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, cellLayoutSetup), new CellData(new Paragraph(""), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT), getParagraph(EMPTY_TEXT)),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(rollNumber, cellLayoutSetup), new CellData(new Paragraph(""), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT), getParagraph(EMPTY_TEXT)),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(address, cellLayoutSetup),
				new CellData(new Paragraph(""), cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				cellLayoutSetup);
		// addRow(table, documentLayoutSetup,
		// Arrays.asList(new CellData("ATTENDANCE", cellLayoutSetup), new
		// CellData("", cellLayoutSetup)));
		// addRow(table, documentLayoutSetup,
		// Arrays.asList(new CellData(totalAttendance, cellLayoutSetup), new
		// CellData("", cellLayoutSetup)));
		// addRow(table, documentLayoutSetup,
		// Arrays.asList(new CellData(totalAttended, cellLayoutSetup), new
		// CellData("", cellLayoutSetup)));

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				cellLayoutSetup);

		document.add(table);

	}

	private void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
											float contentFontSize, StudentLite student) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.45f, 0.2f, 0.35f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph admissionNumber = getKeyValueParagraph("Admission No. ",
				student.getAdmissionNumber());

		Paragraph studentName = getKeyValueParagraph("Student Name : ", student.getName());

		Paragraph fatherName = getKeyValueParagraph("Father Name : ", student.getFathersName());

		Paragraph motherName = getKeyValueParagraph("Mother Name : ", student.getMothersName());

		Paragraph session = getKeyValueParagraph("Session : ",
				student.getStudentSessionData().getShortYearDisplayName());

		Paragraph classValue = getKeyValueParagraph("Class : ",
				student.getStudentSessionData().getStandardNameWithSection());

		Paragraph dob = getKeyValueParagraph("DOB : ",
				student.getDateOfBirth() == null
						|| student.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(student.getDateOfBirth(),
						DATE_FORMAT, User.DFAULT_TIMEZONE));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup),
						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(fatherName, thirdCellLayoutSetup)));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup),
						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(motherName, thirdCellLayoutSetup)));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup),
						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(classValue, thirdCellLayoutSetup)));
		//
		// addRow(table, documentLayoutSetup, Arrays.asList(new
		// CellData(fatherName, firstCellLayoutSetup),
		// new CellData(new Paragraph(""), secondCellLayoutSetup), new
		// CellData(dob, thirdCellLayoutSetup)));
		//
		// addRow(table, documentLayoutSetup,
		// Arrays.asList(new CellData(motherName, firstCellLayoutSetup),
		// new CellData(new Paragraph(""), secondCellLayoutSetup),
		// new CellData(new Paragraph(""), thirdCellLayoutSetup)));

		// addRow(table, documentLayoutSetup,
		// Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);

		document.add(table);

	}

	private void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
											 float contentFontSize, float defaultBorderWidth, ExamReportMarksGrid examReportMarksGrid,
											 ExamReportData examReportData) throws IOException {
		float subjectColumnWidth = 0.15f;
		CourseType courseType = CourseType.SCHOLASTIC;
		float[] columnWidths = getMarksGridWidth(examReportMarksGrid, courseType, false,
				examReportData.getExamReportStructure().getExamReportStructureMetaData(), subjectColumnWidth, false,  false, 0f);
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);

		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(getRegularBoldFont())
				.setTextAlignment(TextAlignment.LEFT);

		addRow(headerTable, documentLayoutSetup,
				getHeaderCells(examReportMarksGrid,
						examReportData, courseType,
						defaultBorderWidth, getRegularFont(), getRegularBoldFont(), contentFontSize - 2,
						contentFontSize, subjectColumnWidth, "Subject", false, "MM", "MO", false));

		Collections.sort(examReportMarksGrid.getExamReportCourseMarksRows(),
				new Comparator<ExamReportCourseMarksRow>() {

					@Override
					public int compare(ExamReportCourseMarksRow o1, ExamReportCourseMarksRow o2) {

						return o1.getCourse().compareTo(o2.getCourse());
					}
				});
		Map<String, ExamReportCourseMarksRow> courseNameExamReportRowMap = new LinkedHashMap<>();

		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
			if (CollectionUtils.isEmpty(examReportCourseMarksRow.getExamReportCourseMarksColumns())) {
				continue;
			}
			courseNameExamReportRowMap.put(examReportCourseMarksRow.getCourse().getCourseName().toLowerCase().trim(),
					examReportCourseMarksRow);
		}

		for (String courseName : SCHOLASTIC_COURSE_NAME_ORDER) {
			if (courseNameExamReportRowMap.containsKey(courseName.toLowerCase())) {
				ExamReportCourseMarksRow examReportCourseMarksRow = courseNameExamReportRowMap
						.get(courseName.toLowerCase());

				addExamCourseRow(documentLayoutSetup,
						examReportData, courseType,
						headerTable, marksCellLayoutSetup, courseCellLayoutSetup, examReportCourseMarksRow, false);

				courseNameExamReportRowMap.remove(courseName.toLowerCase());
			}
		}

		for (Entry<String, ExamReportCourseMarksRow> remainingCourseEntry : courseNameExamReportRowMap.entrySet()) {
			ExamReportCourseMarksRow examReportCourseMarksRow = remainingCourseEntry.getValue();
			addExamCourseRow(documentLayoutSetup,
					examReportData, courseType, headerTable,
					marksCellLayoutSetup, courseCellLayoutSetup, examReportCourseMarksRow, false);
		}

		CourseTotalMarksRows courseTotalMarksRows = generateCourseTotalMarksInGrid(examReportMarksGrid,
				examReportData, courseType,
				marksCellLayoutSetup, courseCellLayoutSetup, false, null, GridConfigs.forOnlyObtainedTotalRow());

		addRow(headerTable, documentLayoutSetup, courseTotalMarksRows.getObtainedMarksRow());

//		List<Paragraph> emptyLines = new ArrayList<>();
//		for (float column : columnWidths) {
//			emptyLines.add(getParagraph(EMPTY_TEXT));
//		}
//
//		addRow(headerTable, documentLayoutSetup, emptyLines, new CellLayoutSetup());
//		addRow(headerTable, documentLayoutSetup, emptyLines, new CellLayoutSetup());

		document.add(headerTable);

	}

	private void generateCoScholasticMarksGridSection(Document document, DocumentLayoutSetup documentLayoutSetup,
													  float contentFontSize, float defaultBorderWidth, ExamReportMarksGrid examReportMarksGrid, boolean showTotal,
													  ExamReportData examReportData) throws IOException {

		float subjectColumnWidth = 0.24f;
		CourseType courseType = CourseType.COSCHOLASTIC;

		float[] columnWidths = new float[] { subjectColumnWidth, 0.24f, 0.04f, 0.24f, 0.24f };
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);

		CellLayoutSetup emptyCellLayoutSetup = new CellLayoutSetup();
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(getRegularBoldFont())
				.setTextAlignment(TextAlignment.LEFT);

		addBlankLine(document, false, 1);

		List<CellData> headerList = new ArrayList<>();
		headerList.addAll(getHeaderCells(examReportMarksGrid,
				examReportData, courseType,
				defaultBorderWidth, getRegularFont(), getRegularBoldFont(), contentFontSize, contentFontSize,
				subjectColumnWidth, "Subject", true, "MM", "MO", false));

		headerList.add(new CellData("", emptyCellLayoutSetup));
		headerList.add(new CellData("Marks", marksCellLayoutSetup));
		headerList.add(new CellData("Grade", marksCellLayoutSetup));
		addRow(headerTable, documentLayoutSetup, headerList);

		List<Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());

		Collections.sort(examReportMarksGrid.getExamReportCourseMarksRows(),
				new Comparator<ExamReportCourseMarksRow>() {

					@Override
					public int compare(ExamReportCourseMarksRow o1, ExamReportCourseMarksRow o2) {

						return o1.getCourse().compareTo(o2.getCourse());
					}
				});
		int index = 0;
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
			if (CollectionUtils.isEmpty(examReportCourseMarksRow.getExamReportCourseMarksColumns())) {
				continue;
			}
			List<CellData> row = new ArrayList<>();
			row.add(new CellData(examReportCourseMarksRow.getCourse().getCourseName(), courseCellLayoutSetup));
			for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
					.getExamReportCourseMarksColumns()) {
				/**
				 * Assuming grades only for coscholatic rows
				 */
				if (CollectionUtils.isEmpty(examReportCourseMarksColumn.getExamDimensionObtainedValuesList())) {
					continue;
				}
				ExamDimensionObtainedValues examDimensionObtainedValues = examReportCourseMarksColumn
						.getExamDimensionObtainedValuesList().get(0);
				String value = ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? "-"
						: examDimensionObtainedValues.getObtainedGrade().getGradeName();
				row.add(new CellData(value, marksCellLayoutSetup));
			}

			row.add(new CellData("", emptyCellLayoutSetup));
			if (index < gradesList.size()) {
				row.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
				row.add(new CellData(gradesList.get(index++).getValue(), marksCellLayoutSetup));
			} else {
				row.add(new CellData("", emptyCellLayoutSetup));
				row.add(new CellData("", emptyCellLayoutSetup));
			}

			addRow(headerTable, documentLayoutSetup, row);
		}
		List<CellData> attendanceRow1 = new ArrayList<>();
		attendanceRow1
				.add(new CellData("Attendance", emptyCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)));
		attendanceRow1.add(new CellData("", emptyCellLayoutSetup));
		attendanceRow1.add(new CellData("", emptyCellLayoutSetup));
		if (index < gradesList.size()) {
			attendanceRow1.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			attendanceRow1.add(new CellData(gradesList.get(index++).getValue(), marksCellLayoutSetup));
		} else {
			attendanceRow1.add(new CellData("", emptyCellLayoutSetup));
			attendanceRow1.add(new CellData("", emptyCellLayoutSetup));
		}
		addRow(headerTable, documentLayoutSetup, attendanceRow1);

		List<CellData> attendanceRow2 = new ArrayList<>();
		attendanceRow2.add(
				new CellData("Total Working Days", marksCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)));
		attendanceRow2.add(new CellData(examReportData.getTotalWorkingDays() == null ? "-"
				: String.valueOf(examReportData.getTotalWorkingDays()), marksCellLayoutSetup));
		attendanceRow2.add(new CellData("", emptyCellLayoutSetup));

		if (index < gradesList.size()) {
			attendanceRow2.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			attendanceRow2.add(new CellData(gradesList.get(index++).getValue(), marksCellLayoutSetup));
		} else {
			attendanceRow2.add(new CellData("", emptyCellLayoutSetup));
			attendanceRow2.add(new CellData("", emptyCellLayoutSetup));
		}

		addRow(headerTable, documentLayoutSetup, attendanceRow2);

		List<CellData> attendanceRow3 = new ArrayList<>();
		attendanceRow3.add(
				new CellData("Total Attended Days", marksCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)));
		attendanceRow3.add(new CellData(examReportData.getTotalAttendedDays() == null ? "-"
				: String.valueOf(examReportData.getTotalAttendedDays()), marksCellLayoutSetup));
		attendanceRow3.add(new CellData("", emptyCellLayoutSetup));

		if (index < gradesList.size()) {
			attendanceRow3.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			attendanceRow3.add(new CellData(gradesList.get(index++).getValue(), marksCellLayoutSetup));
		} else {
			attendanceRow3.add(new CellData("", emptyCellLayoutSetup));
			attendanceRow3.add(new CellData("", emptyCellLayoutSetup));
		}

		addRow(headerTable, documentLayoutSetup, attendanceRow3);

		if (index < gradesList.size()) {
			while (index < gradesList.size()) {
				List<CellData> reminingRows = new ArrayList<>();
				reminingRows.add(new CellData("", emptyCellLayoutSetup));
				reminingRows.add(new CellData("", emptyCellLayoutSetup));
				reminingRows.add(new CellData("", emptyCellLayoutSetup));
				reminingRows.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
				reminingRows.add(new CellData(gradesList.get(index++).getValue(), marksCellLayoutSetup));
				addRow(headerTable, documentLayoutSetup, reminingRows);
			}
		}

		/**
		 * only showing sum in last column
		 */
		if (showTotal) {
			List<CellData> row = new ArrayList<>();
			row.add(new CellData("Total", courseCellLayoutSetup, 1,
					columnWidths.length - examReportMarksGrid.getExamReportTotalMarksColumns()
							.get(examReportMarksGrid.getExamReportTotalMarksColumns().size() - 1)
							.getExamDimensionObtainedValuesList().size()));

			for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportMarksGrid
					.getExamReportTotalMarksColumns()
					.get(examReportMarksGrid.getExamReportTotalMarksColumns().size() - 1)
					.getExamDimensionObtainedValuesList()) {
				String value = ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? "-"
						: examDimensionObtainedValues.getObtainedGrade().getGradeName();
				row.add(new CellData(value, marksCellLayoutSetup));
			}

			addRow(headerTable, documentLayoutSetup, row);
		}

		// addRow(headerTable, documentLayoutSetup,
		// Arrays.asList(getParagraph(NEXT_LINE)), new CellLayoutSetup());

		document.add(headerTable);

	}

	private void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
									   float contentFontSize, ExamReportData examReportData, String reportType) throws IOException {
		int singleContentColumn = 3;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);

		addBlankLine(document, false, 1);

		Paragraph result = getKeyValueParagraph("Result: ",
				examReportData.getExamResultStatus() == null ? "-" : examReportData.getExamResultStatus().name());

		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
				examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName());
		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
						? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
						: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d));
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10));
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%");
		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName());
		Paragraph rank = null;
		if (examReportData.getRank() != null && examReportData.getRank() <= RANK_LIMIT) {
			rank = getKeyValueParagraph("Rank : ",
					examReportData.getRank() == null ? "-" : String.valueOf(examReportData.getRank()));
		}

		if(reportType.equalsIgnoreCase("ANNUAL")) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
		}
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));
		if (rank != null) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(rank, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
		}
		document.add(table);

		Table remarksTable = getPDFTable(documentLayoutSetup, 1);
		Paragraph remarks = getKeyValueParagraph("Remarks: ",
				examReportData.getRemarks() == null ? "" : examReportData.getRemarks());
		addRow(remarksTable, documentLayoutSetup, Arrays.asList(remarks), cellLayoutSetup);
		document.add(remarksTable);
		// addRow(table, documentLayoutSetup, Arrays.asList(new
		// CellData(remarks, cellLayoutSetup),
		// new CellData("",
		// cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));

		// document.add(table);
	}

	private void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize)
			throws IOException {
		int singleContentColumn = 3;

		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Exam Controller"), getParagraph("Class Teacher"),
				getParagraph("Principal")), signatureCellLayoutSetup);
		table.setFixedPosition(SQUARE_BORDER_MARGIN + 3, SQUARE_BORDER_MARGIN + 3,
				documentLayoutSetup.getPageSize().getWidth());
		document.add(table);
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, float defaultBorderWidth, int pageNumber) throws IOException {
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.moveTo(12, 95f);
		canvas.lineTo(583, 95f);
		canvas.setLineWidth(.5f);
		canvas.closePathStroke();
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Exam Controller"), getParagraph("Class Teacher"),
				getParagraph("Principal")), signatureCellLayoutSetup);
		table.setFixedPosition(30f, 95f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
		generateGradeBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
	}

	private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								  float defaultBorderWidth) throws IOException {
//		addBlankLine(document, false, 1);

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize - 2)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("MARKS RANGE")), signatureCellLayoutSetup);
		table.setFixedPosition(10f, 75f, documentLayoutSetup.getPageSize().getWidth());
		document.add(table);

		float[] columnWidths = new float[] { 0.24f, 0.24f, 0.24f, 0.24f };
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize - 4)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
		addBlankLine(document, false, 1);
		List<CellData> headerList = new ArrayList<>();
		headerList.add(new CellData("Marks", marksCellLayoutSetup));
		headerList.add(new CellData("Grade", marksCellLayoutSetup));
		headerList.add(new CellData("Marks", marksCellLayoutSetup));
		headerList.add(new CellData("Grade", marksCellLayoutSetup));
		addRow(headerTable, documentLayoutSetup, headerList);
		List<Map.Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());
		for(int index = 0; index < gradesList.size() / 2; index++) {
			List<CellData> row = new ArrayList<>();
			row.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(index).getValue(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getKey(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getValue(), marksCellLayoutSetup));
			addRow(headerTable, documentLayoutSetup, row);
		}
		if(gradesList.size() % 2 != 0) {
			List<CellData> row = new ArrayList<>();
			row.add(new CellData(gradesList.get(gradesList.size() - 1).getKey(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(gradesList.size() - 1).getValue(), marksCellLayoutSetup));
			row.add(new CellData(EMPTY_TEXT, marksCellLayoutSetup));
			row.add(new CellData(EMPTY_TEXT, marksCellLayoutSetup));
			addRow(headerTable, documentLayoutSetup, row);
		}
		headerTable.setFixedPosition(35f, 15f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(headerTable);
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName,
													StudentManager studentManager) {
		try {
			DocumentOutput report = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
			Document document = initDocument(report.getContent(), documentLayoutSetup);
			float contentFontSize = 12f;
			float defaultBorderWidth = 0.1f;
			float logoWidth = 78f;
			float logoHeight = 75f;
			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
					defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()));
			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateFirstPage(examReportCardLayoutData, document, documentLayoutSetup, institute, examReportData.getStudentLite(), studentManager, pageNumber);

				document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				pageNumber++;

				generateSecondPage(examReportCardLayoutData, document, documentLayoutSetup, institute, examReportData.getStudentLite(),
						studentManager, examReportData.getCourseTypeExamReportMarksGrid(), examReportData, reportType, pageNumber);

				if (pageNumber != examReportDataList.size() * 2) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}
			document.close();
			return report;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;

	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			float logoWidth = 75f;
			float logoHeight = 75f;
			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			int instituteId = institute.getInstituteId();
			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
					DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document, documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH,
						examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC), examReportData);
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}

}
