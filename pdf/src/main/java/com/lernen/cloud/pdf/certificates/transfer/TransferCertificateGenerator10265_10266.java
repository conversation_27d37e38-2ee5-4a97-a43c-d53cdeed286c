package com.lernen.cloud.pdf.certificates.transfer;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentTransferCertificateDetails;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransferCertificateGenerator10265_10266 extends GlobalTransferCertificateGenerator {
	public TransferCertificateGenerator10265_10266(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final String TC_CONTENT_PARAGRAPH_1 = "This is to certify that ";
	private static final String TC_CONTENT_PARAGRAPH_2 = ", Father's Name ";
	private static final String TC_CONTENT_PARAGRAPH_3 = ", Mother's Name ";
	private static final String TC_CONTENT_PARAGRAPH_4 = ", Address ";
	private static final String TC_CONTENT_PARAGRAPH_5 = " was admitted to this school in class ";
	private static final String TC_CONTENT_PARAGRAPH_6 = " on ";
	private static final String TC_CONTENT_PARAGRAPH_7 = " and left on (Date) ";
	private static final String TC_CONTENT_PARAGRAPH_8 = " was then studying in class ";
	private static final String TC_CONTENT_PARAGRAPH_9 = " in school academic session being from ";
	private static final String TC_CONTENT_PARAGRAPH_10 = " to ";
	private static final String TC_CONTENT_PARAGRAPH_11 = " character while in school was ";
	private static final String TC_CONTENT_PARAGRAPH_12 = " All sums due to this school have been paid upto March ";
	private static final String TC_CONTENT_PARAGRAPH_13 = " date of birth according to school records (figure) ";
	private static final String TC_CONTENT_PARAGRAPH_14 = " (in words) ";
	private static final String TC_CONTENT_PARAGRAPH_15 = "Promotion has been granted to class ";

	@Override
	public DocumentOutput generateTransferCertificate(StudentManager studentManager, Institute institute, Student student, 
			String documentName) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
			float contentFontSize = 13f;

			float squareBorderMargin = 8f;
			float borderInnerGap = 2f;
			float defaultBorderWidth = 0.5f;

			Document document = initDocument(invoice.getContent(), documentLayoutSetup);
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
					null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			generateDynamicImageProvider(documentLayoutData, 20f, documentLayoutSetup.getPageSize().getHeight() * 0.87f,
					1.5f, 1.5f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

			float bgImageHeightWidth = 500f;
			//Institute watermark
			int instituteId = institute.getInstituteId();
			generateWatermark(documentLayoutData, instituteId, bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);


			generateHeader(document, documentLayoutSetup, documentLayoutData, student, institute, boldFont, regularFont, studentManager, defaultBorderWidth);
			generateContent(document, documentLayoutSetup, contentFontSize, student, institute, boldFont, regularFont);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont);
			generateMetadata(document, documentLayoutSetup, squareBorderMargin, borderInnerGap,1);

			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	protected void generateMetadata(Document document, DocumentLayoutSetup documentLayoutSetup, float squareBorderMargin, float innerGap, int pageNumber) {
		generateBorderLayout(document, documentLayoutSetup, pageNumber, squareBorderMargin, innerGap);
	}

	private void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, DocumentLayoutData documentLayoutData, Student student,
			Institute institute, PdfFont boldFont, PdfFont regularFont, StudentManager studentManager,
								float defaultBorderWidth) throws IOException {

//		float bgImageHeightWidth = 500f;
//		//Institute watermark
//		int instituteId = institute.getInstituteId();
//		generateWatermark(documentLayoutData, instituteId, bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);

//		generateImage(document, documentLayoutSetup, LogoProvider.INSTANCE.getLogo(instituteId),
//				80f, 70f, 20f, documentLayoutSetup.getPageSize().getHeight() * 0.88f);

		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		String instituteName = institute.getInstituteName().toUpperCase();
		List<Integer> rgb = EColorUtils.hex2Rgb("#744b49");
		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(instituteName, rgb.get(0), rgb.get(1), rgb.get(2)).setMultipliedLeading(1.0f)),
				cellLayoutSetup.copy().setFontSize(40f).setPdfFont(boldFont).setTextAlignment(TextAlignment.CENTER));
		String letterHead1 = institute.getLetterHeadLine1();
		letterHead1 = "(Govt. Recognised)\nAff. No. G.J.H.S -269";
		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(letterHead1, 0, 0, 0)),
				cellLayoutSetup.copy().setFontSize(12f).setPdfFont(regularFont).setTextAlignment(TextAlignment.CENTER));
		String letterHead2 = institute.getLetterHeadLine2();
		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(letterHead2, 0, 0, 0)),
				cellLayoutSetup.copy().setFontSize(12f).setPdfFont(regularFont).setTextAlignment(TextAlignment.CENTER));

		document.add(table);

		addBlankLine(document, false, 2);

		table = getPDFTable(documentLayoutSetup, 1);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(
				"SCHOOL LEAVING CERTIFICATE", rgb.get(0), rgb.get(1), rgb.get(2)).setUnderline(),
				cellLayoutSetup.copy().setFontSize(20f).setPdfFont(boldFont).setTextAlignment(TextAlignment.CENTER))));

		document.add(table);

		addBlankLine(document, true, 1);
	}

	private void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								 Student student, Institute institute, PdfFont boldFont, PdfFont regularFont) throws IOException {
		int singleContentColumn = 1;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(regularFont)
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.JUSTIFIED);
		StudentTransferCertificateDetails studentTransferCertificateDetails = student.getStudentTransferCertificateDetails();

		String heShe = "He/She";
		String hisHer = "His/Her";
		String himHer = "Him/Her";
		if (student.getStudentBasicInfo().getGender() == Gender.FEMALE) {
			heShe = "She";
			hisHer = "Her";
			himHer = "Her";
		} else if (student.getStudentBasicInfo().getGender() == Gender.MALE) {
			heShe = "He";
			hisHer = "His";
			himHer = "Him";
		}

		Table table = getPDFTable(documentLayoutSetup, 2);

		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(getKeyValueParagraph("Serial No. : ", studentTransferCertificateDetails.getTcNumber(), regularFont, regularFont, false, true)
						.setTextAlignment(TextAlignment.LEFT), cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
				new CellData(getKeyValueParagraph("S.R. No. : ", "", regularFont, regularFont, false, false)
						.setTextAlignment(TextAlignment.RIGHT), cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));

		document.add(table);

		addBlankLine(document, false, 2);

		table = getPDFTable(documentLayoutSetup, 1);


		Text keyPara1 = new Text(TC_CONTENT_PARAGRAPH_1);
		Text valuePara1 = new Text(studentTransferCertificateDetails.getTcStudentDetails().getStudentName()).setUnderline();

		Text keyPara2 = new Text(TC_CONTENT_PARAGRAPH_2);
		Text valuePara2 = new Text(studentTransferCertificateDetails.getTcStudentDetails().getFatherGuardianName()).setUnderline();

		Text keyPara3 = new Text(TC_CONTENT_PARAGRAPH_3);
		Text valuePara3 = new Text(studentTransferCertificateDetails.getTcStudentDetails().getMotherName()).setUnderline();

		Text keyPara4 = new Text(TC_CONTENT_PARAGRAPH_4);
		Text valuePara4 = new Text(student.getStudentBasicInfo().getStudentPermanentFullAddress()).setUnderline();

		Text keyPara5 = new Text(TC_CONTENT_PARAGRAPH_5);
		Text valuePara5 = new Text(studentTransferCertificateDetails.getTcStudentDetails().getAdmissionClass()).setUnderline();

		Text keyPara6 = new Text(TC_CONTENT_PARAGRAPH_6);
		Text valuePara6 = new Text(studentTransferCertificateDetails.getTcStudentDetails().getAdmissionDate() == null
				|| studentTransferCertificateDetails.getTcStudentDetails().getAdmissionDate() <= 0 ? EMPTY_TEXT :
				DateUtils.getFormattedDate(studentTransferCertificateDetails.getTcStudentDetails().getAdmissionDate())).setUnderline();

		Text keyPara7 = new Text(TC_CONTENT_PARAGRAPH_7);
		Text valuePara7 = new Text(studentTransferCertificateDetails.getTcStudentRelievingDetails().getRelieveDate() == null
				|| studentTransferCertificateDetails.getTcStudentRelievingDetails().getRelieveDate() <= 0 ? EMPTY_TEXT :
				DateUtils.getFormattedDate(studentTransferCertificateDetails.getTcStudentRelievingDetails().getRelieveDate()) + ".").setUnderline();


		Paragraph paragraph = new Paragraph();
		paragraph.add(keyPara1).add(valuePara1).add(keyPara2).add(valuePara2).add(keyPara3).add(valuePara3).add(keyPara4).add(valuePara4)
				.add(keyPara5).add(valuePara5).add(keyPara6).add(valuePara6).add(keyPara7).add(valuePara7);
		addRow(table, documentLayoutSetup, Collections.singletonList(paragraph), cellLayoutSetup);


		Text keyPara8 = new Text(heShe + TC_CONTENT_PARAGRAPH_8);
		Text valuePara8 = new Text(studentTransferCertificateDetails.getTcStudentLastActiveSessionDetails().getLastActiveSessionClass()).setUnderline();

		Text keyPara9 = new Text(TC_CONTENT_PARAGRAPH_9);
		Text valuePara9 = new Text(student.getStudentAcademicSessionInfoResponse().getAcademicSession().getStartMonth().name() + " " +
				student.getStudentAcademicSessionInfoResponse().getAcademicSession().getStartYear()).setUnderline();

		Text keyPara10 = new Text(TC_CONTENT_PARAGRAPH_10);
		Text valuePara10 = new Text(student.getStudentAcademicSessionInfoResponse().getAcademicSession().getEndMonth().name() + " " +
				student.getStudentAcademicSessionInfoResponse().getAcademicSession().getEndYear()).setUnderline();

		paragraph = new Paragraph();
		paragraph.add(keyPara8).add(valuePara8).add(keyPara9).add(valuePara9).add(keyPara10).add(valuePara10);
		addRow(table, documentLayoutSetup, Collections.singletonList(paragraph), cellLayoutSetup);

		document.add(table);

		addBlankLine(document, false, 2);

		table = getPDFTable(documentLayoutSetup, 1);

		List<Integer> rgb = EColorUtils.hex2Rgb("#744b49");
		Text keyPara11 = new Text(hisHer + TC_CONTENT_PARAGRAPH_11).setFontColor((new DeviceRgb(rgb.get(0), rgb.get(1), rgb.get(2))));
		Text valuePara11 = new Text(studentTransferCertificateDetails.getTcStudentRelievingDetails().getCodeOfConduct() + ".").setUnderline();

		paragraph = new Paragraph();
		paragraph.add(keyPara11).add(valuePara11);
		addRow(table, documentLayoutSetup, Collections.singletonList(paragraph), cellLayoutSetup);

		Text keyPara12 = new Text(TC_CONTENT_PARAGRAPH_12).setFontColor((new DeviceRgb(rgb.get(0), rgb.get(1), rgb.get(2))));
		Text valuePara12 = new Text("PAID.").setUnderline();

		paragraph = new Paragraph();
		paragraph.add(keyPara12).add(valuePara12);
		addRow(table, documentLayoutSetup, Collections.singletonList(paragraph), cellLayoutSetup);

		document.add(table);

		addBlankLine(document, false, 2);

		table = getPDFTable(documentLayoutSetup, 1);

		String dobInFigures = studentTransferCertificateDetails.getTcStudentDetails().getDob() == null || studentTransferCertificateDetails.getTcStudentDetails().getDob() <= 0
				? EMPTY_VALUE : DateUtils.getFormattedDate(studentTransferCertificateDetails.getTcStudentDetails().getDob());
		Text keyPara13 = new Text(hisHer + TC_CONTENT_PARAGRAPH_13);
		Text valuePara13 = new Text(dobInFigures).setUnderline();

		String dobInWords = studentTransferCertificateDetails.getTcStudentDetails().getDob() == null || studentTransferCertificateDetails.getTcStudentDetails().getDob() <= 0
				? EMPTY_VALUE : DateUtils.getDateInWords(studentTransferCertificateDetails.getTcStudentDetails().getDob());
		Text keyPara14 = new Text(TC_CONTENT_PARAGRAPH_14);
		Text valuePara14 = new Text(dobInWords).setUnderline();

		paragraph = new Paragraph();
		paragraph.add(keyPara13).add(valuePara13).add(keyPara14).add(valuePara14);
		addRow(table, documentLayoutSetup, Collections.singletonList(paragraph), cellLayoutSetup);

		document.add(table);

		addBlankLine(document, false, 2);

		table = getPDFTable(documentLayoutSetup, 1);

		Text keyPara15 = new Text(TC_CONTENT_PARAGRAPH_15).setFontColor((new DeviceRgb(rgb.get(0), rgb.get(1), rgb.get(2))));
		Text valuePara15 = new Text(studentTransferCertificateDetails.getTcStudentLastActiveSessionDetails().getPromotingClassName() +".").setUnderline();

		paragraph = new Paragraph();
		paragraph.add(keyPara15).add(valuePara15);
		addRow(table, documentLayoutSetup, Collections.singletonList(paragraph), cellLayoutSetup);

		document.add(table);
	}

	private void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize, PdfFont boldFont) {
		int singleContentColumn = 3;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(boldFont)
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.RIGHT);
		PageSize pageSize = documentLayoutSetup.getPageSize();

		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData("Prepared By", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
				new CellData("Checked By", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
				new CellData("Principal", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))
		));
		table.setFixedPosition(document.getLeftMargin(), document.getBottomMargin() + 5, pageSize.getWidth() - document.getLeftMargin() - document.getRightMargin());

		document.add(table);

	}

}
