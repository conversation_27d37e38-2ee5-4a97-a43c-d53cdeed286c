package com.lernen.cloud.pdf.demand.notice;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.font.constants.StandardFonts;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.StudentDueFeesData;
import com.lernen.cloud.core.api.fees.payment.StudentFeesLevelPaymentData;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class GlobalHindiDemanNoticeGenerator extends DemandNoticeGenerator {
//	public static final String LICENSE = "/Users/<USER>/Lernen/lernen-backend/pdf/src/main/resources/itext-keys/itextkey1564111938952_0.xml";

	public GlobalHindiDemanNoticeGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final String DN_CONTENT_PARAGRAPH_1 = "This fee notice is in regards to clear the fee dues for %s %s of %s. Total amount due is %s Rs.";
	private static final String DN_CONTENT_PARAGRAPH_2 = "Please clear the dues as soon as possible to avoid penalties.";

	private static final String GUARDIAN_IN_HINDI = "अभिभावक";

	private static final String HINDI_DN_CONTENT_PARAGRAPH_1 = "%s, %s पुत्र/पुत्री की विद्यालय फीस %s/- बकाया है। कृपया फीस का भुगतान जल्दी करे जिससे की आपको जुर्माना नहीं भरना पड़े। ";
	private static final String HINDI_DN_CONTENT_PARAGRAPH_V2 = "श्री मान %s, आपके पुत्र /पुत्री %s की कुल %s/- विद्यालय फीस बकाया है। कृपया फीस का भुगतान जल्दी करे जिससे की आपको जुर्माना नहीं भरना पड़े।";
	private static final String HINDI_DN_CONTENT_PARAGRAPH_V3 = "सम्माननीय %s, \n विद्यार्थी %s कक्षा %s के शिक्षण शुल्क की राशि निम्रांकित बकाया है, कृपया शीघ्र जमा करवाने का श्रम करें। \n किस्त - %s \n राशि रु %s";
	// private static final String HINDI_FONT =
	// "/Users/<USER>/Lernen/kruti-dev-021.ttf";

	String a = "हेलो हम अच्छे है। %s Lekin ";

	@Override
	public DocumentOutput generateDemandNotices(Institute institute, List<StudentDueFeesData> dueFeeStudents,
												boolean includeFine, String documentName) {

		LoadItextLiscence();
		int instituteId = institute.getInstituteId();
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
			float contentFontSize = 12f;
			float headerFontSize = 15f;

			Document document = initDocument(invoice.getContent(), documentLayoutSetup);
			Image instituteLogo = generateImage1(document, documentLayoutSetup,
					LogoProvider.INSTANCE.getLogo(instituteId), 50f, 50f);

			int itemCount = 3;
			int counter = 1;
			PdfFont hindiFont = getHindiFont();
			for (StudentDueFeesData studentDueFeesData : dueFeeStudents) {

				generateHeader(document, documentLayoutSetup, institute, headerFontSize, instituteLogo, hindiFont);
				generateContent(document, documentLayoutSetup, contentFontSize, studentDueFeesData, institute,
						hindiFont, includeFine);
				generateSignatureBox(document, documentLayoutSetup, contentFontSize, hindiFont);
				document.add(new Paragraph("\n"));
				if (counter % itemCount == 0 && dueFeeStudents.size() != counter) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				counter++;
			}

			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public Image generateImage1(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
			float height) throws MalformedURLException {
		if (image == null) {
			return null;
		}
		Image img = new Image(ImageDataFactory.create(image));
		img.scaleToFit(width, height);
		return img;
//		document.add(img);
	}

	public void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute,
			float headerFontSize, Image instituteLogo, PdfFont hindiFont) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.15f, 0.7f, 0.15f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);

		Paragraph image = new Paragraph();
		image.add(instituteLogo);
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(image, cellLayoutSetup, 3, 1),
						new CellData(getParagraph(institute.getInstituteName()), cellLayoutSetup),
						new CellData(getParagraph(EMPTY_TEXT), cellLayoutSetup)));
//		addRow(table, documentLayoutSetup, Arrays.asList(image,
//				getParagraph(institute.getInstituteName()), getParagraph(EMPTY_TEXT)),
//				cellLayoutSetup.setFontSize(headerFontSize));
//		addRow(table, documentLayoutSetup, Arrays.asList(image,
//				getParagraph(institute.getInstituteName()), getParagraph(EMPTY_TEXT)),
//				cellLayoutSetup.setFontSize(headerFontSize));
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(institute.getLetterHeadLine1()), getParagraph(EMPTY_TEXT)),
				cellLayoutSetup.setFontSize(9f).setVerticalAlignment(VerticalAlignment.TOP));
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(institute.getLetterHeadLine2()), getParagraph(EMPTY_TEXT)), cellLayoutSetup);

		document.add(table);

		int singleContentColumn = 1;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("\n")), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getHindiBoldParagraph("सूचना पत्रक", hindiFont)),
				cellLayoutSetup.setFontSize(12f));
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("\n")), cellLayoutSetup);
		document.add(table);

	}

	private void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			StudentDueFeesData studentDueFeesData, Institute institute, PdfFont hindiFont, boolean includeFine) throws IOException {
		Student student = studentDueFeesData.getStudent();
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
//		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
//		float contentWidth = getContentWidth(documentLayoutSetup);
//		Table table = new Table(columnCount);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularFont(StandardFonts.TIMES_ROMAN)).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);

		String heShe = "He/She";
		String hisHer = "His/Her";
		if (student.getStudentBasicInfo().getGender() == Gender.FEMALE) {
			heShe = "She";
			hisHer = "Her";
		} else if (student.getStudentBasicInfo().getGender() == Gender.MALE) {
			heShe = "He";
			hisHer = "His";
		}
		Text t1 = new Text(String.format(DN_CONTENT_PARAGRAPH_1, student.getStudentBasicInfo().getName(),
				getSonDaughter(student.getStudentBasicInfo().getGender()),
				student.getStudentFamilyInfo().getFathersName(), includeFine ? studentDueFeesData.getTotalDueAmountWithFine() : studentDueFeesData.getTotalDueAmount()));
		Text t2 = new Text(String.format(DN_CONTENT_PARAGRAPH_2));

		String installmentsDue = "";
		String delimiter = "";
		for (StudentFeesLevelPaymentData studentFeesLevelPaymentData : studentDueFeesData
				.getStudentFeesLevelPaymentDataList()) {
			installmentsDue = installmentsDue + delimiter + studentFeesLevelPaymentData.getFeesName();
			delimiter = ", ";
		}
		Text t3 = new Text(String.format(HINDI_DN_CONTENT_PARAGRAPH_V3,
				StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName()) ? GUARDIAN_IN_HINDI
						: student.getStudentFamilyInfo().getFathersName().trim(),
				student.getStudentBasicInfo().getName(),
				student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName(), installmentsDue,
				String.valueOf(Math.round(includeFine ? studentDueFeesData.getTotalDueAmountWithFine() : studentDueFeesData.getTotalDueAmount()))));

//		Paragraph p1 = new Paragraph();
//		p1.add(t1);
//
//		Paragraph p2 = new Paragraph();
//		p2.add(t2);

		Paragraph p3 = new Paragraph();
		p3.add(t3);
		p3.setFont(hindiFont);

//		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p1, cellLayoutSetup)));
//		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p2, cellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(
						getHindiParagraph(String.format("दिनांक %s",
								DateUtils.getFormattedDate((int) (System.currentTimeMillis() / 1000l), DATE_FORMAT,
										User.DFAULT_TIMEZONE)),
								hindiFont),
						cellLayoutSetup.setTextAlignment(TextAlignment.RIGHT))));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(p3, cellLayoutSetup.setTextAlignment(TextAlignment.LEFT))));
//		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, cellLayoutSetup)));
//		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, cellLayoutSetup)));

		document.add(table);
	}

	private void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			PdfFont hindiFont) throws IOException {
		int singleContentColumn = 3;

		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(EMPTY_TEXT), getParagraph(EMPTY_TEXT), getParagraph(EMPTY_TEXT)),
				signatureCellLayoutSetup);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getHindiBoldParagraph("प्रधानाध्यपक", hindiFont),
						getHindiBoldParagraph("कक्षाध्यापक", hindiFont), getHindiBoldParagraph("अभिभावक", hindiFont)),
				signatureCellLayoutSetup);
		document.add(table);
	}

	private String getSonDaughter(Gender gender) {
		if (gender == Gender.FEMALE) {
			return "daughter";
		} else if (gender == Gender.MALE) {
			return "son";
		}
		return "son/daughter";

	}
}
