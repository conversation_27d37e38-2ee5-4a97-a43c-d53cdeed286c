package com.lernen.cloud.pdf.exam.reports._10001;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamCourseMarks;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.StudentExamMarksDetailsLite;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.lib.examination.ExamMarksUtils;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportGenerator9thTo10th10001 extends ExamReportGenerator10001 implements IExamReportCardGenerator {
	public ExamReportGenerator9thTo10th10001(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator9thTo10th10001.class);
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION_9th_TO_10th = "IA = Internal Assessment, TE = Term Exam";

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Image examControllerSignature = getExamControllerSignature();
			generateStudentReport(institute, examReportData.getStudentLite(), reportType, examReportData, examReportCardLayoutData, 1, examControllerSignature);

			examReportCardLayoutData.getDocument().close();

			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			sortClassExamReports(examReportDataList);
			Image examControllerSignature = getExamControllerSignature();
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReport(institute, examReportData.getStudentLite(), reportType, examReportData,
						examReportCardLayoutData, pageNumber, examControllerSignature);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			examReportCardLayoutData.getDocument().close();

			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;

	}

	private void generateStudentReport(Institute institute, StudentLite studentLite, String reportType,
			ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData, int pageNumber, Image examControllerSignature)
			throws IOException {
		addBlankLine(examReportCardLayoutData, true, 1);

		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.85f);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateStudentInformation(examReportCardLayoutData, studentLite);
		
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			addBlankLine(examReportCardLayoutData, true, 1);
		}
		
		addBlankLine(examReportCardLayoutData, false, 1);
		generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, false,
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));

//		addBlankLine(examReportCardLayoutData, false, 2);
//
//		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
//			generateFoundationOfITCourseMarksGrid(examReportCardLayoutData, examReportData, reportType);
//		}

//		addBlankLine(examReportCardLayoutData, false, 1);
		generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportData);

		addBlankLine(examReportCardLayoutData, false, 2);
		generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, false,
				"Additional Subjects", getScholasticMarksGridSubjectWidth(reportType));
		generateScholasticStarMarkDescription(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportData);

		addBlankLine(examReportCardLayoutData, false, 2);
		generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
				getCoScholasticMarksGridSubjectWidth(reportType));

		/*
		 * govt notice line only for session 19 for 10001
		 */
				
		if(examReportData.getStandardMetaData().getAcademicSessionId() == 19) {
			generateCoScholasticStarMarkDescription(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
					examReportData, examReportCardLayoutData);
		}
		
		if(examReportData.getStandardMetaData().getAcademicSessionId() != 19) {
			addBlankLine(examReportCardLayoutData, false, 1);
			generateStudentAttributeTable(examReportCardLayoutData, examReportData);
		}
		addBlankLine(examReportCardLayoutData, false, 2);

		generateResultSummary(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportData, reportType);

		addBlankLine(examReportCardLayoutData, false, 1);
		generateRemarksSection(examReportCardLayoutData, examReportData);
		generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examControllerSignature);

		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, String reportType, boolean addTotalRow, String subjectColumnTitle,
			float subjectColumnWidth) throws IOException {
		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjets(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), subjectColumnTitle,
				subjectColumnWidth, nonAdditionalSubjects, null);

	}

	private void generateFoundationOfITCourseMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, String reportType) throws IOException {
		String foundationOfITCourseName = "Foundation of IT*";
		String foundationOfITTheoryExamName = "FIT Theory";
		String foundationOfITPracticalExamName = "FIT Practical";
		ExamDimensionObtainedValues threoryMarks = null;
		ExamDimensionObtainedValues practicalMarks = null;

		List<StudentExamMarksDetailsLite> studentExamMarksDetailsLiteList = examReportData.getStudentExamMarksDetailsLiteList();
		for (StudentExamMarksDetailsLite studentExamMarksDetailsLite : studentExamMarksDetailsLiteList) {
			String examName = studentExamMarksDetailsLite.getExamMetaData().getExamName().trim();

			if (examName.equalsIgnoreCase(foundationOfITTheoryExamName)
					|| examName.equalsIgnoreCase(foundationOfITPracticalExamName)) {
				for (ExamCourseMarks examCourseMarks : studentExamMarksDetailsLite.getExamCoursesAllDimensionsMarks()) {
					if (examCourseMarks.getCourse().getCourseName().trim().equalsIgnoreCase(foundationOfITCourseName)) {
						for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
								.getExamDimensionObtainedValues()) {
							if (examDimensionObtainedValues.getExamDimension().isTotal()) {

								if (examName.equalsIgnoreCase(foundationOfITTheoryExamName)) {
									threoryMarks = examDimensionObtainedValues;
								} else if (examName.equalsIgnoreCase(foundationOfITPracticalExamName)) {
									practicalMarks = examDimensionObtainedValues;
								}

								break;
							}
						}
						break;
					}
				}
			}

		}

		if (threoryMarks == null || practicalMarks == null || threoryMarks.getMaxMarks() == null
				|| threoryMarks.getMaxMarks() <= 0d || practicalMarks.getMaxMarks() == null
				|| practicalMarks.getMaxMarks() <= 0d) {
			return;
		}

		Double totalMaxMarks = ExamMarksUtils.addValues(threoryMarks.getMaxMarks(), practicalMarks.getMaxMarks());
		Double totalObtainedMarks = ExamMarksUtils.addValues(threoryMarks.getObtainedMarks(),
				practicalMarks.getObtainedMarks());
		ExamGrade examGrade = null;
		if (totalMaxMarks != null && totalMaxMarks > 0d && totalObtainedMarks != null && totalObtainedMarks >= 0d) {
			examGrade = ExamMarksUtils.getExamGradeByPercentage(
					examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC),
					totalObtainedMarks / totalMaxMarks);
		}

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize();
		float subjectColumnWidth = getScholasticMarksGridSubjectWidth(reportType);
		float remainingColumnWidth = 1 - subjectColumnWidth;
		Table table = getPDFTable(documentLayoutSetup, new float[] { subjectColumnWidth, remainingColumnWidth * 0.25f,
				remainingColumnWidth * 0.25f, remainingColumnWidth * 0.25f, remainingColumnWidth * 0.25f });

		PdfFont boldFont = getRegularBoldFont();

		CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
		headerCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 2)
				.setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth()))
				.setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth()))
				.setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
				.setTextAlignment(TextAlignment.LEFT);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("Additional Subject",
						headerCellLayoutSetup.copy().setFontSize(contentFontSize), 3, 1),
						new CellData("Term II (100)", headerCellLayoutSetup, 1, 4)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("Theory", headerCellLayoutSetup),
						new CellData("Practical", headerCellLayoutSetup), new CellData("Total", headerCellLayoutSetup),
						new CellData("Grade", headerCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("(" + Math.round(threoryMarks.getMaxMarks()) + ")", headerCellLayoutSetup),
						new CellData("(" + Math.round(practicalMarks.getMaxMarks()) + ")", headerCellLayoutSetup),
						new CellData("(" + Math.round(totalMaxMarks) + ")", headerCellLayoutSetup),
						new CellData(EMPTY_TEXT, headerCellLayoutSetup)));

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(foundationOfITCourseName, courseCellLayoutSetup),
				new CellData(threoryMarks.getObtainedMarks() == null || threoryMarks.getObtainedMarks() < 0d ? "-"
						: String.valueOf(Math.round(threoryMarks.getObtainedMarks())), marksCellLayoutSetup),
				new CellData(practicalMarks.getObtainedMarks() == null || practicalMarks.getObtainedMarks() < 0d ? "-"
						: String.valueOf(Math.round(practicalMarks.getObtainedMarks())), marksCellLayoutSetup),
				new CellData(totalObtainedMarks == null || totalObtainedMarks < 0d ? "-"
						: String.valueOf(Math.round(totalObtainedMarks)), marksCellLayoutSetup),
				new CellData(examGrade == null || StringUtils.isBlank(examGrade.getGradeName()) ? "-"
						: examGrade.getGradeName(), marksCellLayoutSetup)));

		document.add(table);
	}

	protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
													 float contentFontSize, ExamReportData examReportData) throws IOException {
		Text descTitle = new Text("Description:\n").setFont(getRegularBoldFont()).setFontSize(contentFontSize);

		Text descText = new Text(SCHOLASTIC_EXAM_DESCRIPTION_9th_TO_10th)
				.setFontSize(contentFontSize - 2);
		Paragraph desc = new Paragraph();
		desc.add(descTitle).add(descText);
		document.add(desc);
	}

}
