package com.lernen.cloud.pdf.admitcard;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.ExamAdmitCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.DatesheetDetailRow;
import com.lernen.cloud.core.api.examination.DatesheetDetails;
import com.lernen.cloud.core.api.examination.ExamDetails;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public class AdmitCardWithDatesheetGenerator10135 extends GlobalAdmitCardWithDatesheetGenerator {

	public AdmitCardWithDatesheetGenerator10135(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(AdmitCardWithDatesheetGenerator10135.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 30f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 20f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 20f;
	public static final float LOGO_WIDTH = 75f;
	public static final float LOGO_HEIGHT = 75f;
	public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;

	public static final String INSTRUCTION_HEADING = "Instructions";
	public static final String INSTRUCTION_DETAILS_1 = "1. All students are directed to be on time for the examination.";
	public static final String INSTRUCTION_DETAILS_2 = "2. Students should bring their own pencil, pen, scale, eraser etc  necessary for the exam.";
	public static final String INSTRUCTION_DETAILS_3 = "3. Students are required to produce admit card and I.D card when asked for.";
	public static final String INSTRUCTION_DETAILS_4 = "4. No students will be allowed to leave the exam hall without submitting their Answer scripts; make sure it's signed and submitted.";
	public static final String INSTRUCTION_DETAILS_5 = "5. A student shall not help or try to help any other student nor try to be helped by any other students or person during the exam.";
	public static final String INSTRUCTION_DETAILS_6 = "6. Unfair means in any form shall forfeit the right of the student to continue the exam thereby will be expelled  from school.";

	@Override
	public DocumentOutput generateAdmitCard(Institute institute, List<Student> students, ExamDetails examDetails,
											Map<Integer, List<DatesheetDetailRow>> datesheetDetailsMap,
											Map<UUID, Set<UUID>> studentCourseMap,
											String documentName, ExamAdmitCardPreferences examAdmitCardPreferences,
											StudentManager studentManager, DatesheetDetails datesheetDetails) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4, DEFAULT_PAGE_TOP_MARGIN,
					DEFAULT_PAGE_BOTTOM_MARGIN, DEFAULT_PAGE_SIDE_MARGIN);
			float contentFontSize = 10.5f;
			float defaultBorderWidth = DEFAULT_TABLE_BORDER_WIDTH;
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			int instituteId = institute.getInstituteId();
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup,
					null, null, contentFontSize, defaultBorderWidth,
					LOGO_WIDTH, LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(instituteId),
					null, null);

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			String notes = datesheetDetails == null ? null : datesheetDetails.getNotes();

			int studentCount = 0;
			int pageNumber = 1;
			boolean addWaterMark = true;
			for (Student student : students) {
				generateAdmitCards(document, documentLayoutData, cambriaBoldFont, cambriaFont,
						documentLayoutSetup, institute, examDetails, datesheetDetailsMap, student,
						studentManager, addWaterMark, contentFontSize, notes);
				studentCount++;
				addWaterMark = false;
				if(studentCount < students.size() && studentCount % 2 == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					addWaterMark = true;
					pageNumber++;
				} else {
					PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
					canvas.moveTo(12, documentLayoutSetup.getPageSize().getHeight() / 2);
					canvas.lineTo(583, documentLayoutSetup.getPageSize().getHeight() / 2);
					canvas.setLineWidth(1f);
					canvas.closePathStroke();
					addBlankLine(document, false, 2);
				}
			}

			documentLayoutData.getDocument().close();

			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating admit cards for institute {}, exam id {}", institute.getInstituteId(),
					examDetails.getExamMetaData().getExamId(), e);
		}
		return null;
	}

	private void generateAdmitCards(Document document, DocumentLayoutData documentLayoutData,
									PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
									Institute institute, ExamDetails examDetails,
									Map<Integer, List<DatesheetDetailRow>> datesheetDetailsMap,
									Student student, StudentManager studentManager,
									boolean addWaterMark, float contentFontSize, String notes) throws IOException {

		/**
		 * Watermark
		 */
		float watermarkImageHeightWidth = 300f;
		float pageHeight = documentLayoutSetup.getPageSize().getHeight();
		if(addWaterMark) {
			generateWatermark(documentLayoutData, institute.getInstituteId(), watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth - 150,
					(watermarkImageHeightWidth - 240) + (pageHeight / 2));

			generateWatermark(documentLayoutData, institute.getInstituteId(), watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth - 150,
					(watermarkImageHeightWidth - 240));
		}


		float yOffSet = documentLayoutSetup.getPageSize().getHeight() - LOGO_HEIGHT - 15f - (LOGO_HEIGHT / 2);

		/**
		 * We are not adding WaterMark for second student in a page,
		 * so changing yOffset for those
		 */
		if(!addWaterMark) {
			yOffSet = (documentLayoutSetup.getPageSize().getHeight() / 2) - LOGO_HEIGHT - (LOGO_HEIGHT / 2);
		}
		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		if (image != null) {
			generateImage(document, documentLayoutSetup, image, 80, 90,
					documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f, yOffSet);
		}

		/**
		 * Header
		 */
		generateHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute, addWaterMark, contentFontSize - 1);

		/**
		 * Student Details
		 */
		generateStudentDetails(document, documentLayoutData, documentLayoutSetup, cambriaFont, cambriaBoldFont, student, examDetails,
				contentFontSize - 1);

		/**
		 * Basic Datesheet Details
		 */
		generateBasicDatesheetDetails(document, documentLayoutData, documentLayoutSetup, cambriaFont, cambriaBoldFont,
				datesheetDetailsMap, contentFontSize);

		/**
		 * Signature Details
		 */
		generateSignatureDetails(document, documentLayoutSetup, documentLayoutData, cambriaBoldFont, institute, contentFontSize);

		/**
		 * Notes
		 */
		if(!StringUtils.isBlank(notes)) {
			generateNoteDetails(document, documentLayoutSetup, documentLayoutData, cambriaFont, cambriaBoldFont, notes);
		} else {
			generateInstruction(document, documentLayoutSetup, documentLayoutData, cambriaBoldFont, cambriaFont, contentFontSize);
		}

	}

	protected void generateSignatureDetails(Document document, DocumentLayoutSetup documentLayoutSetup,
											DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont,
											Institute institute, float contentFontSize) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize - 1);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

		List<Integer> signatureFontColor = EColorUtils.hex2Rgb("#474747");

		byte[] examInchargeSignature = ImageProvider.INSTANCE.getImage(ImageProvider._10135_EXAM_INCHARGE_SIGNATURE);
		Image examInchargeSignatureImage = new Image(ImageDataFactory.create(examInchargeSignature));
		examInchargeSignatureImage.setHeight(30f);
		examInchargeSignatureImage.setWidth(70f);

		Cell examInchargeSignatureCellImage = new Cell();
		examInchargeSignatureCellImage.add(examInchargeSignatureImage);
		examInchargeSignatureCellImage.setBorder(null);
		table.addCell(examInchargeSignatureCellImage);

		Cell emptySignatureCell = new Cell();
		emptySignatureCell.setBorder(null);
		table.addCell(emptySignatureCell);


		byte[] principalSignature = ImageProvider.INSTANCE.getImage(ImageProvider._10135_PRINCIPAL_SIGNATURE);
		Image principalSignatureImage = new Image(ImageDataFactory.create(principalSignature));
		principalSignatureImage.setHeight(30f);
		principalSignatureImage.setWidth(70f);
		principalSignatureImage.setHorizontalAlignment(HorizontalAlignment.RIGHT);

		Cell principalSignatureCellImage = new Cell();
		principalSignatureCellImage.setHorizontalAlignment(HorizontalAlignment.RIGHT);
		principalSignatureCellImage.add(principalSignatureImage);
		principalSignatureCellImage.setBorder(null);
		table.addCell(principalSignatureCellImage);

		String leftSideSignatureText = "Examination Incharge's Signature";
		Paragraph classTeacherSignatureText = getParagraph(leftSideSignatureText, signatureFontColor, cambriaBoldFont);
		Paragraph principalSignatureText = getParagraph("Principal's Signature", signatureFontColor, cambriaBoldFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(classTeacherSignatureText, firstCellLayoutSetup),
						new CellData("", secondCellLayoutSetup),
						new CellData(principalSignatureText, thirdCellLayoutSetup)));

		document.add(table);

	}

	private void generateInstruction(Document document, DocumentLayoutSetup documentLayoutSetup,
									 DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
									 float contentFontSize) {

		List<Integer> color = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(contentFontSize - 2.5f).setTextAlignment(TextAlignment.LEFT);
		addRow(table, documentLayoutSetup, Arrays.asList(
				getParagraph(INSTRUCTION_HEADING, color)), cellLayoutSetup.copy().setPdfFont(cambriaBoldFont)
				.setTextAlignment(TextAlignment.LEFT));
		document.add(table);

		table = getPDFTable(documentLayoutSetup, 1);
		addRow(table, documentLayoutSetup, Arrays.asList(
				getParagraph(INSTRUCTION_DETAILS_1, color)), cellLayoutSetup.copy());
		addRow(table, documentLayoutSetup, Arrays.asList(
				getParagraph(INSTRUCTION_DETAILS_2, color)), cellLayoutSetup.copy());
		addRow(table, documentLayoutSetup, Arrays.asList(
				getParagraph(INSTRUCTION_DETAILS_3, color)), cellLayoutSetup.copy());
		addRow(table, documentLayoutSetup, Arrays.asList(
				getParagraph(INSTRUCTION_DETAILS_4, color)), cellLayoutSetup.copy());
		addRow(table, documentLayoutSetup, Arrays.asList(
				getParagraph(INSTRUCTION_DETAILS_5, color)), cellLayoutSetup.copy());
		addRow(table, documentLayoutSetup, Arrays.asList(
				getParagraph(INSTRUCTION_DETAILS_6, color)), cellLayoutSetup.copy());
		document.add(table);

	}

	protected void generateBasicDatesheetDetails(Document document, DocumentLayoutData documentLayoutData,
										  DocumentLayoutSetup documentLayoutSetup, PdfFont cambriaFont,
										  PdfFont cambriaBoldFont, Map<Integer, List<DatesheetDetailRow>> datesheetDetailsMap,
												 float contentFontSize) {
		String examStartTimeStr = "";
		String examEndTimeStr = "";
		String timingNameStr = "";

		if(datesheetDetailsMap != null && !CollectionUtils.isEmpty(datesheetDetailsMap.entrySet())) {
			List<DatesheetDetailRow> value = datesheetDetailsMap.entrySet().iterator().next().getValue();
			if(!CollectionUtils.isEmpty(value)) {
				DatesheetDetailRow datesheetDetailRow = value.get(0);
				if(datesheetDetailRow != null && datesheetDetailRow.getDatesheetID() != null) {
					Integer examStartTime = datesheetDetailRow.getExamStartDate();
					Integer examEndTime = datesheetDetailRow.getExamEndDate();
					String timingName = DateUtils.getFormattedAmPmTime(datesheetDetailRow.getStartTime(), false) + " - " +
							DateUtils.getFormattedAmPmTime(datesheetDetailRow.getEndTime(), false);

					examStartTimeStr = DateUtils.getFormattedDate(examStartTime);
					examEndTimeStr = DateUtils.getFormattedDate(examEndTime);
					timingNameStr = timingName;
				}
			}

		}

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

		List<Integer> keyFontColor = EColorUtils.hex2Rgb("#434343");
		List<Integer> valueFontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);

		Paragraph examStartDate = getKeyValueParagraph("Exam Start Date : ", examStartTimeStr,
				keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
		Paragraph examEndDate = getKeyValueParagraph("Exam End Date : ", examEndTimeStr,
				keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);
		Paragraph examTiming = getKeyValueParagraph("Exam Timing : ", timingNameStr,
				keyFontColor, valueFontColor, cambriaBoldFont, cambriaBoldFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(examStartDate, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
						new CellData(examEndDate, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(examTiming, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
						new CellData("", thirdCellLayoutSetup)));

		document.add(table);

//		addBlankLine(document, false, 1);
	}

	protected void generateHeader(Document document, DocumentLayoutData documentLayoutData,
								  PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
								  Institute institute, boolean addWaterMark,
								  float contentFontSize) throws IOException {

		float yOffSet = documentLayoutSetup.getPageSize().getHeight() - LOGO_HEIGHT - 25f;
		/**
		 * We are not adding WaterMark for second student in a page,
		 * so changing yOffset for those
		 */
		if(!addWaterMark) {
			yOffSet = (documentLayoutSetup.getPageSize().getHeight() / 2) - LOGO_HEIGHT - 5f;
		}
		generateDynamicImageProvider(documentLayoutData, 30f, yOffSet, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setTextAlignment(TextAlignment.CENTER).setPaddingLeft(25f);

		List<Integer> instituteNameFontColor = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase(), instituteNameFontColor)),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 4).setPdfFont(cambriaBoldFont));

		List<Integer> letterHead1FontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1(), letterHead1FontColor)),
				cellLayoutSetup.copy().setFontSize(contentFontSize - 1).setPdfFont(cambriaFont));

		List<Integer> letterHead2FontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2(), letterHead2FontColor)),
				cellLayoutSetup.copy().setFontSize(contentFontSize - 1).setPdfFont(cambriaFont));

		documentLayoutData.getDocument().add(table);

		addBlankLine(document, false, 1);

		generateAdmitCardText(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute);
	}

	protected void generateAdmitCardText(Document document, DocumentLayoutData documentLayoutData,
										 PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
										 Institute institute) throws IOException {

		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setTextAlignment(TextAlignment.CENTER);

		List<Integer> admitCardTextFontColor = EColorUtils.hex2Rgb("#474747");
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("ADMIT CARD", admitCardTextFontColor)),
				cellLayoutSetup.copy().setFontSize(14f).setPdfFont(cambriaBoldFont));

		documentLayoutData.getDocument().add(table);

		addBlankLine(document, false, 1);
	}
}
