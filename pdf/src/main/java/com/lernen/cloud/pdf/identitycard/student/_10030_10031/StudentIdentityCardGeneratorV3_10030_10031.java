package com.lernen.cloud.pdf.identitycard.student._10030_10031;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.AddressUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.identitycard.student.GlobalStudentIdentityCardGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */

public class StudentIdentityCardGeneratorV3_10030_10031 extends GlobalStudentIdentityCardGenerator {

	public StudentIdentityCardGeneratorV3_10030_10031(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StudentIdentityCardGeneratorV3_10030_10031.class);
	private static final String INSTITUTE_NAME_PREFIX_1 = "ADARSH VIDYA";
	private static final String INSTITUTE_NAME_SUFFIX_1 = "MANDIR";
	private static final String INSTITUTE_NAME_PREFIX_2 = "ADARSH INTER";
	private static final String INSTITUTE_NAME_SUFFIX_2 = "COLLEGE";
	private static final String INSTITUTE_ADDRESS = "Piyawali, Gautam Buddh Nagar";
	private static final String INSTITUTE_CONTACT_NUMBER = "Mo. - **********, **********";
	private static final String INSTITUTE_EMAIL = "Email - <EMAIL>";
//	private static final String ICARD_COLOR = EColorUtils.LIGHT_RED_COLOR2_HEX_CODE;
	private static final String _10030_ICARD_COLOR_V4 = "#dea126";
	private static final String _10031_ICARD_COLOR_V4 = "#3c60cf";


	@Override
	public DocumentOutput generateIdentityCard(StudentManager studentManager, Institute institute, StudentTransportDetails studentTransportDetails, StudentIdentityCardPreferences studentIdentityCardPreferences, Student student, String documentName, StaffManager staffManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont, documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, 1, studentIdentityCardPreferences);
			document.close();
			return getA4PortraitIdentityCard(documentName, documentOutput, PageSize.A4, 3, 3, 20f);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating identity cards for institute {}, student {}", institute.getInstituteId(), student.getStudentId(), e);
		}
		return null;
	}

	private void generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student, StudentManager studentManager, int pageNumber, StudentIdentityCardPreferences studentIdentityCardPreferences) throws IOException {

		/**
		 * Bottom bar, keep this on top as we are
		 * using canvas in generateInstituteHeader which
		 * require document to be created before it,
		 * ow error occurred
		 */
		generateBottomBar(document, cambriaBoldFont, documentLayoutSetup, studentIdentityCardPreferences);

		generateBackgroundCanvas(institute.getInstituteId(), document, documentLayoutSetup, pageNumber);

		/**
		 * Institute Header
		 */
		generateInstituteDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont, documentLayoutSetup, institute);


		generateSessionDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont, documentLayoutSetup, institute, student);

		generateStudentImage(document, documentLayoutSetup, institute, studentManager, student);


		/**
		 * Student basic details
		 */
		generateStudentBasicDetails(document, cambriaBoldFont, cambriaFont, documentLayoutSetup, student);


		generateBarCodeImage(document, documentLayoutSetup);
	}

	private void generateBackgroundCanvas(int instituteId, Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {

		generateBackgroundTriangle(instituteId, document, documentLayoutSetup, pageNumber);

		generateImageBackground(instituteId, document, documentLayoutSetup, pageNumber);

	}

	private void generateImageBackground(int instituteId, Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {

		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		Color color = WebColors.getRGBColor(instituteId == 10030 ? _10030_ICARD_COLOR_V4 : _10031_ICARD_COLOR_V4);
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);

		canvas.moveTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.68)), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.30)), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.30)), documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2)));
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.68)), documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2)));
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
				(documentLayoutSetup.getPageSize().getWidth() * (0.68)), documentLayoutSetup.getPageSize().getHeight());

		canvas.setLineWidth(5f);
		canvas.fill();
		canvas.closePathStroke();
	}

	private void generateBackgroundTriangle(int instituteId, Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);

		Color color = WebColors.getRGBColor(instituteId == 10030 ? _10030_ICARD_COLOR_V4 : _10031_ICARD_COLOR_V4);
		canvas.setFillColor(color);
		canvas.setStrokeColor(color);

		canvas.moveTo(0, documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight());
		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight()
				- (documentLayoutSetup.getPageSize().getHeight() / 4));
		canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight()
				- ((documentLayoutSetup.getPageSize().getHeight() / 2) - 10));
		canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight());
		canvas.setLineWidth(5f);
		canvas.fill();
		canvas.closePathStroke();
	}
	private void generateBarCodeImage(Document document, DocumentLayoutSetup documentLayoutSetup) throws IOException {

		byte[] image = ImageProvider.INSTANCE.getImage(ImageProvider._10030_10031_STUDENT_ICARD_BARCODE);
		if (image == null) {
			return;
		}

		float imageWidth = documentLayoutSetup.getPageSize().getWidth() - 20f;
		float imageHeight = 10f;

		float imageOffsetX = 10f;
		float imageOffsetY = 33f;

		generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight, imageOffsetX, imageOffsetY);

	}


	protected void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width, float height, float offsetX, float offsetY) throws IOException {
		try {
			generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
		} catch (Exception e) {
			logger.error("Exception while adding background image", e);
		}

	}

	private void generateStudentBasicDetails(Document document, PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Student student) {

		Table table = getPDFTable(documentLayoutSetup, 1);

		float subToTop = 0;

		String fatherName = student.getStudentFamilyInfo() == null ? "" : student.getStudentFamilyInfo().getFathersName();
		String motherName = student.getStudentFamilyInfo() == null ? "" : student.getStudentFamilyInfo().getMothersName();

		String address = AddressUtils.getStudentAddress(student);
		if (isTextGreater(address, 25)) {
			subToTop += 11;
		}

		if (isTextGreater(fatherName, 25)) {
			subToTop += 11;
		}

		table.setFixedPosition(0, 54 - subToTop, documentLayoutSetup.getPageSize().getWidth());


		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(7f).setTextAlignment(TextAlignment.CENTER).setPaddingTop(0f).setPaddingBottom(0f);

		List<Integer> rgbBlack = EColorUtils.hex2Rgb(EColorUtils.BLACK_COLOR_HEX_CODE);

		String studentNameString = student.getStudentBasicInfo().getName().toUpperCase();
		Paragraph studentNameParagraph = getParagraph(studentNameString, rgbBlack);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentNameParagraph, cellLayoutSetup.copy().setFontSize(10f).setPdfFont(cambriaBoldFont))));


		cellLayoutSetup.setFontSize(6.5f);
		List<Integer> keyLightBlueColor = EColorUtils.hex2Rgb(EColorUtils.LIGHT_BLUE_COLOR_HEX_CODE);


		cellLayoutSetup.setPaddingLeft(0f).setTextAlignment(TextAlignment.LEFT).setPaddingLeft(15f).setPaddingRight(15f);

		Paragraph classKeyValue = getKeyValueParagraph("Class: ", student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(), keyLightBlueColor, rgbBlack, cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(classKeyValue, cellLayoutSetup.copy())));

		Paragraph admissionNumberKeyValue = getKeyValueParagraph("Admission No.: ", student.getStudentBasicInfo().getAdmissionNumber(), keyLightBlueColor, rgbBlack, cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(admissionNumberKeyValue, cellLayoutSetup.copy())));

		Paragraph fatherNameKeyValue = getKeyValueParagraph("Father’s Name: ", student.getStudentFamilyInfo().getFathersName(), keyLightBlueColor, rgbBlack, cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(fatherNameKeyValue, cellLayoutSetup.copy())));

		Paragraph phoneNumberKeyValue = getKeyValueParagraph("Contact No.: ", student.getStudentBasicInfo().getPrimaryContactNumber(), keyLightBlueColor, rgbBlack, cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(phoneNumberKeyValue, cellLayoutSetup.copy())));

		Paragraph addressKeyValue = getKeyValueParagraph("Address: ", address, keyLightBlueColor, rgbBlack, cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(addressKeyValue, cellLayoutSetup.copy())));

		document.add(table);
	}

	protected void generateBottomBar(int instituteId, Document document, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup, StudentIdentityCardPreferences studentIdentityCardPreferences) {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup bottomBarCellLayoutSetup = new CellLayoutSetup();
		bottomBarCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(9f).setPaddingTop(0f).setPaddingBottom(0f);

		bottomBarCellLayoutSetup.setBackgroundColor(instituteId == 10030 ? _10030_ICARD_COLOR_V4 : _10031_ICARD_COLOR_V4);

		List<Integer> rgb = EColorUtils.hex2Rgb(EColorUtils.WHITE_COLOR_HEX_CODE);

		table.setFixedPosition(0f, 0f, documentLayoutSetup.getPageSize().getWidth());
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(INSTITUTE_CONTACT_NUMBER, rgb)), bottomBarCellLayoutSetup.copy().setPaddingTop(1f));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(INSTITUTE_EMAIL, rgb)), bottomBarCellLayoutSetup.copy().setPaddingBottom(1f));
		document.add(table);
	}


	private void generateSessionDetails(Document document, DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student) {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
		centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER).setPaddingBottom(0f).setPaddingTop(0f);


		List<Integer> whiteColor = EColorUtils.hex2Rgb(EColorUtils.WHITE_COLOR_HEX_CODE);

		Float rgbInstituteNameFontSize = 1f;

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Session- " + student.getStudentAcademicSessionInfoResponse().getAcademicSession().getShortYearDisplayName(), whiteColor)), centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize).setPaddingTop(2f).setPaddingBottom(1f).setFontSize(7f).setPaddingLeft(2f));
		document.add(table);
	}

	private void generateInstituteDetails(Document document, DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute) throws IOException {
//		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), 1);

//		Color color = WebColors.getRGBColor(ICARD_COLOR);
//		canvas.setFillColor(color);
//		canvas.setStrokeColor(color);
//
//		canvas.moveTo(0, documentLayoutSetup.getPageSize().getHeight());
//		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight());
//		canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight() - (documentLayoutSetup.getPageSize().getHeight() / 4));
//		canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight() - ((documentLayoutSetup.getPageSize().getHeight() / 2) - 10));
//		canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight());
//		canvas.setLineWidth(5f);
//		canvas.fill();
//		canvas.closePathStroke();

		int instituteId = institute.getInstituteId();
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
		centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT).setPaddingLeft(30f).setPaddingBottom(0f).setPaddingTop(0f);

		centerCellLayoutSetup.setBackgroundColor(instituteId == 10030 ? _10030_ICARD_COLOR_V4 : _10031_ICARD_COLOR_V4);

		List<Integer> whiteColor = EColorUtils.hex2Rgb(EColorUtils.WHITE_COLOR_HEX_CODE);

		List<Integer> rgb = EColorUtils.hex2Rgb(EColorUtils.WHITE_COLOR_HEX_CODE);
		List<Integer> lightBlackColor = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);

		Float rgbInstituteNameFontSize = 11f;
		Float rgbLetterHead1FontSize = 8f;


		if (institute.getInstituteId() == 10030) {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(INSTITUTE_NAME_PREFIX_1.toUpperCase(), rgb)), centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize).setPaddingLeft(35f).setPaddingTop(2f));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(INSTITUTE_NAME_SUFFIX_1.toUpperCase(), rgb)), centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize).setPaddingLeft(35f));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(INSTITUTE_ADDRESS, rgb)), centerCellLayoutSetup.copy().setFontSize(7f).setPaddingLeft(35f));
		} else if (institute.getInstituteId() == 10031) {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(INSTITUTE_NAME_PREFIX_2.toUpperCase(), rgb)), centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize).setPaddingLeft(35f).setPaddingTop(2f));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(INSTITUTE_NAME_SUFFIX_2.toUpperCase(), rgb)), centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize).setPaddingLeft(35f));
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph(INSTITUTE_ADDRESS, rgb)), centerCellLayoutSetup.copy().setFontSize(7f).setPaddingLeft(35f));
		}
		document.add(table);
		byte[] image = (institute.getInstituteId() == 10030) ? ImageProvider.INSTANCE.getImage(ImageProvider._10030_LOGO_WITH_WHITE_CIRCLE_BG) : ImageProvider.INSTANCE.getImage(ImageProvider._10031_LOGO_WITH_WHITE_CIRCLE_BG);

		generateLogo(document, documentLayoutSetup, image, 25f, 25f, -5, 10, 1f, 1f);


	}

	private void generateStudentImage(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, StudentManager studentManager, Student student) throws MalformedURLException, IOException {

		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		if (image == null) {
			image = ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME);
		}

		float imageWidth = (documentLayoutSetup.getPageSize().getWidth() / 3);
		float imageHeight = (documentLayoutSetup.getPageSize().getWidth() / 3) + 2f;
		float imageOffsetX = (documentLayoutSetup.getPageSize().getWidth() / 3) + 1.6f;
		float imageOffsetY = (documentLayoutSetup.getPageSize().getHeight() / 2) + 3f;

//		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), 1);
//
//		Color color = WebColors.getRGBColor(ICARD_COLOR);
//		canvas.setFillColor(color);
//		canvas.setStrokeColor(color);
//
//
//		canvas.moveTo(imageOffsetX-1f, imageOffsetY-1f);
//		canvas.lineTo(imageOffsetX + imageWidth+1f, imageOffsetY-1f);
//		canvas.lineTo(imageOffsetX + imageWidth+1f, imageOffsetY + imageHeight - 10-1f);
//		canvas.lineTo(imageOffsetX-1f, imageOffsetY + imageHeight - 20-1f);
//		canvas.lineTo(imageOffsetX-1f, imageOffsetY-1f);
//		canvas.setLineWidth(5f);
//		canvas.fill();
//		canvas.closePathStroke();

		generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight, imageOffsetX, imageOffsetY);


	}


	protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute, DocumentOutput documentOutput) throws IOException {

		/**
		 * 	aadhar card size - 8.5cmx5.5cm
		 * 	in inches - 3.34646x2.16535
		 * 	1 inch  = 72 points
		 * 	3.34646*72 & 2.16535*72
		 * 	240.94512f X 155.9052f
		 */
		PageSize pageSize = new PageSize(156f, 241f);
		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
		float contentFontSize = 9f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		float logoWidth = 30f;
		float logoHeight = 30f;

		int instituteId = institute.getInstituteId();
		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(), getRegularBoldFont(), contentFontSize, 0f, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId), null, null);

		documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
		return documentLayoutData;
	}

	public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
		return initDocumentLayoutSetup(false, defaultPageSize, 0f, 0f, 0f, 0f);
	}

	@Override
	public DocumentOutput generateIdentityCards(StudentManager studentManager, Institute institute, List<StudentTransportDetails> studentTransportDetails, StudentIdentityCardPreferences studentIdentityCardPreferences, List<Student> students, String documentName, StaffManager staffManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			int pageNumber = 1;
			for (Student student : students) {

				generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont, documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, pageNumber, studentIdentityCardPreferences);

				if (pageNumber != students.size()) {
					documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			documentLayoutData.getDocument().close();

			return getA4PortraitIdentityCard(documentName, documentOutput, PageSize.A4, 3, 3, 20f);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating student identity card institute {}", institute.getInstituteId(), e);
		}
		return null;
	}
}
