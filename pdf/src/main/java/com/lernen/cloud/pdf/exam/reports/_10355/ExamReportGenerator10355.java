package com.lernen.cloud.pdf.exam.reports._10355;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridHeaderConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamReportGenerator10355 extends ExamReportGenerator implements IExamReportCardGenerator {
	public ExamReportGenerator10355(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10355.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 45f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";

	private static final String DISTINCTION = "DISTINCTION";
	private static final String DIVISION_I = "I";
	private static final String DIVISION_II = "II";
	private static final String DIVISION_III = "III";
	private static final String DIVISION_IV = "IV";

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
										 ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			generateStudentReport(institute, examReportData.getStudentLite(), studentManager, reportType,
					examReportData, examReportCardLayoutData, 1);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateStudentReport(Institute institute, StudentLite studentLite, StudentManager studentManager,
									   String reportType, ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData,
									   Integer pageNumber) throws IOException {

		addBlankLine(examReportCardLayoutData, false, 12);
		generateMetaDataLayout(examReportCardLayoutData, institute, studentLite, studentManager, reportType,
				pageNumber, examReportData);

		addBlankLine(examReportCardLayoutData, false, 2);

		GridConfigs gridConfigs = new GridConfigs(new GridHeaderConfigs(false), new GridTotalMarksRowConfigs(
				true, false, true, false, true,false, false,
				false, true, false, false, "Total", "", "Percentage", "Test Date",
				"CUML Attendance", "Result Date", "Division", "Rank/Total Students", "Result"), false);

		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs, "Scholastic Subjects",
				getScholasticMarksGridSubjectWidth(reportType), null, null);

		addBlankLine(examReportCardLayoutData, false, 2);
		generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forOnlyObtainedTotalRow(), "Additional Subjects",
				getCoScholasticMarksGridSubjectWidth(reportType));
		addBlankLine(examReportCardLayoutData, false, 1);
		generateResultSummary(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize()-1, examReportData, reportType,
				examReportCardLayoutData.getBoldFont(), examReportCardLayoutData.getBoldFont());

		generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize()-1, examReportCardLayoutData);

	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.2f;
		}
		return 0.17f;

	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.2f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.17f;
		}
		return 0.3f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = 12f;
		float defaultBorderWidth = 0.8f;
		// Document document = initDocument(
		// new FileInputStream(new
		// File("/Users/<USER>/Lernen/outputPdfHTML/Fancy_Decoration_Border.pdf")),
		// report.getContent(), documentLayoutSetup);
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		float logoWidth = 110f;
		float logoHeight = 110f;

		ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup,
				getRegularFont(), getRegularBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
				LogoProvider.INSTANCE.getLogo(institute.getInstituteId()));

		return examReportCardLayoutData;
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
										  StudentLite studentLite, StudentManager studentManager, String reportType, Integer pageNumber, ExamReportData examReportData)
			throws IOException {

		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f);
		addBlankLine(examReportCardLayoutData, false, 2);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData);
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
								  Institute institute, String reportType, float offsetX, float offsetY) throws IOException {

		//generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(examReportCardLayoutData.getBoldFont()).setFontSize(14f)
				.setTextAlignment(TextAlignment.CENTER);


		addBlankLine(examReportCardLayoutData, true, 1);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(),
				Arrays.asList(getParagraph("")), cellLayoutSetup.copy().setFontSize(25f));

		addBlankLine(examReportCardLayoutData, false, 1);


		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
		String headerExamTitle = "Half Yearly Progress Report";
		if(reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE))
		{
			headerExamTitle = "Academic Session: ";
		}
		headerExamTitle += studentLite.getStudentSessionData().getShortYearDisplayName();

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
				cellLayoutSetup.copy().setFontSize(15f));

		examReportCardLayoutData.getDocument().add(table);

	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
											  StudentLite studentLite, ExamReportData examReportData) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize();

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.20f, 0.45f, 0.20f, 0.15f});
		Table HeadingTable = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(examReportCardLayoutData.getRegularFont()).setFontSize(contentFontSize-1).setBorder(new SolidBorder(0.8f));

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

		String dobValue = studentLite.getDateOfBirth() == null ?
				"" : DateUtils.getFormattedDate(studentLite.getDateOfBirth(),
				"dd.MM.yyyy", User.DFAULT_TIMEZONE);

		String rollNumber = StringUtils.isEmpty(studentLite.getStudentSessionData().getRollNumber()) ? "" : studentLite.getStudentSessionData().getRollNumber();

		String classValue = StringUtils.isEmpty(studentLite.getStudentSessionData().getStandardNameWithSection()) ? "" : studentLite.getStudentSessionData().getStandardNameWithSection();

		addRow(HeadingTable, documentLayoutSetup, Arrays.asList(getParagraph("Student Profile").setUnderline()), firstCellLayoutSetup.copy().setBorderBottom(Border.NO_BORDER).setPdfFont(examReportCardLayoutData.getBoldFont()));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(getParagraph("Student's Name"), secondCellLayoutSetup.copy().setPdfFont(examReportCardLayoutData.getBoldFont())),
						new CellData(getParagraph(studentLite.getName()), secondCellLayoutSetup),
						new CellData(getParagraph("Roll No"), secondCellLayoutSetup.copy().setPdfFont(examReportCardLayoutData.getBoldFont())),
						new CellData(getParagraph(rollNumber), secondCellLayoutSetup)
				));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(getParagraph("D.O.B"), secondCellLayoutSetup.copy().setPdfFont(examReportCardLayoutData.getBoldFont())),
						new CellData(getParagraph(dobValue), secondCellLayoutSetup),
						new CellData(getParagraph("Class & section"), secondCellLayoutSetup.copy().setPdfFont(examReportCardLayoutData.getBoldFont())),
						new CellData(getParagraph(classValue), secondCellLayoutSetup)
				));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(getParagraph("Father's Name"), secondCellLayoutSetup.copy().setPdfFont(examReportCardLayoutData.getBoldFont())),
						new CellData(getParagraph(studentLite.getFathersName()), secondCellLayoutSetup),
						new CellData(getParagraph("Admission No"), secondCellLayoutSetup.copy().setPdfFont(examReportCardLayoutData.getBoldFont())),
						new CellData(getParagraph(studentLite.getAdmissionNumber()), secondCellLayoutSetup)
				));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(getParagraph("Mother’s Name"), secondCellLayoutSetup.copy().setPdfFont(examReportCardLayoutData.getBoldFont())),
						new CellData(getParagraph(studentLite.getMothersName()), secondCellLayoutSetup),
						new CellData(getParagraph(EMPTY_TEXT), secondCellLayoutSetup.copy().setPdfFont(examReportCardLayoutData.getBoldFont())),
						new CellData(getParagraph( EMPTY_TEXT), secondCellLayoutSetup)
				));
		document.add(HeadingTable);
		document.add(table);

	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
										 float contentFontSize, ExamReportData examReportData, String reportType, PdfFont boldFont,
										 PdfFont regularFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		Paragraph rank = getKeyValueParagraph("Rank : ", String.valueOf(examReportData.getRank() != null ? examReportData.getRank() : ""), boldFont, boldFont);

		Paragraph attendance = getKeyValueParagraph("Attendance : ", String.valueOf((examReportData.getTotalAttendedDays() == null ? "-" : examReportData.getTotalAttendedDays()) + "/" +  (examReportData.getTotalWorkingDays() == null ? "-" : examReportData.getTotalWorkingDays())), boldFont, boldFont);

//		Paragraph division = getKeyValueParagraph("Division  :  ",  examReportData.getExamResultStatus() == null ||
//                    examReportData.getExamResultStatus() == ExamResultStatus.FAIL ? "NA" : getDivision(
//                    examReportData.getPercentage(), examReportData.getExamResultStatus()), boldFont, boldFont);

		Paragraph promotedClass = getKeyValueParagraph("Pass and Promoted to: ",
				examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), boldFont, regularFont);
//
//		Paragraph percentage = getKeyValueParagraph("Percentage : ",
//				examReportData.getPercentage() == null ? "-"
//						: String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%",
//				 boldFont, regularFont);


		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(rank, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(attendance, cellLayoutSetup)));

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(promotedClass, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
		document.add(table);

	}

	protected String getDivision(Double percentage, ExamResultStatus examResultStatus) {
		if (percentage == null) {
			return "";
		}
		if (percentage >= 75.0 && percentage <= 100) {
			return DISTINCTION;
		} else if (percentage >= 60.0 && percentage < 75.0) {
			return DIVISION_I;
		} else if (percentage >= 45.0 && percentage < 60.0) {
			return DIVISION_II;
		} else if (percentage >= 36.0 && percentage < 45.0) {
			return DIVISION_III;
		}
		return DIVISION_IV;
	}


//	protected void generateStudentAttributeTable(ExamReportCardLayoutData examReportCardLayoutData,
//			ExamReportData examReportData) throws IOException {
//		CellLayoutSetup attributeCellLayoutSetup = new CellLayoutSetup();
//		attributeCellLayoutSetup.setPdfFont(examReportCardLayoutData.getBoldFont())
//				.setFontSize(examReportCardLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER)
//				.setBorder(new SolidBorder(1f));
//
//		Table attributesTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(),
//				new float[] { 0.15f, 0.10f, 0.15f, 0.10f });
//
//		examReportCardLayoutData.getDocument().add(attributesTable);
//	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, ExamReportCardLayoutData examReportCardLayoutData) throws IOException {
		int singleContentColumn = 3;

		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(examReportCardLayoutData.getBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph(EMPTY_TEXT),
				getParagraph("Principal")), signatureCellLayoutSetup);
		table.setFixedPosition(SQUARE_BORDER_MARGIN + 1, SQUARE_BORDER_MARGIN + 15,
				documentLayoutSetup.getPageSize().getWidth());
		document.add(table);
	}

	protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
															   ExamReportData examReportData, String reportType, boolean addTotalRow, String subjectColumnTitle,
															   float subjectColumnWidth) throws IOException {
		ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
		if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
				|| examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
				|| CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
				.getAdditionalCourses())) {
			return;
		}
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), subjectColumnTitle,
				subjectColumnWidth,
				examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
				"#ff0000");

	}

	protected Set<UUID> getNonAdditionalSubjets(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();
				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
											  List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReport(institute, examReportData.getStudentLite(), studentManager, reportType,
						examReportData, examReportCardLayoutData, pageNumber);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			float logoWidth = 75f;
			float logoHeight = 75f;
			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			int instituteId = institute.getInstituteId();
			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
					DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH,
						examReportData, GridConfigs.forOnlyObtainedTotalRow(), "Scholastic Subjects",
						getScholasticMarksGridSubjectWidth(reportType),
						null, "#ff0000");
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}
}
