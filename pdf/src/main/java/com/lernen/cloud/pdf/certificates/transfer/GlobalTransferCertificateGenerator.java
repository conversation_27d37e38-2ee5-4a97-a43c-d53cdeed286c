package com.lernen.cloud.pdf.certificates.transfer;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;

/**
 * 
 * <AUTHOR>
 *
 */
public class GlobalTransferCertificateGenerator extends TransferCertificateGenerator {
	public GlobalTransferCertificateGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 50f;

	private static final String TC_CONTENT_PARAGRAPH_1 = "This is to certify that %s %s of %s & %s was admitted to school on %s and left on %s.";
	private static final String TC_CONTENT_PARAGRAPH_2 = "%s was studying in the session %s class %s. All dues to the school on %s account have been settled.";
	private static final String TC_CONTENT_PARAGRAPH_3 = "%s date of birth according to the admission register is %s";

	@Override
	public DocumentOutput generateTransferCertificate(StudentManager studentManager, Institute institute, Student student, 
			String documentName) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
			float contentFontSize = 12f;

			Document document = initDocument(invoice.getContent(), documentLayoutSetup);

			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
					null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);

			generateDynamicImageProvider(documentLayoutData, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

			float bgImageHeightWidth = 500f;
			//Institute watermark
			int instituteId = institute.getInstituteId();
			generateWatermark(documentLayoutData, instituteId, bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);


			generateHeader(document, documentLayoutSetup, student, institute);
			generateContent(document, documentLayoutSetup, contentFontSize, student, institute);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize);

			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	private void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, Student student,
			Institute institute) throws IOException {
		int singleContentColumn = 1;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setWidth(contentWidth).setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("SCHOLAR'S TRANSFER CERTIFICATE")),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
		document.add(table);

		// Student copy section
		singleContentColumn = 1;
		columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		contentWidth = getContentWidth(documentLayoutSetup);
		table = new Table(columnCount);

		cellLayoutSetup.setWidth(contentWidth / singleContentColumn);

		Paragraph admissionNumber = getKeyValueParagraph("ADMISSION NO. - ",
				student.getStudentBasicInfo().getAdmissionNumber());

		addRow(table, documentLayoutSetup, Arrays
				.asList(new CellData(admissionNumber, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT))));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		document.add(table);

	}

	private void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			Student student, Institute institute) throws IOException {
		int singleContentColumn = 1;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);

		String heShe = "He/She";
		String hisHer = "His/Her";
		if (student.getStudentBasicInfo().getGender() == Gender.FEMALE) {
			heShe = "She";
			hisHer = "Her";
		} else if (student.getStudentBasicInfo().getGender() == Gender.MALE) {
			heShe = "He";
			hisHer = "His";
		}
		Text t1 = new Text(String.format(TC_CONTENT_PARAGRAPH_1, student.getStudentBasicInfo().getName(),
				getSonDaughter(student.getStudentBasicInfo().getGender()),
				StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName()) ? "NA" : student.getStudentFamilyInfo().getFathersName(),
				StringUtils.isBlank(student.getStudentFamilyInfo().getMothersName()) ? "NA" : student.getStudentFamilyInfo().getMothersName(),
				student.getStudentBasicInfo().getAdmissionDate() == null || student.getStudentBasicInfo().getAdmissionDate() <= 0
						? "NA" : DateUtils.getFormattedDate(student.getStudentBasicInfo().getAdmissionDate(), DATE_FORMAT,
						User.DFAULT_TIMEZONE),
				student.getStudentBasicInfo().getRelieveDate() == null
						|| student.getStudentBasicInfo().getRelieveDate() <= 0 ? NOT_AVAILABLE
								: DateUtils.getFormattedDate(student.getStudentBasicInfo().getRelieveDate(),
										DATE_FORMAT, User.DFAULT_TIMEZONE)));
		Text t2 = new Text(String.format(TC_CONTENT_PARAGRAPH_2, heShe,
				student.getStudentAcademicSessionInfoResponse().getAcademicSession().getYearDisplayName(),
				student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(),
				hisHer.toLowerCase()));
		Text t3 = new Text(String.format(TC_CONTENT_PARAGRAPH_3, hisHer,
				student.getStudentBasicInfo().getDateOfBirth() == null
						|| student.getStudentBasicInfo().getDateOfBirth() <= 0 ? NOT_AVAILABLE
								: DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth(),
										DATE_FORMAT, User.DFAULT_TIMEZONE)));

		Paragraph p1 = new Paragraph();
		p1.add(t1);

		Paragraph p2 = new Paragraph();
		p2.add(t2);

		Paragraph p3 = new Paragraph();
		p3.add(t3);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p1, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p2, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p3, cellLayoutSetup)));

		document.add(table);
	}

	private String getSonDaughter(Gender gender) {
		if (gender == Gender.FEMALE) {
			return "daughter";
		} else if (gender == Gender.MALE) {
			return "son";
		}
		return "son/daughter";

	}

	private void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize)
			throws IOException {
		int singleContentColumn = 3;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);

		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData("Signature Class Teacher", signatureCellLayoutSetup),
				new CellData("Checked By", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
				new CellData("Principal", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
		document.add(table);
	}
}
