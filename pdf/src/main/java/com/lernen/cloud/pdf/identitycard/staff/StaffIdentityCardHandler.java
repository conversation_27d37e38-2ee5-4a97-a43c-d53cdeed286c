package com.lernen.cloud.pdf.identitycard.staff;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.staff.FullStaffDetails;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StaffIdentityCardHandler {

	private static final Logger logger = LogManager.getLogger(StaffIdentityCardHandler.class);

	private final StaffIdentityCardGeneratorFactory identityCardGeneratorFactory;
	private final InstituteManager instituteManager;
	private final StaffManager staffManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;

	public StaffIdentityCardHandler(InstituteManager instituteManager, StaffManager staffManager,
									UserPreferenceSettings userPreferenceSettings, UserPermissionManager userPermissionManager, AssetProvider assetProvider) {
		this.identityCardGeneratorFactory = new StaffIdentityCardGeneratorFactory(assetProvider);
		this.instituteManager = instituteManager;
		this.staffManager = staffManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
	}

	public DocumentOutput generateIdentityCard(int instituteId, UUID staffId, UUID userId) {
		if (instituteId <= 0 || staffId == null || userId == null) {
			logger.error("Invalid institute id or academicSessionId or staffId or userId");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
					"Invalid institute id or academicSessionId or staffId or userId"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_STAFF_IDENTITY_CARD);

		FullStaffDetails staff = staffManager.getFullStaffDetails(instituteId, staffId);

		if (staff == null) {
			logger.info(
					"No student found in instituteId {},  staffId {}. Skipping idenity card generation.",
					instituteId, staffId);
			return null;
		}

		StaffIdentityCardGenerator identityCardGenerator = identityCardGeneratorFactory
				.getIdentityCardGenerator(instituteId);
		
		DocumentPropertiesPreferences documentPropertiesPreferences =  userPreferenceSettings.getDocumentPropertiesPreferences(instituteId);

		String fileName = staff.getStaffBasicDetailsWithCategoryDepartDesignation().getName().trim() + " - Identity Card" + ".pdf";
		return identityCardGenerator.generateIdentityCard(staffManager, instituteManager.getInstitute(instituteId), staff, fileName, documentPropertiesPreferences);
	}

	public DocumentOutput generateIdentityCards(int instituteId, UUID userId) {
		if (instituteId <= 0 || userId == null) {
			logger.error("Invalid institute id or academicSessionId or staffId or userId");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
					"Invalid institute id or academicSessionId or staffId or userId"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_STAFF_IDENTITY_CARD);

		List<FullStaffDetails> staffList = staffManager.getAdvanceStaffDetails(instituteId, StaffStatus.ONBOARD, null);

		if (CollectionUtils.isEmpty(staffList)) {
			logger.info(
					"No staff found in instituteId {} Skipping idenity card generation.",
					instituteId);
			return null;
		}

		StaffIdentityCardGenerator identityCardGenerator = identityCardGeneratorFactory
				.getIdentityCardGenerator(instituteId);

		String fileName = "Staff - Identity Card.pdf";
		DocumentPropertiesPreferences documentPropertiesPreferences =  userPreferenceSettings.getDocumentPropertiesPreferences(instituteId);
		return identityCardGenerator.generateIdentityCards(staffManager, instituteManager.getInstitute(instituteId), staffList, fileName, documentPropertiesPreferences);
	}

}
