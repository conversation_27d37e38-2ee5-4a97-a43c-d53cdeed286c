package com.lernen.cloud.pdf.admitcard;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.ExamAdmitCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.DatesheetDetailRow;
import com.lernen.cloud.core.api.examination.DatesheetDetails;
import com.lernen.cloud.core.api.examination.ExamDetails;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

public class AdmitCardWithDatesheetGenerator10200  extends GlobalAdmitCardWithDatesheetGenerator {

    public AdmitCardWithDatesheetGenerator10200(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(GlobalAdmitCardWithDatesheetGenerator.class);

    @Override
    public DocumentOutput generateAdmitCard(Institute institute, List<Student> students, ExamDetails examDetails,
                                            Map<Integer, List<DatesheetDetailRow>> datesheetDetailsMap,
                                            Map<UUID, Set<UUID>> studentCourseMap,
                                            String documentName, ExamAdmitCardPreferences examAdmitCardPreferences,
                                            StudentManager studentManager, DatesheetDetails datesheetDetails) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4, DEFAULT_PAGE_TOP_MARGIN,
                    DEFAULT_PAGE_BOTTOM_MARGIN, DEFAULT_PAGE_SIDE_MARGIN);
            float contentFontSize = 12f;
            float defaultBorderWidth = DEFAULT_TABLE_BORDER_WIDTH;
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

            int instituteId = institute.getInstituteId();

            float logoWidth = examAdmitCardPreferences == null ? LOGO_WIDTH :
                    examAdmitCardPreferences.getLogoWidth()  == null ? LOGO_WIDTH : examAdmitCardPreferences.getLogoWidth();
            float logoHeight = examAdmitCardPreferences == null ? LOGO_HEIGHT :
                    examAdmitCardPreferences.getLogoHeight()  == null ? LOGO_HEIGHT : examAdmitCardPreferences.getLogoHeight();

            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup,
                    null, null, contentFontSize, defaultBorderWidth,
                    logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId),
                    null, null);

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            String notes = datesheetDetails == null ? null : datesheetDetails.getNotes();

            int pageNumber = 1;
            for (Student student : students) {

                Set<UUID> assignCoursesUUIDs = studentCourseMap.get(student.getStudentId());
                generateAdmitCards(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                        documentLayoutSetup, institute, examDetails, datesheetDetailsMap, assignCoursesUUIDs, student, pageNumber,
                        studentManager, contentFontSize, examAdmitCardPreferences, notes);

                if (pageNumber != students.size()) {
                    documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }

            documentLayoutData.getDocument().close();

            return documentOutput;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error while generating admit cards for institute {}, exam id {}", institute.getInstituteId(),
                    examDetails.getExamMetaData().getExamId(), e);
        }
        return null;
    }

    private void generateAdmitCards(Document document, DocumentLayoutData documentLayoutData,
                                    PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
                                    Institute institute, ExamDetails examDetails,
                                    Map<Integer, List<DatesheetDetailRow>> datesheetDetails,
                                    Set<UUID> assignCoursesUUIDs, Student student, int pageNumber,
                                    StudentManager studentManager, float contentFontSize,
                                    ExamAdmitCardPreferences examAdmitCardPreferences, String notes) throws IOException {

        /**
         * Watermark
         */
        float watermarkImageHeightWidth = 400f;
        generateWatermark(documentLayoutData, institute.getInstituteId(),
                watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20, watermarkImageHeightWidth / 2);


        byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
        if (image != null) {
            generateImage(document, documentLayoutSetup, image, 80, 90,
                    documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f,
                    documentLayoutSetup.getPageSize().getHeight() * 0.80f + 10f);
        }

        /**
         * Header
         */
        generateHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                documentLayoutSetup, institute);

        /**
         * Student Details
         */
        generateStudentDetails(document, documentLayoutData, documentLayoutSetup, cambriaFont, cambriaBoldFont, student, examDetails,
                contentFontSize);

        /**
         * Datesheet Table
         */
        generateDatesheetTable(document, documentLayoutSetup, documentLayoutData, cambriaFont, cambriaBoldFont, institute,
                student, datesheetDetails, assignCoursesUUIDs, examAdmitCardPreferences);

        /**
         * Notes
         */
        if(!StringUtils.isBlank(notes)) {
            generateNoteDetails(document, documentLayoutSetup, documentLayoutData, cambriaFont, cambriaBoldFont, notes);
        }

        /**
         * Signature Details
         */
        generateSignatureDetails(document, documentLayoutSetup, documentLayoutData, cambriaBoldFont, institute, contentFontSize,
                "Class Teacher's Signature", EMPTY_TEXT,"Principal's Signature");

        generateImage(document, documentLayoutSetup,
                ImageProvider.INSTANCE.getImage(ImageProvider._10200_PRINCIPAL_SIGNATURE),
                50f, 50f,
                documentLayoutSetup.getPageSize().getWidth() - 180f, 40f);

        generateBorderLayout(documentLayoutData, pageNumber);

    }

    protected void generateHeader(Document document, DocumentLayoutData documentLayoutData,
                                  PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
                                  Institute institute) throws IOException {

        generateDynamicImageProvider(documentLayoutData, 30f, documentLayoutSetup.getPageSize().getHeight() - LOGO_HEIGHT - 25f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setTextAlignment(TextAlignment.CENTER).setPaddingLeft(25f);

        float instituteNameFontSize = 17f;
        List<Integer> instituteNameFontColor = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);
        if(institute.getInstituteId() == 10200) {
            instituteNameFontSize = 24f;
            instituteNameFontColor = EColorUtils.hex2Rgb(EColorUtils.purpleHexCode);
        }
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase(), instituteNameFontColor)),
                cellLayoutSetup.copy().setFontSize(instituteNameFontSize).setPdfFont(cambriaBoldFont));

        List<Integer> letterHead1FontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1(), letterHead1FontColor)),
                cellLayoutSetup.copy().setFontSize(12f).setPdfFont(cambriaFont));

        List<Integer> letterHead2FontColor = EColorUtils.hex2Rgb(EColorUtils.themeColorHexCode);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2(), letterHead2FontColor)),
                cellLayoutSetup.copy().setFontSize(12f).setPdfFont(cambriaFont));

        documentLayoutData.getDocument().add(table);

        addBlankLine(document, true, 1);

        generateAdmitCardText(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                documentLayoutSetup, institute);
    }
}
