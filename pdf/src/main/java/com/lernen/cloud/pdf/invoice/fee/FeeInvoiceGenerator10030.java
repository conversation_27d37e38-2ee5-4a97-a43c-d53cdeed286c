package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberToWords;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class FeeInvoiceGenerator10030 extends FeeInvoiceGenerator {

	public FeeInvoiceGenerator10030(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public static final float DEFAULT_LOGO_WIDTH = 40f;
	public static final float DEFAULT_LOGO_HEIGHT = 40f;

	@Override
	public DocumentOutput generateInvoice(Institute institute, FeePaymentInvoiceSummary feePaymentInvoiceSummary,
										  StudentTransportDetails studentTransportDetails, String documentName, boolean officeCopy, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite,
										  FeeInvoicePreferences feeInvoicePreferences) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			if(userType == UserType.STUDENT) {
				officeCopy = false;
			}
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, DEFAULT_PAGE_SIZE,
					DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN,
					DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN, 158.75f, 0f);
			float contentFontSize = 8f;
			float headerFontSize = 11f;
			float defaultBorderWidth = 0.1f;
			Document document = initDocument(invoice.getContent(), documentLayoutSetup);
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());
			
			generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
					contentFontSize, headerFontSize, defaultBorderWidth, 1, STUDENT_COPY_TEXT, userType);
			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	private void generateFeeInvoice(Institute institute,DocumentLayoutData documentLayoutData,
									FeePaymentInvoiceSummary feePaymentInvoiceSummary,
									float contentFontSize, float headerFontSize, float defaultBorderWidth,
									int pageNumber, String studentCopyText, UserType userType) throws IOException {
		
		Document document = documentLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();

		generateDynamicImageProvider(documentLayoutData, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
		generateHeader(document, documentLayoutSetup, feePaymentInvoiceSummary, institute, headerFontSize,
				defaultBorderWidth, studentCopyText, userType);
		generateStudentInformation(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
				defaultBorderWidth);
		generateFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
				defaultBorderWidth);
		generateFeePaymentSummary(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
				defaultBorderWidth);
		generateSignatureBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
		if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
				.getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.CANCELLED) {
			// addWaterMark(document, documentLayoutSetup, "Cancelled");
			PdfPage pdfPage = document.getPdfDocument().getPage(pageNumber);
			Rectangle pagesize = pdfPage.getPageSizeWithRotation();

			float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
			float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.70f;

			addWaterMark(document, ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y, pageNumber);
		}
	}

	public Table getPDFTable(DocumentLayoutSetup documentLayoutSetup, int singleCopyColumnCount) {
		return getPDFTable(documentLayoutSetup, documentLayoutSetup.getPageSize().getWidth(),
				singleCopyColumnCount);
	}

	public Table getPDFTable(DocumentLayoutSetup documentLayoutSetup, float[] singleCopyColumnWidthPercents) {
		return getPDFTable(documentLayoutSetup, documentLayoutSetup.getPageSize().getWidth(),
				singleCopyColumnWidthPercents);
	}

	public void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
			FeePaymentInvoiceSummary feePaymentInvoiceSummary, Institute institute, float headerFontSize,
			float defaultBorderWidth, String studentCopyText, UserType userType) throws IOException {
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())),
				cellLayoutSetup.setFontSize(headerFontSize));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup.setFontSize(9f));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("School Fee Receipt")),
				Arrays.asList(getParagraph("School Fee Receipt")), cellLayoutSetup);

		document.add(table);

		// Student copy section
		singleContentColumn = 3;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		cellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth));

		Paragraph receipt = getKeyValueParagraph("Receipt No. ",
				feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getInvoiceId());

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(receipt, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
						new CellData(studentCopyText, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
						new CellData(
								feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
										.getAcademicSession().getYearDisplayName(),
								cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))),
				Arrays.asList(new CellData(receipt, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
						new CellData(userType == UserType.STUDENT ? STUDENT_COPY_TEXT : OFFICE_COPY_TEXT, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
						new CellData(
								feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
										.getAcademicSession().getYearDisplayName(),
								cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
		document.add(table);

	}

	public void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
			throws IOException {
		int singleContentColumn = 2;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)
				.setBorderLeft(new SolidBorder(defaultBorderWidth));
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
				.setBorderRight(new SolidBorder(defaultBorderWidth));

		Paragraph feeCollectedBy = getKeyValueParagraph("Collected By : ", String.valueOf(
				feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionAddedByUser() == null ? ""
						: feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionAddedByUser()
								.getFullName()));

		Paragraph studentName = getKeyValueParagraph("Student Name : ",
				feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getName());

		Paragraph date = getKeyValueParagraph("Date: ",
				DateUtils.getFormattedDate(
						feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionAddedAt(),
						DATE_WITH_TIME_TILL_MINUTE_FORMAT, User.DFAULT_TIMEZONE));

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(date, firstCellLayoutSetup),
				new CellData(feeCollectedBy, thirdCellLayoutSetup)));

		Paragraph fatherName = getKeyValueParagraph("Father Name : ",
				feePaymentInvoiceSummary.getStudent().getStudentFamilyInfo().getFathersName());
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. ",
				feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getAdmissionNumber());

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentName, firstCellLayoutSetup),
				new CellData(admissionNumber, thirdCellLayoutSetup)));

		Paragraph motherName = getKeyValueParagraph("Mother Name : ",
				feePaymentInvoiceSummary.getStudent().getStudentFamilyInfo().getMothersName());

		Paragraph classValue = getKeyValueParagraph("Class : ", feePaymentInvoiceSummary.getStudent()
				.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection())
						.setFont(getRegularFont());

		// Text classValue = new
		// Text(feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
		// .getStandard().getDisplayNameWithSection()).setFont(getRegularFont());
		// Paragraph classContent = new Paragraph();
		// classContent.add(classValue);

		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(fatherName, firstCellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth))),
				new CellData(classValue, thirdCellLayoutSetup.setBorderBottom(new SolidBorder(defaultBorderWidth)))));

		document.add(table);
	}

	public void generateFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth) throws IOException {

		if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
				.getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.ACTIVE) {
			generateActiveFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
					defaultBorderWidth);
		} else {
			generateCancelledFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
					defaultBorderWidth);
		}
	}

	public void generateCancelledFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
			throws IOException {

		if (feePaymentInvoiceSummary.getFeePaymentTransactionDetails() == null || CollectionUtils.isEmpty(
				feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getFeeIdFeeHeadTransactionDetails())) {
			return;
		}

		int singleFeeContentColumn = 1;
		CellLayoutSetup feeCellLayoutSetup = new CellLayoutSetup();
		feeCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
				.setBorderRight(new SolidBorder(defaultBorderWidth));

		int count = 0;
		for (FeeIdFeeHeadTransactionDetails feeIdFeeHeadTransactionDetails : feePaymentInvoiceSummary
				.getFeePaymentTransactionDetails().getFeeIdFeeHeadTransactionDetails()) {
			count++;
			Table feeTable = getPDFTable(documentLayoutSetup, singleFeeContentColumn);
			addRow(feeTable, documentLayoutSetup,
					Arrays.asList(
							getParagraph(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeName())),
					feeCellLayoutSetup);
			document.add(feeTable);

			Table feeHeadTable = getPDFTable(documentLayoutSetup, DEFAULT_CANCELLED_FEE_HEAD_HEADER_WIDTH);

			CellLayoutSetup feeHeadCellLayoutSetup = new CellLayoutSetup();
			feeHeadCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
					.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

			if (count == 1) {
				addRow(feeHeadTable, documentLayoutSetup,
						Arrays.asList(new CellData("#", feeHeadCellLayoutSetup),
								new CellData("Particulars", feeHeadCellLayoutSetup),
								new CellData("Paid Amount(INR)", feeHeadCellLayoutSetup)));
			}
			int feeHeadCount = 1;
			feeHeadCellLayoutSetup.setPdfFont(getRegularFont());
			for (FeeHeadTransactionAmountsDetails feeHeadTransactionAmountsDetails : feeIdFeeHeadTransactionDetails
					.getFeeHeadTransactionAmountsDetails()) {
				addRow(feeHeadTable, documentLayoutSetup,
						Arrays.asList(new CellData(String.valueOf(feeHeadCount++), feeHeadCellLayoutSetup),
								new CellData(feeHeadTransactionAmountsDetails.getFeeHeadConfiguration().getFeeHead(),
										feeHeadCellLayoutSetup),
								new CellData(String.valueOf(feeHeadTransactionAmountsDetails.getPaidAmount()),
										feeHeadCellLayoutSetup)));
			}
			document.add(feeHeadTable);
		}
	}

	public void generateActiveFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
			throws IOException {

		if (CollectionUtils.isEmpty(feePaymentInvoiceSummary.getFeeIdInvoices())) {
			return;
		}

		int singleFeeContentColumn = 1;
		CellLayoutSetup feeCellLayoutSetup = new CellLayoutSetup();
		feeCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
				.setBorderRight(new SolidBorder(defaultBorderWidth));

		int count = 0;
		for (FeeIdInvoice feeIdInvoice : feePaymentInvoiceSummary.getFeeIdInvoices()) {
			count++;
			Table feeHeadTable = getPDFTable(documentLayoutSetup, DEFAULT_ACTIVE_FEE_HEAD_HEADER_WIDTH);

			CellLayoutSetup feeHeadCellLayoutSetup = new CellLayoutSetup();
			feeHeadCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
					.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

			if (count == 1) {
				addRow(feeHeadTable, documentLayoutSetup,
						Arrays.asList(new CellData("#", feeHeadCellLayoutSetup),
								new CellData("Particulars", feeHeadCellLayoutSetup),
								new CellData("Amount(INR)", feeHeadCellLayoutSetup),
								new CellData("Dues", feeHeadCellLayoutSetup),
								new CellData("Paid Amount", feeHeadCellLayoutSetup)));
			}
			Table feeTable = getPDFTable(documentLayoutSetup, singleFeeContentColumn);
			addRow(feeTable, documentLayoutSetup,
					Arrays.asList(getParagraph(feeIdInvoice.getFeeConfigurationBasicInfo().getFeeName())),
					feeCellLayoutSetup);
			document.add(feeTable);
			int feeHeadCount = 1;
			feeHeadCellLayoutSetup.setPdfFont(getRegularFont());
			for (FeeHeadInvoice feeHeadInvoice : feeIdInvoice.getFeeHeadInvoices()) {
				addRow(feeHeadTable, documentLayoutSetup, Arrays.asList(
						new CellData(String.valueOf(feeHeadCount++), feeHeadCellLayoutSetup),
						new CellData(feeHeadInvoice.getFeeHeadPaymentDetails().getFeeHeadConfiguration().getFeeHead(),
								feeHeadCellLayoutSetup),
						new CellData(String.valueOf(feeHeadInvoice.getFeeHeadPaymentDetails().getAssignedAmount()),
								feeHeadCellLayoutSetup),
						new CellData(String.valueOf(feeHeadInvoice.getDueAmountBeforeTransaction()),
								feeHeadCellLayoutSetup),
						new CellData(String.valueOf(feeHeadInvoice.getFeeHeadTransactionAmounts().getPaidAmount()),
								feeHeadCellLayoutSetup)));
			}
			document.add(feeHeadTable);
		}

	}

	public void generateFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
			throws IOException {

		if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
				.getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.ACTIVE) {
			generateActiveFeePaymentSummary(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
					defaultBorderWidth);
		} else {
			generateCancelledFeePaymentSummary(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
					defaultBorderWidth);
		}
	}

	public void generateCancelledFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
			throws IOException {
		int singleContentColumn = 2;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
		keyCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth));

		CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
				.setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(null);

		double fineAmount = 0d;
		if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalFineAmount(), 0d) > 0) {
			fineAmount = feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalFineAmount();
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData("Total Fine Paid (INR):", keyCellLayoutSetup),
							new CellData(String.valueOf(
									feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalFineAmount()),
									valueCellLayoutSetup)));
		}

		double creditWalletAmount = 0d;

		if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount(),
				0d) > 0) {
			creditWalletAmount = feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount();
			addRow(table, documentLayoutSetup, Arrays.asList(
					new CellData("Credit Wallet Amount (INR):", keyCellLayoutSetup),
					new CellData(String.valueOf(
							feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount()),
							valueCellLayoutSetup)));
		}

		double debitWalletAmount = 0d;

		if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount(),
				0d) > 0) {
			debitWalletAmount = feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount();
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData("Used Wallet Amount (INR):", keyCellLayoutSetup),
							new CellData(String.valueOf(
									feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount()),
									valueCellLayoutSetup)));
		}

		double totalActualPaidAmount = feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmount() + fineAmount
				+ creditWalletAmount - debitWalletAmount;

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Total Paid Amount (INR):", keyCellLayoutSetup),
				new CellData(String.valueOf(totalActualPaidAmount), valueCellLayoutSetup)));

		document.add(table);

		// Amount in words
		singleContentColumn = 1;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		Paragraph amountsInWord = getKeyValueParagraph("(In Words): ",
				NumberToWords.convertNumberToWords((int) Math.round(totalActualPaidAmount)) + " Only.");

		CellLayoutSetup amountInWordsCellLayoutSetup = new CellLayoutSetup();
		amountInWordsCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth))
				.setBorderBottom(null);

		addRow(table, documentLayoutSetup, Arrays.asList(amountsInWord), amountInWordsCellLayoutSetup);
		document.add(table);
	}

	public void generateActiveFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
			throws IOException {
		int singleContentColumn = 2;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
		keyCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth));

		CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
				.setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(null);

//		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Total Fees (INR):", keyCellLayoutSetup),
//				new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalAssignedAmount()), valueCellLayoutSetup)));
//		if (Double.compare(feePaymentInvoiceSummary.getTotalDiscontAmount(), 0d) > 0) {
//			addRow(table, documentLayoutSetup,
//					Arrays.asList(new CellData("Discount Amount (INR):", keyCellLayoutSetup), new CellData(
//							String.valueOf(feePaymentInvoiceSummary.getTotalDiscontAmount()), valueCellLayoutSetup)));
//		}

		if (Double.compare(feePaymentInvoiceSummary.getTotalFineAmount(), 0d) > 0) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Fine Amount (INR):", keyCellLayoutSetup),
					new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalFineAmount()), valueCellLayoutSetup)));
		}
		double fineAmount = Double.compare(feePaymentInvoiceSummary.getTotalFineAmount(), 0d) > 0
				? feePaymentInvoiceSummary.getTotalFineAmount()
				: 0d;
		double creditWalletAmount = Double
				.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount(), 0d) > 0
						? feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount()
						: 0d;

		double debitWalletAmount = 0d;

		if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount(),
				0d) > 0) {
			debitWalletAmount = feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount();
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData("Used Wallet Amount (INR):", keyCellLayoutSetup),
							new CellData(String.valueOf(
									feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount()),
									valueCellLayoutSetup)));
		}
		double totalActualPaidAmount = feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmount() + fineAmount
				+ creditWalletAmount - debitWalletAmount;

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Total Paid Amount (INR):", keyCellLayoutSetup),
				new CellData(String.valueOf(totalActualPaidAmount), valueCellLayoutSetup)));

//		if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount(),
//				0d) > 0) {
//			addRow(table, documentLayoutSetup, Arrays.asList(
//					new CellData("Credit Wallet Amount (INR):", keyCellLayoutSetup),
//					new CellData(String.valueOf(
//							feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount()),
//							valueCellLayoutSetup)));
//		}

//		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Due Amount (INR):", keyCellLayoutSetup),
//				new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalDueAmount()), valueCellLayoutSetup)));

		document.add(table);

		// Amount in words
		singleContentColumn = 1;
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		Paragraph amountsInWord = getKeyValueParagraph("(In Words): ",
				NumberToWords.convertNumberToWords((int) Math.round(totalActualPaidAmount)) + " Only.");

		CellLayoutSetup amountInWordsCellLayoutSetup = new CellLayoutSetup();
		amountInWordsCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth))
				.setBorderBottom(null);

		addRow(table, documentLayoutSetup, Arrays.asList(amountsInWord), amountInWordsCellLayoutSetup);
		document.add(table);

	}

	public void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			float defaultBorderWidth) throws IOException {
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
				.setBorderTop(null);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)),
				signatureCellLayoutSetup.copy().setBorderBottom(null));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Signature")), signatureCellLayoutSetup);
		document.add(table);
	}

	@Override
	public DocumentOutput generateBulkInvoices(Institute institute, List<FeePaymentInvoiceSummary>
			feePaymentInvoiceSummaryList, String documentName, boolean doubleCopy, FeeInvoicePreferences feeInvoicePreferences) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, DEFAULT_PAGE_SIZE,
					DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN, DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN, 158.75f, 0f);
			float contentFontSize = 8f;
			float headerFontSize = 11f;
			float defaultBorderWidth = 0.1f;
			Document document = initDocument(invoice.getContent(), documentLayoutSetup);
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());

			if(CollectionUtils.isEmpty(feePaymentInvoiceSummaryList)) {
				document.close();
				return invoice;
			}

			int pageNumber = 1;
			for (FeePaymentInvoiceSummary feePaymentInvoiceSummary : feePaymentInvoiceSummaryList) {
				generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
						contentFontSize, headerFontSize, defaultBorderWidth, pageNumber, OFFICE_COPY_TEXT, null);

				if (pageNumber != feePaymentInvoiceSummaryList.size()) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}
			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}
