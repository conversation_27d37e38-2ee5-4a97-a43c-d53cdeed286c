/**
 * 
 */
package com.embrate.cloud.push.notifications.content.builder;

import com.embrate.cloud.core.api.attendance.AttendanceInputType;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.attendance.AttendanceType;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceType;
import com.lernen.cloud.core.api.user.UserType;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class StaffAttendancePushNotificationContentBuilder {
	private static final String DEFAULT_TITLE = "Your attendance is marked";
	private static final String DEFAULT_DEVICE_DESCRIPTION = "Your attendance status '%s' is captured at %s in biometric machine.";
	private static final String DEFAULT_WEB_DESCRIPTION = "Your attendance status '%s' is marked at %s.";

	private static final String LATE_ENTRY_TITLE = "Attendance marked, but you are late today";
	private static final String EARLY_EXIT_TITLE = "Attendance marked, but you are leaving early today";

	private static final String DEFAULT_ADMIN_TITLE = "Staff attendance is marked";
	private static final String DEFAULT_ADMIN_DEVICE_DESCRIPTION = "Dear Admin,\nAttendance for %s with %s is recorded as %s at %s via the biometric system.\nThank you.";
	private static final String DEFAULT_ADMIN_WEB_DESCRIPTION = "Dear Admin,\nAttendance for %s with %s is recorded as %s at %s\nThank you.";

	public PushNotificationContent getStaffPushNotificationContent(String attendanceDateStr, AttendanceInputType attendanceInputType, StaffAttendanceType staffAttendanceType, boolean attendanceWithinTimeRange) {

		switch (attendanceInputType) {
			case DEVICE:
				return new PushNotificationContent(getAttendanceWithinTimeRangeValue(staffAttendanceType, attendanceWithinTimeRange), String.format(DEFAULT_DEVICE_DESCRIPTION, staffAttendanceType.name(), attendanceDateStr), null);
			case WEB:
			case MOBILE:
				return new PushNotificationContent(getAttendanceWithinTimeRangeValue(staffAttendanceType, attendanceWithinTimeRange), String.format(DEFAULT_WEB_DESCRIPTION, staffAttendanceType.name(), attendanceDateStr), null);
			default:
				return null;
		}
	}

	private String getAttendanceWithinTimeRangeValue(StaffAttendanceType staffAttendanceType, boolean attendanceWithinTimeRange) {
		if(attendanceWithinTimeRange) {
			return DEFAULT_TITLE;
		}
		switch (staffAttendanceType) {
			case IN:
				return LATE_ENTRY_TITLE;
			case OUT:
				return EARLY_EXIT_TITLE;
			default:
				return DEFAULT_TITLE;
		}
	}

	public PushNotificationContent getAdminPushNotificationContent(String staffId, String staffName, AttendanceInputType attendanceInputType, StaffAttendanceType staffAttendanceType, String attendanceDateStr) {

		switch(attendanceInputType) {
			case DEVICE:
				return new PushNotificationContent(DEFAULT_ADMIN_TITLE, String.format(DEFAULT_ADMIN_DEVICE_DESCRIPTION, staffName, staffId, staffAttendanceType.name(), attendanceDateStr), null);
			case WEB:
			case MOBILE:
				return new PushNotificationContent(DEFAULT_ADMIN_TITLE, String.format(DEFAULT_ADMIN_WEB_DESCRIPTION, staffName, staffId, staffAttendanceType.name(), attendanceDateStr), null);
			default:
				return null;
		}
	}

	public PushNotificationContent getStaffStatusPushNotificationContent(String attendanceDateStr, AttendanceInputType attendanceInputType, StaffAttendanceStatus staffAttendanceStatus) {

		switch (attendanceInputType) {
			case DEVICE:
				return new PushNotificationContent(DEFAULT_TITLE, String.format(DEFAULT_DEVICE_DESCRIPTION, staffAttendanceStatus.name(), attendanceDateStr), null);
			case WEB:
			case MOBILE:
				return new PushNotificationContent(DEFAULT_TITLE, String.format(DEFAULT_WEB_DESCRIPTION, staffAttendanceStatus.name(), attendanceDateStr), null);
			default:
				return null;
		}
	}

	public PushNotificationContent getAdminPushNotificationContent(String staffId, String staffName, AttendanceInputType attendanceInputType, StaffAttendanceStatus staffAttendanceStatus, String attendanceDateStr) {

		switch(attendanceInputType) {
			case DEVICE:
				return new PushNotificationContent(DEFAULT_ADMIN_TITLE, String.format(DEFAULT_ADMIN_DEVICE_DESCRIPTION, staffName, staffId, staffAttendanceStatus.name(), attendanceDateStr), null);
			case WEB:
			case MOBILE:
				return new PushNotificationContent(DEFAULT_ADMIN_TITLE, String.format(DEFAULT_ADMIN_WEB_DESCRIPTION, staffName, staffId, staffAttendanceStatus.name(), attendanceDateStr), null);
			default:
				return null;
		}
	}
}
