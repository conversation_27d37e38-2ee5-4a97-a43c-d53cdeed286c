/**
 *
 */
package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.StudentDiaryPushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.diary.StudentDiaryManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class StudentDiaryPushNotificationHandler extends AbstractPushNotificationHandler {

    public static final String STUDENT_DIARY_REMARK = "studentDiaryRemark";
    public static final String REMARK_ID = "remarkId";
    private static final Logger logger = LogManager.getLogger(StudentDiaryPushNotificationHandler.class);
    private final PushNotificationManager pushNotificationManager;
    private final StudentDiaryPushNotificationContentBuilder studentDiaryPushNotificationContentBuilder;
    private final StudentDiaryManager studentDiaryManager;
    private final UserPreferenceSettings userPreferenceSettings;
    private final UserPermissionManager userPermissionManager;

    public StudentDiaryPushNotificationHandler(PushNotificationManager pushNotificationManager, StudentDiaryPushNotificationContentBuilder studentDiaryPushNotificationContentBuilder, StudentDiaryManager studentDiaryManager, UserPreferenceSettings userPreferenceSettings, UserPermissionManager userPermissionManager) {
        super(pushNotificationManager);
        this.pushNotificationManager = pushNotificationManager;
        this.studentDiaryPushNotificationContentBuilder = studentDiaryPushNotificationContentBuilder;
        this.studentDiaryManager = studentDiaryManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.userPermissionManager = userPermissionManager;
    }

    public void sendStudentRemarkNotificationsAsync(int instituteId, int academicSessionId, UUID timetableId, UUID createdUserId) {
        Thread t = new Thread(new Runnable() {
            @Override
            public void run() {
                sendStudentRemarkNotifications(instituteId, academicSessionId, timetableId, createdUserId);
            }
        });
        t.start();
    }

    public void sendStudentRemarkNotifications(int instituteId, int academicSessionId, UUID remarkId, UUID createdUserId) {
        if (instituteId <= 0 || remarkId == null) {
            logger.error("Invalid institute {} or remark {} ", instituteId, remarkId);
            return;
        }

        PushNotificationContent pushNotificationContent = studentDiaryPushNotificationContentBuilder.getPushNotificationContent();

        List<User> users = studentDiaryManager.getDiaryRemarkUsers(instituteId, academicSessionId, remarkId);
        if (CollectionUtils.isEmpty(users)) {
            logger.error("No user found for notification for institute {}, remark {} ", instituteId, remarkId);
            return;
        }

        List<User> finalUserList = new ArrayList<User>();
        for (User user : users) {
            if (!user.getUserStatus().equals(UserStatus.ENABLED)) {
                continue;
            }
            if (!user.getUuid().equals(createdUserId)) {
                finalUserList.add(user);
            }
        }

        Map<String, String> metaData = new HashMap<String, String>();
        metaData.put(REMARK_ID, remarkId == null ? null : remarkId.toString());

        boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(), NotificationEntity.STUDENT_DIARY_REMARK, remarkId, metaData));

        if (!notificationAdded) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS, "Invalid bell notification details."));
        }

        final String remarkIdStr = remarkId == null ? null : remarkId.toString();
        prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
            @Override
            public void update(Map<String, String> dataPayload, User user) {
                dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_DIARY_REMARK);
                dataPayload.put(REMARK_ID, remarkIdStr);
            }
        });
    }
}
