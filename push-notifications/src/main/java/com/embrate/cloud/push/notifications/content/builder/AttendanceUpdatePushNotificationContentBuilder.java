/**
 * 
 */
package com.embrate.cloud.push.notifications.content.builder;

import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.attendance.AttendanceType;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class AttendanceUpdatePushNotificationContentBuilder {
	private static final String DEFAULT_TITLE = "Your attendance is marked";
	private static final String DEFAULT_DESCRIPTION = "Your attendance for %s is marked. Click here to see";
	public PushNotificationContent getPushNotificationContent(String date, Map<Integer, AttendanceType> attendanceTypeMap,  Map<Integer, AttendanceStatus> attendanceStatusMap) {
		if(MapUtils.isEmpty(attendanceStatusMap) || MapUtils.isEmpty(attendanceTypeMap)){
			return new PushNotificationContent(DEFAULT_TITLE, String.format(DEFAULT_DESCRIPTION, date), null);
		}
		// Only 1 attendance type
	    if(attendanceTypeMap.size() == 1){
			AttendanceStatus attendanceStatus = null;
			for(Map.Entry<Integer, AttendanceStatus> attendanceStatusEntry : attendanceStatusMap.entrySet()){
				if(attendanceTypeMap.containsKey(attendanceStatusEntry.getKey())){
					attendanceStatus = attendanceStatusEntry.getValue();
					break;
				}
			}
			if(attendanceStatus == null){
				return new PushNotificationContent(DEFAULT_TITLE, String.format(DEFAULT_DESCRIPTION, date), null);
			}

			String title = "Your attendance is marked as %s today";
			String description = "Click here to see the summary";
			return new PushNotificationContent(String.format(title, attendanceStatus.getDisplayName()), description, null);
		}

		String title = "Your attendance is marked for today";
		StringBuilder description = new StringBuilder();
		String delimiter = "";
		for(Map.Entry<Integer, AttendanceStatus> attendanceStatusEntry : attendanceStatusMap.entrySet()){
			if(attendanceStatusEntry.getValue() == null){
				continue;
			}
			if(!attendanceTypeMap.containsKey(attendanceStatusEntry.getKey())){
				continue;
			}
			AttendanceType attendanceType = attendanceTypeMap.get(attendanceStatusEntry.getKey());
			description.append(delimiter).append(attendanceType.getName().trim()).append(" : ").append(attendanceStatusEntry.getValue().getDisplayName());
			delimiter = "\n";
		}
		description.append("\n\nClick here to see the summary");

		return new PushNotificationContent(title, description.toString(), null);
//
//		String title = "Your attendance is marked";
//		String body = "Your attendance for " +  date + " is marked. Click here to see";
//		switch (attendanceStatus){
//			case PRESENT:
//				title = "Your presence is marked";
//				body = "Your attendance for " +  date + " is marked as present. Click here to see";
//				break;
//			case LEAVE:
//				title = "Your leave is marked";
//				body = "Your are on leave for " +  date + ". Click here to see";
//				break;
//			case ABSENT:
//				title = "Your absent is marked";
//				body = "You are absent on " +  date + ". Click here to see";
//				break;
//			case HALF_DAY:
//				title = "Your half day is marked";
//				body = "Your attendance for " +  date + " is marked as half day. Click here to see";
//				break;
//		}
//		return new PushNotificationContent(title, body, null);
	}
}
