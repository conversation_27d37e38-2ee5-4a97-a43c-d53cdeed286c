package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.VisitorDetailsAddPushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.api.visitor.VisitorDetails;
import com.lernen.cloud.core.api.visitor.VisitorStatus;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.visitor.VisitorDetailsManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class VisitorDetailsAddPushNotificationHandler extends AbstractPushNotificationHandler {

	public static final String VISITOR_ID = "visitorId";
	public static final String ADMIN_ADD_VISITOR = "adminAddVisitor";
	private static final Logger logger = LogManager.getLogger(VisitorDetailsAddPushNotificationHandler.class);
	private final UserManager userManager;
	private final PushNotificationManager pushNotificationManager;
	private final VisitorDetailsManager visitorDetailsManager;
	private final UserPermissionManager userPermissionManager;
	private final VisitorDetailsAddPushNotificationContentBuilder visitorDetailsAddPushNotificationContentBuilder;

	public VisitorDetailsAddPushNotificationHandler(UserManager userManager, PushNotificationManager pushNotificationManager, VisitorDetailsManager visitorDetailsManager, UserPermissionManager userPermissionManager, VisitorDetailsAddPushNotificationContentBuilder visitorDetailsAddPushNotificationContentBuilder) {
		super(pushNotificationManager);
		this.userManager = userManager;
		this.pushNotificationManager = pushNotificationManager;
		this.visitorDetailsManager = visitorDetailsManager;
		this.userPermissionManager = userPermissionManager;
		this.visitorDetailsAddPushNotificationContentBuilder = visitorDetailsAddPushNotificationContentBuilder;
	}

	public void sendVisitorDetailsAddNotificationsAsync(int instituteId, VisitorDetails visitorDetails, int academicSessionId) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendVisitorNotifications(instituteId, visitorDetails, academicSessionId);
			}
		});
		t.start();
	}

	public void sendVisitorNotifications(int instituteId, VisitorDetails visitorDetails, int academicSessionId) {
		if (instituteId <= 0) {
			logger.error("Invalid institute {} ", instituteId);
			return;
		}

		if (visitorDetails == null) {
			return;
		}
		BellNotificationPayload bellNotificationPayload = sendAdminVisitorNotifications(instituteId, visitorDetails.getVisitorId(), visitorDetails);
		if (bellNotificationPayload == null) {
			return;
		}

		boolean notificationAdded = pushNotificationManager.addNotification(bellNotificationPayload);

		if (!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}
	}

	private BellNotificationPayload sendAdminVisitorNotifications(int instituteId, UUID visitorId, VisitorDetails visitorDetails) {
		Set<User> finalUserSet = new HashSet<>();
//		Currently use in STAFF_APPROVAL_PENDING state when we transfer the staff
		if(visitorDetails.getStatus() == VisitorStatus.STAFF_APPROVAL_PENDING ) {
			User raisedFor = userManager.getUser(visitorDetails.getStaff().getStaffId());
			if (raisedFor != null) {
				finalUserSet.add(raisedFor);
			}
		}
//		TODO : Need this when we add notification in add new visitor flow
//		if(visitorDetails.getStatus() == VisitorStatus.APPROVED){
//			List<User> adminUserList = userPermissionManager.getUserByUserPermission(instituteId, new HashSet<>(Arrays.asList(AuthorisationRequiredAction.RECEPTION_NOTIFICATION_PERMISSION)));
//			if (!CollectionUtils.isEmpty(adminUserList)) {
//				for (User user : adminUserList) {
//					if (!user.getUserStatus().equals(UserStatus.ENABLED)) {
//						continue;
//					}
//					finalUserSet.add(user);
//				}
//			}
//		}

		if (CollectionUtils.isEmpty(finalUserSet)) {
			return null;
		}

		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(VISITOR_ID, visitorId == null ? null : visitorId.toString());

		PushNotificationContent adminPushNotificationContent = visitorDetailsAddPushNotificationContentBuilder.getPushNotificationContent();

		UUID finalVisitorId = visitorId;
		prepareAndSendBulkNotificationAsync(instituteId, new ArrayList<>(finalUserSet), adminPushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, ADMIN_ADD_VISITOR);
				dataPayload.put(VISITOR_ID, finalVisitorId == null ? "" : finalVisitorId.toString());
			}
		});

		return new BellNotificationPayload(instituteId, new ArrayList<>(finalUserSet), adminPushNotificationContent.getTitle(), adminPushNotificationContent.getBody(),
				NotificationEntity.ADD_VISITOR, visitorId, metaData);
	}

}
