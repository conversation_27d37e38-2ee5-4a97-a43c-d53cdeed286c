/**
 * 
 */
package com.lernen.cloud.core.api.transport;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class TransportAreaAmountDetailsPayload {

	private int academicSessionId;

	private int areaId;

	private List<TransportVehicleAmountPayload> transportVehicleAmountList;

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public int getAreaId() {
		return areaId;
	}

	public void setAreaId(int areaId) {
		this.areaId = areaId;
	}

	public TransportAreaAmountDetailsPayload() {
	}

	public TransportAreaAmountDetailsPayload(int academicSessionId, int areaId, List<TransportVehicleAmountPayload> transportVehicleAmountList) {
		this.academicSessionId = academicSessionId;
		this.areaId = areaId;
		this.transportVehicleAmountList = transportVehicleAmountList;
	}

	public List<TransportVehicleAmountPayload> getTransportVehicleAmountList() {
		return transportVehicleAmountList;
	}

	public void setTransportVehicleAmountList(List<TransportVehicleAmountPayload> transportVehicleAmountList) {
		this.transportVehicleAmountList = transportVehicleAmountList;
	}

	@Override
	public String toString() {
		return "TransportAreaAmountDetailsPayload{" +
				"academicSessionId=" + academicSessionId +
				", areaId=" + areaId +
				", transportVehicleAmountList=" + transportVehicleAmountList +
				'}';
	}
}
