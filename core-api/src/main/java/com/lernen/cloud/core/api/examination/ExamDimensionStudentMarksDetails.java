package com.lernen.cloud.core.api.examination;

import java.util.List;

public class ExamDimensionStudentMarksDetails {

    private final ExamDimensionValues examDimensionValues;

    private final List<ExamGrade> examGrade;

    private final List<StudentMarksDetails> studentMarksDetailsList;

    public ExamDimensionStudentMarksDetails(ExamDimensionValues examDimensionValues, List<ExamGrade> examGrade, List<StudentMarksDetails> studentMarksDetailsList) {
        this.examDimensionValues = examDimensionValues;
        this.examGrade = examGrade;
        this.studentMarksDetailsList = studentMarksDetailsList;
    }

    public ExamDimensionValues getExamDimensionValues() {
        return examDimensionValues;
    }

    public List<ExamGrade> getExamGrade() {
        return examGrade;
    }

    public List<StudentMarksDetails> getStudentMarksDetailsList() {
        return studentMarksDetailsList;
    }

    @Override
    public String toString() {
        return "ExamDimensionStudentMarksDetails{" +
                "examDimensionValues=" + examDimensionValues +
                ", examGrade=" + examGrade +
                ", studentMarksDetailsList=" + studentMarksDetailsList +
                '}';
    }
}
