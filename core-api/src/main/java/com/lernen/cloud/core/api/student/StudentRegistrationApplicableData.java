package com.lernen.cloud.core.api.student;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentRegistrationApplicableData {

	private String registrationNumber;

	private List<UUID> feeStructureIds;

	public String getRegistrationNumber() {
		return registrationNumber;
	}

	public void setRegistrationNumber(String registrationNumber) {
		this.registrationNumber = registrationNumber;
	}

	public List<UUID> getFeeStructureIds() {
		return feeStructureIds;
	}

	public void setFeeStructureIds(List<UUID> feeStructureIds) {
		this.feeStructureIds = feeStructureIds;
	}

	@Override
	public String toString() {
		return "StudentRegistrationApplicableData [registrationNumber="
				+ registrationNumber + ", feeStructureIds=" + feeStructureIds
				+ "]";
	}

}
