package com.lernen.cloud.core.api.examination;

import java.util.Set;
import java.util.UUID;

import com.embrate.cloud.core.api.hpc.utils.HPCExamType;

public class StudentHPCReportCardStatusPayload {
    private HPCExamType hpcExamType;
    private StudentExamDisplayDataStatus studentExamDisplayDataStatus;
    private Set<UUID> studentIdSet;

    public StudentHPCReportCardStatusPayload(){
        
    }
    
    public StudentHPCReportCardStatusPayload(HPCExamType hpcExamType,
            StudentExamDisplayDataStatus studentExamDisplayDataStatus, Set<UUID> studentIdSet) {
        this.hpcExamType = hpcExamType;
        this.studentExamDisplayDataStatus = studentExamDisplayDataStatus;
        this.studentIdSet = studentIdSet;
    }
    
    public HPCExamType getHpcExamType() {
        return hpcExamType;
    }
    public void setHpcExamType(HPCExamType hpcExamType) {
        this.hpcExamType = hpcExamType;
    }
    public StudentExamDisplayDataStatus getStudentExamDisplayDataStatus() {
        return studentExamDisplayDataStatus;
    }
    public void setStudentExamDisplayDataStatus(StudentExamDisplayDataStatus studentExamDisplayDataStatus) {
        this.studentExamDisplayDataStatus = studentExamDisplayDataStatus;
    }
    public Set<UUID> getStudentIdSet() {
        return studentIdSet;
    }
    public void setStudentIdSet(Set<UUID> studentIdSet) {
        this.studentIdSet = studentIdSet;
    }

    @Override
    public String toString() {
        return "StudentHPCReportCardStatusPayload [hpcExamType=" + hpcExamType + ", studentExamDisplayDataStatus="
                + studentExamDisplayDataStatus + ", studentIdSet=" + studentIdSet + "]";
    }
    
}
