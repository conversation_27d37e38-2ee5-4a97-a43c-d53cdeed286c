package com.lernen.cloud.core.api.examination.report;

import com.lernen.cloud.core.api.examination.ExamReportCardMetadata;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportCardConfiguration {

	private ExamReportCardMetadata examReportCardMetadata;

	private ExamReportStructure examReportStructure;

	public ExamReportCardMetadata getExamReportCardMetadata() {
		return examReportCardMetadata;
	}

	public void setExamReportCardMetadata(
			ExamReportCardMetadata examReportCardMetadata) {
		this.examReportCardMetadata = examReportCardMetadata;
	}

	public ExamReportStructure getExamReportStructure() {
		return examReportStructure;
	}

	public void setExamReportStructure(
			ExamReportStructure examReportStructure) {
		this.examReportStructure = examReportStructure;
	}

	@Override
	public String toString() {
		return "ExamReportCardConfiguration [examReportCardMetadata="
				+ examReportCardMetadata + ", examReportStructure="
				+ examReportStructure + "]";
	}

}
