package com.lernen.cloud.core.api.fees;

import com.lernen.cloud.core.api.institute.AcademicSession;

public class FeeConfigurationResponse {

	private final FeeConfigurationBasicInfo feeConfigurationBasicInfo;

	private final AcademicSession academicSession;

	public FeeConfigurationResponse(FeeConfigurationBasicInfo feeConfigurationBasicInfo,
			AcademicSession academicSession) {
		this.feeConfigurationBasicInfo = feeConfigurationBasicInfo;
		this.academicSession = academicSession;
	}

	public FeeConfigurationBasicInfo getFeeConfigurationBasicInfo() {
		return feeConfigurationBasicInfo;
	}

	public AcademicSession getAcademicSession() {
		return academicSession;
	}

	@Override
	public String toString() {
		return "FeeConfigurationResponse [feeConfigurationBasicInfo=" + feeConfigurationBasicInfo + ", academicSession="
				+ academicSession + "]";
	}

}
