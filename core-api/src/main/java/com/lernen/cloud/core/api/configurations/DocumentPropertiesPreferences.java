package com.lernen.cloud.core.api.configurations;

/**
 * 
 * <AUTHOR>
 *
 */
public class DocumentPropertiesPreferences {

	//Header Font Sizes
	public static final String INSTITUTE_NAME_FONT_SIZE = "institute_name_font_size";
	public static final String LETTER_HEAD_1_FONT_SIZE = "letter_head_1_font_size";
	public static final String LETTER_HEAD_2_FONT_SIZE = "letter_head_2_font_size";

	//Header Font Color
	public static final String INSTITUTE_NAME_COLOR = "institute_name_color";
	public static final String LETTER_HEAD_1_COLOR = "letter_head_1_color";
	public static final String LETTER_HEAD_2_COLOR = "letter_head_2_color";

	//Logos & Student Images Size
	public static final String PRIMARY_LOGO_WIDTH = "primary_logo_width";
	public static final String PRIMARY_LOGO_HEIGHT = "primary_logo_height";
	public static final String PRIMARY_LOGO_XOFFSET = "primary_logo_xoffset";
	public static final String PRIMARY_LOGO_YOFFSET = "primary_logo_yoffset";

	public static final String SECONDARY_LOGO_WIDTH = "secondary_logo_width";
	public static final String SECONDARY_LOGO_HEIGHT = "secondary_logo_height";
	public static final String SECONDARY_LOGO_XOFFSET = "secondary_logo_xoffset";
	public static final String SECONDARY_LOGO_YOFFSET = "secondary_logo_yoffset";

	public static final String WATERMARK_LOGO_WIDTH = "watermark_logo_width";
	public static final String WATERMARK_LOGO_HEIGHT = "watermark_logo_height";
	public static final String WATERMARK_LOGO_XOFFSET = "watermark_logo_xoffset";
	public static final String WATERMARK_LOGO_YOFFSET = "watermark_logo_yoffset";

	public static final String STUDENT_IMAGE_WIDTH = "student_image_width";
	public static final String STUDENT_IMAGE_HEIGHT = "student_image_height";
	public static final String STUDENT_IMAGE_XOFFSET = "student_image_xoffset";
	public static final String STUDENT_IMAGE_YOFFSET = "student_image_yoffset";

	public static final String DOCUMENT_HEADING_FONT_SIZE = "document_heading_font_size";
	public static final String DOCUMENT_CONTENT_FONT_SIZE = "document_content_font_size";

	public static String getConfigType() {
		return "document_properties_preferences";
	}


	private Float instituteNameFontSize;//e.g-12f
	private Float letterHead1FontSize;
	private Float letterHead2FontSize;

	private String instituteNameColor;//e.g"#f9f9f9"
	private String letterHead1Color;
	private String letterHead2Color;

	private Float primaryLogoWidth;
	private Float primaryLogoHeight;
	private Float primaryLogoXOffset;
	private Float primaryLogoYOffset;

	private Float secondaryLogoWidth;
	private Float secondaryLogoHeight;
	private Float secondaryLogoXOffset;
	private Float secondaryLogoYOffset;

	private Float watermarkLogoWidth;
	private Float watermarkLogoHeight;
	private Float watermarkLogoXOffset;
	private Float watermarkLogoYOffset;

	private Float studentImageWidth;
	private Float studentImageHeight;
	private Float studentImageXOffset;
	private Float studentImageYOffset;

	private Float documentHeadingFontSize;
	private Float documentContentFontSize;

	public Float getInstituteNameFontSize() {
		return instituteNameFontSize;
	}

	public void setInstituteNameFontSize(Float instituteNameFontSize) {
		this.instituteNameFontSize = instituteNameFontSize;
	}

	public Float getLetterHead1FontSize() {
		return letterHead1FontSize;
	}

	public void setLetterHead1FontSize(Float letterHead1FontSize) {
		this.letterHead1FontSize = letterHead1FontSize;
	}

	public Float getLetterHead2FontSize() {
		return letterHead2FontSize;
	}

	public void setLetterHead2FontSize(Float letterHead2FontSize) {
		this.letterHead2FontSize = letterHead2FontSize;
	}

	public String getInstituteNameColor() {
		return instituteNameColor;
	}

	public void setInstituteNameColor(String instituteNameColor) {
		this.instituteNameColor = instituteNameColor;
	}

	public String getLetterHead1Color() {
		return letterHead1Color;
	}

	public void setLetterHead1Color(String letterHead1Color) {
		this.letterHead1Color = letterHead1Color;
	}

	public String getLetterHead2Color() {
		return letterHead2Color;
	}

	public void setLetterHead2Color(String letterHead2Color) {
		this.letterHead2Color = letterHead2Color;
	}

	public Float getPrimaryLogoHeight() {
		return primaryLogoHeight;
	}

	public void setPrimaryLogoHeight(Float primaryLogoHeight) {
		this.primaryLogoHeight = primaryLogoHeight;
	}

	public Float getPrimaryLogoWidth() {
		return primaryLogoWidth;
	}

	public void setPrimaryLogoWidth(Float primaryLogoWidth) {
		this.primaryLogoWidth = primaryLogoWidth;
	}

	public Float getSecondaryLogoHeight() {
		return secondaryLogoHeight;
	}

	public void setSecondaryLogoHeight(Float secondaryLogoHeight) {
		this.secondaryLogoHeight = secondaryLogoHeight;
	}

	public Float getSecondaryLogoWidth() {
		return secondaryLogoWidth;
	}

	public void setSecondaryLogoWidth(Float secondaryLogoWidth) {
		this.secondaryLogoWidth = secondaryLogoWidth;
	}

	public Float getWatermarkLogoHeight() {
		return watermarkLogoHeight;
	}

	public void setWatermarkLogoHeight(Float watermarkLogoHeight) {
		this.watermarkLogoHeight = watermarkLogoHeight;
	}

	public Float getWatermarkLogoWidth() {
		return watermarkLogoWidth;
	}

	public void setWatermarkLogoWidth(Float watermarkLogoWidth) {
		this.watermarkLogoWidth = watermarkLogoWidth;
	}

	public Float getStudentImageHeight() {
		return studentImageHeight;
	}

	public void setStudentImageHeight(Float studentImageHeight) {
		this.studentImageHeight = studentImageHeight;
	}

	public Float getStudentImageWidth() {
		return studentImageWidth;
	}

	public void setStudentImageWidth(Float studentImageWidth) {
		this.studentImageWidth = studentImageWidth;
	}

	public Float getPrimaryLogoXOffset() {
		return primaryLogoXOffset;
	}

	public void setPrimaryLogoXOffset(Float primaryLogoXOffset) {
		this.primaryLogoXOffset = primaryLogoXOffset;
	}

	public Float getPrimaryLogoYOffset() {
		return primaryLogoYOffset;
	}

	public void setPrimaryLogoYOffset(Float primaryLogoYOffset) {
		this.primaryLogoYOffset = primaryLogoYOffset;
	}

	public Float getSecondaryLogoXOffset() {
		return secondaryLogoXOffset;
	}

	public void setSecondaryLogoXOffset(Float secondaryLogoXOffset) {
		this.secondaryLogoXOffset = secondaryLogoXOffset;
	}

	public Float getSecondaryLogoYOffset() {
		return secondaryLogoYOffset;
	}

	public void setSecondaryLogoYOffset(Float secondaryLogoYOffset) {
		this.secondaryLogoYOffset = secondaryLogoYOffset;
	}

	public Float getWatermarkLogoXOffset() {
		return watermarkLogoXOffset;
	}

	public void setWatermarkLogoXOffset(Float watermarkLogoXOffset) {
		this.watermarkLogoXOffset = watermarkLogoXOffset;
	}

	public Float getWatermarkLogoYOffset() {
		return watermarkLogoYOffset;
	}

	public void setWatermarkLogoYOffset(Float watermarkLogoYOffset) {
		this.watermarkLogoYOffset = watermarkLogoYOffset;
	}

	public Float getStudentImageXOffset() {
		return studentImageXOffset;
	}

	public void setStudentImageXOffset(Float studentImageXOffset) {
		this.studentImageXOffset = studentImageXOffset;
	}

	public Float getStudentImageYOffset() {
		return studentImageYOffset;
	}

	public void setStudentImageYOffset(Float studentImageYOffset) {
		this.studentImageYOffset = studentImageYOffset;
	}

	public Float getDocumentHeadingFontSize() {
		return documentHeadingFontSize;
	}

	public void setDocumentHeadingFontSize(Float documentHeadingFontSize) {
		this.documentHeadingFontSize = documentHeadingFontSize;
	}

	public Float getDocumentContentFontSize() {
		return documentContentFontSize;
	}

	public void setDocumentContentFontSize(Float documentContentFontSize) {
		this.documentContentFontSize = documentContentFontSize;
	}

	@Override
	public String toString() {
		return "DocumentPropertiesPreferences{" +
				"instituteNameFontSize=" + instituteNameFontSize +
				", letterHead1FontSize=" + letterHead1FontSize +
				", letterHead2FontSize=" + letterHead2FontSize +
				", instituteNameColor='" + instituteNameColor + '\'' +
				", letterHead1Color='" + letterHead1Color + '\'' +
				", letterHead2Color='" + letterHead2Color + '\'' +
				", primaryLogoWidth=" + primaryLogoWidth +
				", primaryLogoHeight=" + primaryLogoHeight +
				", primaryLogoXOffset=" + primaryLogoXOffset +
				", primaryLogoYOffset=" + primaryLogoYOffset +
				", secondaryLogoWidth=" + secondaryLogoWidth +
				", secondaryLogoHeight=" + secondaryLogoHeight +
				", secondaryLogoXOffset=" + secondaryLogoXOffset +
				", secondaryLogoYOffset=" + secondaryLogoYOffset +
				", watermarkLogoWidth=" + watermarkLogoWidth +
				", watermarkLogoHeight=" + watermarkLogoHeight +
				", watermarkLogoXOffset=" + watermarkLogoXOffset +
				", watermarkLogoYOffset=" + watermarkLogoYOffset +
				", studentImageWidth=" + studentImageWidth +
				", studentImageHeight=" + studentImageHeight +
				", studentImageXOffset=" + studentImageXOffset +
				", studentImageYOffset=" + studentImageYOffset +
				", documentHeadingFontSize=" + documentHeadingFontSize +
				", documentContentFontSize=" + documentContentFontSize +
				'}';
	}
}
