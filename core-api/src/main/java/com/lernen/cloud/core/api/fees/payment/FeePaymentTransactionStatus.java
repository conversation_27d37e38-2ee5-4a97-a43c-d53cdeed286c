package com.lernen.cloud.core.api.fees.payment;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public enum FeePaymentTransactionStatus {
	ACTIVE, CANCELLED;
	
	public static FeePaymentTransactionStatus getFeePaymentTransactionStatus(String feePaymentTransactionStatus){
		if(StringUtils.isBlank(feePaymentTransactionStatus)){
			return null;
		}
		for(FeePaymentTransactionStatus feePaymentTransactionStatusEnum : FeePaymentTransactionStatus.values()){
			if(feePaymentTransactionStatusEnum.name().equalsIgnoreCase(feePaymentTransactionStatus)){
				return feePaymentTransactionStatusEnum;
			}
		}
		return null;
	}
}
