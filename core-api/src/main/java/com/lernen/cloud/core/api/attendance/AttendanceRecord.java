/**
 * 
 */
package com.lernen.cloud.core.api.attendance;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class AttendanceRecord {
	
	private AttendanceType attendanceType;

	private AttendanceStatus attendanceStatus;

	private int attendanceDateTS;
	
	private String remarks;

	private UUID createdBy;

	private UUID updatedBy;

	private Integer createdAt;

	private Integer updatedAt;

	/**
	 * @param attendanceType
	 * @param attendanceStatus
	 * @param attendanceDateTS
	 * @param remarks
	 * @param createdBy
	 * @param updatedBy
	 * @param createdAt
	 * @param updatedAt
	 */
	public AttendanceRecord(AttendanceType attendanceType, AttendanceStatus attendanceStatus, int attendanceDateTS,
			String remarks, UUID createdBy, UUID updatedBy, Integer createdAt, Integer updatedAt) {
		this.attendanceType = attendanceType;
		this.attendanceStatus = attendanceStatus;
		this.attendanceDateTS = attendanceDateTS;
		this.remarks = remarks;
		this.createdBy = createdBy;
		this.updatedBy = updatedBy;
		this.createdAt = createdAt;
		this.updatedAt = updatedAt;
	}

	/**
	 * @return the attendanceType
	 */
	public AttendanceType getAttendanceType() {
		return attendanceType;
	}

	/**
	 * @param attendanceType the attendanceType to set
	 */
	public void setAttendanceType(AttendanceType attendanceType) {
		this.attendanceType = attendanceType;
	}

	/**
	 * @return the attendanceStatus
	 */
	public AttendanceStatus getAttendanceStatus() {
		return attendanceStatus;
	}

	/**
	 * @param attendanceStatus the attendanceStatus to set
	 */
	public void setAttendanceStatus(AttendanceStatus attendanceStatus) {
		this.attendanceStatus = attendanceStatus;
	}

	/**
	 * @return the remarks
	 */
	public String getRemarks() {
		return remarks;
	}

	/**
	 * @param remarks the remarks to set
	 */
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	/**
	 * @return the createdBy
	 */
	public UUID getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(UUID createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public UUID getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(UUID updatedBy) {
		this.updatedBy = updatedBy;
	}

	/**
	 * @return the createdAt
	 */
	public Integer getCreatedAt() {
		return createdAt;
	}

	/**
	 * @param createdAt the createdAt to set
	 */
	public void setCreatedAt(Integer createdAt) {
		this.createdAt = createdAt;
	}

	/**
	 * @return the updatedAt
	 */
	public Integer getUpdatedAt() {
		return updatedAt;
	}

	/**
	 * @param updatedAt the updatedAt to set
	 */
	public void setUpdatedAt(Integer updatedAt) {
		this.updatedAt = updatedAt;
	}

	/**
	 * @return the attendanceDateTS
	 */
	public int getAttendanceDateTS() {
		return attendanceDateTS;
	}

	/**
	 * @param attendanceDateTS the attendanceDateTS to set
	 */
	public void setAttendanceDateTS(int attendanceDateTS) {
		this.attendanceDateTS = attendanceDateTS;
	}

	@Override
	public String toString() {
		return "AttendanceRecord [attendanceType=" + attendanceType + ", attendanceStatus=" + attendanceStatus
				+ ", attendanceDateTS=" + attendanceDateTS + ", remarks=" + remarks + ", createdBy=" + createdBy
				+ ", updatedBy=" + updatedBy + ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + "]";
	}
}
