package com.lernen.cloud.core.api.configurations;

import com.lernen.cloud.core.api.attendance.AttendanceStatus;

public class StudentAttendancePreferences {

    private final boolean biometricEnabled;

    private final AttendanceStatus defaultAttendanceStatus;

    public StudentAttendancePreferences(boolean biometricEnabled, AttendanceStatus defaultAttendanceStatus) {
        this.biometricEnabled = biometricEnabled;
        this.defaultAttendanceStatus = defaultAttendanceStatus;
    }

    public boolean isBiometricEnabled() {
        return biometricEnabled;
    }

    public AttendanceStatus getDefaultAttendanceStatus() {
        return defaultAttendanceStatus;
    }

    @Override
    public String toString() {
        return "StudentAttendancePreferences{" +
                "biometricEnabled=" + biometricEnabled +
                ", defaultAttendanceStatus=" + defaultAttendanceStatus +
                '}';
    }
}
