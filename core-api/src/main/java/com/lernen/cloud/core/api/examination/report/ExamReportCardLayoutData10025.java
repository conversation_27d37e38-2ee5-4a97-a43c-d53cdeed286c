package com.lernen.cloud.core.api.examination.report;

import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportCardLayoutData10025 extends ExamReportCardLayoutData {

	private final byte[] saraswatiImage;

	private final Float saraswatiImageWidth;

	private final Float saraswatiImageHeight;

	public ExamReportCardLayoutData10025(Document document,
			DocumentLayoutSetup documentLayoutSetup, PdfFont pdfFont,
			PdfFont hindiPdfFont, Float contentFontSize,
			Float defaultBorderWidth, Float logoWidth, Float logoHeight,
			byte[] logo, byte[] saraswatiImage, Float saraswatiImageWidth,
			Float saraswatiImageHeight) {
		super(document, documentLayoutSetup, pdfFont, hindiPdfFont,
				contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
				logo);
		this.saraswatiImage = saraswatiImage;
		this.saraswatiImageWidth = saraswatiImageWidth;
		this.saraswatiImageHeight = saraswatiImageHeight;
	}

	public byte[] getSaraswatiImage() {
		return saraswatiImage;
	}

	public Float getSaraswatiImageWidth() {
		return saraswatiImageWidth;
	}

	public Float getSaraswatiImageHeight() {
		return saraswatiImageHeight;
	}

}
