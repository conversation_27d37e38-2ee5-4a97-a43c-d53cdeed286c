package com.lernen.cloud.core.api.library;

import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.user.DataUpdationAction;

public class UpdateCopiesPayload {

    private UUID bookId;
    private int numberOfCopies;
    private List<IndividualBookDetailsPayload> individualBookDetailsPayloads;
    private List<UUID> accessionIdList; 
    private DataUpdationAction dataUpdationAction;

    public UpdateCopiesPayload(){

    }

    public UpdateCopiesPayload(UUID bookId, int numberOfCopies,
            List<IndividualBookDetailsPayload> individualBookDetailsPayloads, List<UUID> accessionIdList,
            DataUpdationAction dataUpdationAction) {
        this.bookId = bookId;
        this.numberOfCopies = numberOfCopies;
        this.individualBookDetailsPayloads = individualBookDetailsPayloads;
        this.accessionIdList = accessionIdList;
        this.dataUpdationAction = dataUpdationAction;
    }

    public UUID getBookId() {
        return bookId;
    }

    public void setBookId(UUID bookId) {
        this.bookId = bookId;
    }

    public int getNumberOfCopies() {
        return numberOfCopies;
    }

    public void setNumberOfCopies(int numberOfCopies) {
        this.numberOfCopies = numberOfCopies;
    }

    public List<IndividualBookDetailsPayload> getIndividualBookDetailsPayloads() {
        return individualBookDetailsPayloads;
    }

    public void setIndividualBookDetailsPayloads(List<IndividualBookDetailsPayload> individualBookDetailsPayloads) {
        this.individualBookDetailsPayloads = individualBookDetailsPayloads;
    }

    public List<UUID> getAccessionIdList() {
        return accessionIdList;
    }

    public void setAccessionIdList(List<UUID> accessionIdList) {
        this.accessionIdList = accessionIdList;
    }

    public DataUpdationAction getDataUpdationAction() {
        return dataUpdationAction;
    }

    public void setDataUpdationAction(DataUpdationAction dataUpdationAction) {
        this.dataUpdationAction = dataUpdationAction;
    }

    @Override
    public String toString() {
        return "UpdateCopiesPayload [bookId=" + bookId
                + ", numberOfCopies=" + numberOfCopies + ", individualBookDetailsPayloads="
                + individualBookDetailsPayloads + ", accessionIdList=" + accessionIdList + ", dataUpdationAction="
                + dataUpdationAction + "]";
    }
    
}
