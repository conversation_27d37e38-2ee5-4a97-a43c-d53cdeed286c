package com.lernen.cloud.core.api.attendance.staff;

import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffTimingDetails;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class StaffAttendanceDetails {

    private int instituteId;

    private Integer attendanceDate;

    private Staff staff;

    private Double totalDuration;

    private StaffAttendanceStatus staffAttendanceStatus;

    private StaffTimingDetails staffTimingDetails;

    private Map<StaffAttendanceType, List<StaffAttendanceTimeDetails>> staffAttendanceTimeTypeMap;

    private StaffAttendanceType nextStaffAttendanceType;

    public StaffAttendanceDetails(int instituteId, Integer attendanceDate, Staff staff, Double totalDuration, StaffAttendanceStatus staffAttendanceStatus, StaffTimingDetails staffTimingDetails, Map<StaffAttendanceType, List<StaffAttendanceTimeDetails>> staffAttendanceTimeTypeMap) {
        this.instituteId = instituteId;
        this.attendanceDate = attendanceDate;
        this.staff = staff;
        this.totalDuration = totalDuration;
        this.staffAttendanceStatus = staffAttendanceStatus;
        this.staffTimingDetails = staffTimingDetails;
        this.staffAttendanceTimeTypeMap = staffAttendanceTimeTypeMap;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public Integer getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(Integer attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public Staff getStaff() {
        return staff;
    }

    public void setStaff(Staff staff) {
        this.staff = staff;
    }

    public Double getTotalDuration() {
        return totalDuration;
    }

    public void setTotalDuration(Double totalDuration) {
        this.totalDuration = totalDuration;
    }

    public StaffAttendanceStatus getStaffAttendanceStatus() {
        return staffAttendanceStatus;
    }

    public void setStaffAttendanceStatus(StaffAttendanceStatus staffAttendanceStatus) {
        this.staffAttendanceStatus = staffAttendanceStatus;
    }

    public StaffTimingDetails getStaffTimingDetails() {
        return staffTimingDetails;
    }

    public void setStaffTimingDetails(StaffTimingDetails staffTimingDetails) {
        this.staffTimingDetails = staffTimingDetails;
    }

    public Map<StaffAttendanceType, List<StaffAttendanceTimeDetails>> getStaffAttendanceTimeTypeMap() {
        return staffAttendanceTimeTypeMap;
    }

    public void setStaffAttendanceTimeTypeMap(Map<StaffAttendanceType, List<StaffAttendanceTimeDetails>> staffAttendanceTimeTypeMap) {
        this.staffAttendanceTimeTypeMap = staffAttendanceTimeTypeMap;
    }

    public StaffAttendanceType getNextStaffAttendanceType() {
        return nextStaffAttendanceType;
    }

    public void setNextStaffAttendanceType(StaffAttendanceType nextStaffAttendanceType) {
        this.nextStaffAttendanceType = nextStaffAttendanceType;
    }

    @Override
    public String toString() {
        return "StaffAttendanceDetails{" +
                "instituteId=" + instituteId +
                ", attendanceDate=" + attendanceDate +
                ", staff=" + staff +
                ", totalDuration=" + totalDuration +
                ", staffAttendanceStatus=" + staffAttendanceStatus +
                ", staffTimingDetails=" + staffTimingDetails +
                ", staffAttendanceTimeTypeMap=" + staffAttendanceTimeTypeMap +
                ", nextStaffAttendanceType=" + nextStaffAttendanceType +
                '}';
    }
}
