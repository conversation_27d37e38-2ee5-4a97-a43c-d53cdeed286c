package com.lernen.cloud.core.api.fees.payment;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeePaymentSummary {
	private final UUID studentId;
	
	private final int instituteId;
	
	private final UUID feeId;
	 
	private final List<FeeHeadPaymentData> feeHeadPaymentDataList;

	public FeePaymentSummary(UUID studentId, int instituteId, UUID feeId,
			List<FeeHeadPaymentData> feeHeadPaymentDataList) {
		this.studentId = studentId;
		this.instituteId = instituteId;
		this.feeId = feeId;
		this.feeHeadPaymentDataList = feeHeadPaymentDataList;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public UUID getFeeId() {
		return feeId;
	}

	public List<FeeHeadPaymentData> getFeeHeadPaymentDataList() {
		return feeHeadPaymentDataList;
	}

	@Override
	public String toString() {
		return "FeePaymentDetails [studentId=" + studentId + ", instituteId=" + instituteId + ", feeId=" + feeId
				+ ", feeHeadPaymentDataList=" + feeHeadPaymentDataList + "]";
	}
	
	
}
