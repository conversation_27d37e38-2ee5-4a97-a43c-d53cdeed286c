package com.lernen.cloud.core.api.transport;

public class TransportAreaDetail {
	
	private TransportArea transportArea;
	private TransportParentArea transportParentArea;
	
	public TransportAreaDetail(TransportArea transportArea, TransportParentArea transportParentArea) {
		this.transportArea = transportArea;
		this.transportParentArea = transportParentArea;	
	}
	
	public TransportAreaDetail() {
	}

	public void setTransportArea(TransportArea transportArea) {
		this.transportArea = transportArea;
	}

	public TransportArea getTransportArea() {
		return transportArea;
	}

	public TransportParentArea getTransportParentArea() {
		return transportParentArea;
	}

	public void setTransportParentArea(TransportParentArea transportParentArea) {
		this.transportParentArea = transportParentArea;
	}


	@Override
	public String toString() {
		return "TransportAreaDetail [transportArea=" + transportArea + ", transportParentArea=" + transportParentArea +"]";
	}
}
