package com.lernen.cloud.core.api.examination.report;

import java.util.List;

/**
 * <AUTHOR>
 */

public class ExamReportResultConfigs {

    private String resultCalculatorId;

    private List<ExamResultComputationGroup> resultComputationGroupList;

    private Integer failedCourseThreshold;
    private Double graceMarks;

    public ExamReportResultConfigs() {
    }

    public ExamReportResultConfigs(String resultCalculatorId, List<ExamResultComputationGroup> resultComputationGroupList) {
        this.resultCalculatorId = resultCalculatorId;
        this.resultComputationGroupList = resultComputationGroupList;
    }

    public String getResultCalculatorId() {
        return resultCalculatorId;
    }

    public void setResultCalculatorId(String resultCalculatorId) {
        this.resultCalculatorId = resultCalculatorId;
    }

    public List<ExamResultComputationGroup> getResultComputationGroupList() {
        return resultComputationGroupList;
    }

    public void setResultComputationGroupList(List<ExamResultComputationGroup> resultComputationGroupList) {
        this.resultComputationGroupList = resultComputationGroupList;
    }

    public Integer getFailedCourseThreshold() {
        return failedCourseThreshold;
    }

    public void setFailedCourseThreshold(Integer failedCourseThreshold) {
        this.failedCourseThreshold = failedCourseThreshold;
    }

    public Double getGraceMarks() {
        return graceMarks;
    }

    public void setGraceMarks(Double graceMarks) {
        this.graceMarks = graceMarks;
    }

    @Override
    public String toString() {
        return "ExamReportResultConfigs{" +
                "resultCalculatorId='" + resultCalculatorId + '\'' +
                ", resultComputationGroupList=" + resultComputationGroupList +
                ", failedCourseThreshold=" + failedCourseThreshold +
                ", graceMarks=" + graceMarks +
                '}';
    }
}
