package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.institute.StandardSections;

/**
 * <AUTHOR>
 * @created_at 11/11/23 : 10:49
 **/
public class SectionMarksStatusDetails {

    private final StandardSections standardSections;

    private final MarksFeedStatus marksFeedStatus;

    public SectionMarksStatusDetails(StandardSections standardSections, MarksFeedStatus marksFeedStatus) {
        this.standardSections = standardSections;
        this.marksFeedStatus = marksFeedStatus;
    }

    public StandardSections getStandardSections() {
        return standardSections;
    }

    public MarksFeedStatus getMarksFeedStatus() {
        return marksFeedStatus;
    }

    @Override
    public String toString() {
        return "SectionMarksStatusDetails{" +
                "standardSections=" + standardSections +
                ", marksFeedStatus=" + marksFeedStatus +
                '}';
    }
}
