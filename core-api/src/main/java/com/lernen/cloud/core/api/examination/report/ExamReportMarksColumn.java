package com.lernen.cloud.core.api.examination.report;

import com.lernen.cloud.core.api.examination.ExamDimensionValues;
import com.lernen.cloud.core.api.examination.ExamMetaData;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportMarksColumn<T extends ExamDimensionValues> {

	private final String id;

	private final String title;

	private final String subTitle;

	private final String colorHexCode;

	private final boolean displayTotalMarks;

	private final ExamReportGridColumnType examReportGridColumnType;

	private final Boolean roundOff;

	private final ExamMetaData examMetaData;

	private final List<ExamReportMarksColumn<T>> childExamReportMarksColumns;

	private final List<T> examDimensionValues;

	private final boolean hideTotalDimensionMaxMarks;
	private final boolean hideRemainingDimensionMaxMarks;

	private final String courseWiseClassMetricsComputeColumn;

	private final ExamReportCourseGainColumns courseWiseGainMetricsComputeColumns;

	// Only used where data is not coming from {@examDimensionObtainedValuesList} column
	// Like for STATIC/RANK etc columns
	private String columnValue;

	private int rowSpan;

	private int colSpan;

	private int x;

	private int y;

	private boolean hide;

	public ExamReportMarksColumn(String id, String title, String subTitle, String colorHexCode, boolean displayTotalMarks,
								 ExamReportGridColumnType examReportGridColumnType, Boolean roundOff,
								 ExamMetaData examMetaData, List<ExamReportMarksColumn<T>> childExamReportMarksColumns,
								 List<T> examDimensionValues, boolean hideTotalDimensionMaxMarks,
								 boolean hideRemainingDimensionMaxMarks, String courseWiseClassMetricsComputeColumn,
								 ExamReportCourseGainColumns courseWiseGainMetricsComputeColumns, String columnValue, boolean hide) {
		this.id = id;
		this.title = title;
		this.subTitle = subTitle;
		this.colorHexCode = colorHexCode;
		this.displayTotalMarks = displayTotalMarks;
		this.examReportGridColumnType = examReportGridColumnType;
		this.roundOff = roundOff;
		this.examMetaData = examMetaData;
		this.childExamReportMarksColumns = childExamReportMarksColumns;
		this.examDimensionValues = examDimensionValues;
		this.hideTotalDimensionMaxMarks = hideTotalDimensionMaxMarks;
		this.hideRemainingDimensionMaxMarks = hideRemainingDimensionMaxMarks;
		this.courseWiseClassMetricsComputeColumn = courseWiseClassMetricsComputeColumn;
		this.courseWiseGainMetricsComputeColumns = courseWiseGainMetricsComputeColumns;
		this.columnValue = columnValue;
		this.hide = hide;
	}

	public String getId() {
		return id;
	}

	public String getTitle() {
		return title;
	}

	public String getSubTitle() {
		return subTitle;
	}

	public String getColorHexCode() {
		return colorHexCode;
	}

	public boolean isDisplayTotalMarks() {
		return displayTotalMarks;
	}

	public ExamReportGridColumnType getExamReportGridColumnType() {
		return examReportGridColumnType;
	}

	public ExamMetaData getExamMetaData() {
		return examMetaData;
	}

	public List<ExamReportMarksColumn<T>> getChildExamReportMarksColumns() {
		return childExamReportMarksColumns;
	}

	public List<T> getExamDimensionValues() {
		return examDimensionValues;
	}

	public int getRowSpan() {
		return rowSpan;
	}

	public void setRowSpan(int rowSpan) {
		this.rowSpan = rowSpan;
	}

	public int getColSpan() {
		return colSpan;
	}

	public void setColSpan(int colSpan) {
		this.colSpan = colSpan;
	}

	public int getX() {
		return x;
	}

	public void setX(int x) {
		this.x = x;
	}

	public int getY() {
		return y;
	}

	public void setY(int y) {
		this.y = y;
	}

	public Boolean getRoundOff() {
		return roundOff;
	}

	public String getCourseWiseClassMetricsComputeColumn() {
		return courseWiseClassMetricsComputeColumn;
	}

	public ExamReportCourseGainColumns getCourseWiseGainMetricsComputeColumns() {
		return courseWiseGainMetricsComputeColumns;
	}

	public String getColumnValue() {
		return columnValue;
	}

	public void setColumnValue(String columnValue) {
		this.columnValue = columnValue;
	}

	public boolean isHide() {
		return hide;
	}

	public void setHide(boolean hide) {
		this.hide = hide;
	}

	public boolean isHideTotalDimensionMaxMarks() {
		return hideTotalDimensionMaxMarks;
	}

	public boolean isHideRemainingDimensionMaxMarks() {
		return hideRemainingDimensionMaxMarks;
	}

	@Override
	public String toString() {
		return "ExamReportMarksColumn{" +
				"id='" + id + '\'' +
				", title='" + title + '\'' +
				", subTitle='" + subTitle + '\'' +
				", colorHexCode='" + colorHexCode + '\'' +
				", displayTotalMarks=" + displayTotalMarks +
				", examReportGridColumnType=" + examReportGridColumnType +
				", roundOff=" + roundOff +
				", examMetaData=" + examMetaData +
				", childExamReportMarksColumns=" + childExamReportMarksColumns +
				", examDimensionValues=" + examDimensionValues +
				", hideTotalDimensionMaxMarks=" + hideTotalDimensionMaxMarks +
				", hideRemainingDimensionMaxMarks=" + hideRemainingDimensionMaxMarks +
				", courseWiseClassMetricsComputeColumn='" + courseWiseClassMetricsComputeColumn + '\'' +
				", courseWiseGainMetricsComputeColumns=" + courseWiseGainMetricsComputeColumns +
				", columnValue='" + columnValue + '\'' +
				", rowSpan=" + rowSpan +
				", colSpan=" + colSpan +
				", x=" + x +
				", y=" + y +
				", hide=" + hide +
				'}';
	}

}
