package com.lernen.cloud.core.api.student;

import java.util.UUID;

public class StudentSiblingDetailsRow {

    private final int instituteId;

    private final UUID siblingGroupId;

    private final StudentLite student;

    public StudentSiblingDetailsRow(int instituteId, UUID siblingGroupId, StudentLite student) {
        this.instituteId = instituteId;
        this.siblingGroupId = siblingGroupId;
        this.student = student;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public UUID getSiblingGroupId() {
        return siblingGroupId;
    }

    public StudentLite getStudent() {
        return student;
    }

    @Override
    public String toString() {
        return "StudentSiblingDetailsRow{" +
                "instituteId=" + instituteId +
                ", siblingGroupId=" + siblingGroupId +
                ", student=" + student +
                '}';
    }
}
