package com.lernen.cloud.core.api.pdf;

import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.VerticalAlignment;

/**
 * 
 * <AUTHOR>
 *
 */
public class CellLayoutSetup {

	private Float height;
	private Float width;

	private PdfFont pdfFont;

	private Float fontSize;

	private Border borderLeft;

	private Border borderRight;

	private Border borderTop;

	private Border borderBottom;

	private Float paddingTop;

	private Float paddingBottom;

	private Float paddingLeft;

	private Float paddingRight;

	private TextAlignment textAlignment;

	private VerticalAlignment verticalAlignment;

	private String backgroundColor;

	private float rotationAngle;

	public CellLayoutSetup() {

	}

	public CellLayoutSetup(Float width, PdfFont pdfFont, Float fontSize,
			Border borderLeft, Border borderRight, Border borderTop,
			Border borderBottom, Float paddingTop, Float paddingBottom,
			Float paddingLeft, Float paddingRight, TextAlignment textAlignment,
			VerticalAlignment verticalAlignment, String backgroundColor) {
		this.width = width;
		this.pdfFont = pdfFont;
		this.fontSize = fontSize;
		this.borderLeft = borderLeft;
		this.borderRight = borderRight;
		this.borderTop = borderTop;
		this.borderBottom = borderBottom;
		this.paddingTop = paddingTop;
		this.paddingBottom = paddingBottom;
		this.paddingLeft = paddingLeft;
		this.paddingRight = paddingRight;
		this.textAlignment = textAlignment;
		this.verticalAlignment = verticalAlignment;
		this.backgroundColor = backgroundColor;
	}

	public CellLayoutSetup(Float width, PdfFont pdfFont,
			TextAlignment textAlignment) {
		this.width = width;
		this.pdfFont = pdfFont;
		this.textAlignment = textAlignment;
	}

	public Float getHeight() {
		return height;
	}

	public CellLayoutSetup setHeight(Float height) {
		this.height = height;
		return this;
	}

	public Float getWidth() {
		return width;
	}

	public CellLayoutSetup setWidth(Float width) {
		this.width = width;
		return this;
	}

	public PdfFont getPdfFont() {
		return pdfFont;
	}

	public CellLayoutSetup setPdfFont(PdfFont pdfFont) {
		this.pdfFont = pdfFont;
		return this;
	}

	public Float getFontSize() {
		return fontSize;
	}

	public CellLayoutSetup setFontSize(Float fontSize) {
		this.fontSize = fontSize;
		return this;
	}

	public Border getBorderLeft() {
		return borderLeft;
	}

	public CellLayoutSetup setBorder(Border border) {
		this.borderLeft = border;
		this.borderRight = border;
		this.borderTop = border;
		this.borderBottom = border;
		return this;
	}

	public CellLayoutSetup setBorderLeft(Border borderLeft) {
		this.borderLeft = borderLeft;
		return this;
	}

	public Border getBorderRight() {
		return borderRight;
	}

	public CellLayoutSetup setBorderRight(Border borderRight) {
		this.borderRight = borderRight;
		return this;
	}

	public Border getBorderTop() {
		return borderTop;
	}

	public CellLayoutSetup setBorderTop(Border borderTop) {
		this.borderTop = borderTop;
		return this;
	}

	public Border getBorderBottom() {
		return borderBottom;
	}

	public CellLayoutSetup setBorderBottom(Border borderBottom) {
		this.borderBottom = borderBottom;
		return this;
	}

	public CellLayoutSetup setPadding(Float padding) {
		this.paddingBottom = padding;
		this.paddingTop = padding;
		this.paddingLeft = padding;
		this.paddingRight = padding;
		return this;
	}

	public Float getPaddingTop() {
		return paddingTop;
	}

	public CellLayoutSetup setPaddingTop(Float paddingTop) {
		this.paddingTop = paddingTop;
		return this;
	}

	public Float getPaddingBottom() {
		return paddingBottom;
	}

	public CellLayoutSetup setPaddingBottom(Float paddingBottom) {
		this.paddingBottom = paddingBottom;
		return this;
	}

	public Float getPaddingLeft() {
		return paddingLeft;
	}

	public CellLayoutSetup setPaddingLeft(Float paddingLeft) {
		this.paddingLeft = paddingLeft;
		return this;
	}

	public Float getPaddingRight() {
		return paddingRight;
	}

	public CellLayoutSetup setPaddingRight(Float paddingRight) {
		this.paddingRight = paddingRight;
		return this;
	}

	public TextAlignment getTextAlignment() {
		return textAlignment;
	}

	public CellLayoutSetup setTextAlignment(TextAlignment textAlignment) {
		this.textAlignment = textAlignment;
		return this;
	}

	public VerticalAlignment getVerticalAlignment() {
		return verticalAlignment;
	}

	public CellLayoutSetup setVerticalAlignment(
			VerticalAlignment verticalAlignment) {
		this.verticalAlignment = verticalAlignment;
		return this;
	}

	public String getBackgroundColor() {
		return backgroundColor;
	}

	public CellLayoutSetup setBackgroundColor(String backgroundColor) {
		this.backgroundColor = backgroundColor;
		return this;
	}

	public float getRotationAngle() {
		return rotationAngle;
	}

	public CellLayoutSetup setRotationAngle(float rotationAngle) {
		this.rotationAngle = rotationAngle;
		return this;
	}

	public CellLayoutSetup copy() {
		return new CellLayoutSetup(width, pdfFont, fontSize, borderLeft,
				borderRight, borderTop, borderBottom, paddingTop, paddingBottom,
				paddingLeft, paddingRight, textAlignment, verticalAlignment,
				backgroundColor);
	}
}
