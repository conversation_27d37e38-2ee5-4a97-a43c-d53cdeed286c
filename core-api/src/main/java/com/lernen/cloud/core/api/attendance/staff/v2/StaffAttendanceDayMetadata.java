package com.lernen.cloud.core.api.attendance.staff.v2;

import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceStatus;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceType;
import com.lernen.cloud.core.api.institute.Time;
import com.lernen.cloud.core.api.staff.StaffTimingDetails;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */

public class StaffAttendanceDayMetadata {

    private StaffTimingDetails staffTimingDetails;

    private List<StaffTimeDuration> staffTimeDurationList;

    private StaffAttendanceType lastAttendanceType;


    public StaffAttendanceDayMetadata() {
    }

    public StaffAttendanceDayMetadata(StaffTimingDetails staffTimingDetails, List<StaffTimeDuration> staffTimeDurationList, StaffAttendanceType lastAttendanceType) {
        this.staffTimingDetails = staffTimingDetails;
        this.staffTimeDurationList = staffTimeDurationList;
        this.lastAttendanceType = lastAttendanceType;
    }

    public StaffTimingDetails getStaffTimingDetails() {
        return staffTimingDetails;
    }

    public void setStaffTimingDetails(StaffTimingDetails staffTimingDetails) {
        this.staffTimingDetails = staffTimingDetails;
    }

    public List<StaffTimeDuration> getStaffTimeDurationList() {
        return staffTimeDurationList;
    }

    public void setStaffTimeDurationList(List<StaffTimeDuration> staffTimeDurationList) {
        this.staffTimeDurationList = staffTimeDurationList;
    }

    public StaffAttendanceType getLastAttendanceType() {
        return lastAttendanceType;
    }

    public void setLastAttendanceType(StaffAttendanceType lastAttendanceType) {
        this.lastAttendanceType = lastAttendanceType;
    }

    public Time getLastLoggedTime(){
        List<StaffTimeDuration> staffTimeDurationList = getStaffTimeDurationList();
        if(CollectionUtils.isEmpty(staffTimeDurationList)){
            return null;
        }
        StaffTimeDuration staffTimeDuration = staffTimeDurationList.get(staffTimeDurationList.size() - 1);
        if(staffTimeDuration.getOutTime() != null){
            return staffTimeDuration.getOutTime();
        }
        return staffTimeDuration.getInTime();
    }

    @Override
    public String toString() {
        return "StaffAttendanceDayMetadata{" +
                "staffTimingDetails=" + staffTimingDetails +
                ", staffTimeDurationList=" + staffTimeDurationList +
                ", lastAttendanceType=" + lastAttendanceType +
                '}';
    }
}
