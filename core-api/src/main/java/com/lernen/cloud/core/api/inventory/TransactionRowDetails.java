package com.lernen.cloud.core.api.inventory;

import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.student.StudentLite;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransactionRowDetails extends TransactionRow {

	private final String transactionToName;

	private final String transactionByName;

	private final Vendor vendor;

	private final StudentLite studentLite;

	public TransactionRowDetails(int instituteId, UUID transactionId,
			String reference, String email, UUID skuId, String productName,
			InventoryTransactionType transactionType,
			InventoryUserType inventoryUserType, double quantity,
			double totalPrice, double totalDiscount, double totalTax,
			double initialQuantity, double finalQuantity, String transactionTo,
			long transactionDate, long transactionAddedAt, String transactionBy,
			PaymentStatus paymentStatus, String description,
			double additionalCost, double additionalDiscount,
			String transactionToName, String transactionByName, Vendor vendor,
			TransactionMode transactionMode,
			InventoryTransactionStatus inventoryTransactionStatus,
			StudentLite studentLite,  Double usedWalletAmount,
								 Double walletCreditAmount, Double paidAmount, Map<String, Object> metadata) {
		super(instituteId, transactionId, reference, email, skuId, productName,
				transactionType, inventoryUserType, quantity, totalPrice,
				totalDiscount, totalTax, initialQuantity, finalQuantity,
				transactionTo, transactionDate, transactionAddedAt,
				transactionBy, paymentStatus, description, additionalCost,
				additionalDiscount, transactionMode,
				inventoryTransactionStatus, usedWalletAmount, walletCreditAmount, paidAmount, metadata);
		this.transactionToName = transactionToName;
		this.transactionByName = transactionByName;
		this.vendor = vendor;
		this.studentLite = studentLite;
	}

	public String getTransactionToName() {
		return transactionToName;
	}

	public String getTransactionByName() {
		return transactionByName;
	}

	public Vendor getVendor() {
		return vendor;
	}

	public StudentLite getStudentLite() {
		return studentLite;
	}

}
