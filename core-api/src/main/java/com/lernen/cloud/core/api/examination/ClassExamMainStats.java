package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.institute.Standard;

/**
 * 
 * <AUTHOR>
 *
 */
public class ClassExamMainStats {

	private final Standard standard;

	private Double maxPercentage;

	private Double minPercentage;

	private Double avgPercentage;

	public ClassExamMainStats(Standard standard) {
		this.standard = standard;
	}

	public ClassExamMainStats(Standard standard, Double maxPercentage, Double minPercentage, Double avgPercentage) {
		this.standard = standard;
		this.maxPercentage = maxPercentage;
		this.minPercentage = minPercentage;
		this.avgPercentage = avgPercentage;
	}

	public Standard getStandard() {
		return standard;
	}

	public Double getMaxPercentage() {
		return maxPercentage;
	}

	public Double getMinPercentage() {
		return minPercentage;
	}

	public Double getAvgPercentage() {
		return avgPercentage;
	}

	public void setMaxPercentage(Double maxPercentage) {
		this.maxPercentage = maxPercentage;
	}

	public void setMinPercentage(Double minPercentage) {
		this.minPercentage = minPercentage;
	}

	public void setAvgPercentage(Double avgPercentage) {
		this.avgPercentage = avgPercentage;
	}

}
