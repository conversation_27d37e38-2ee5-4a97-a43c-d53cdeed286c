/**
 * 
 */
package com.lernen.cloud.core.api.examination.report.greensheet;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum GreenSheetClass {
	PRE_PRIMARY_I, PRE_PRIMARY_II, PRE_PRIMARY_III, I, II, III, IV, V, VI, VII, VIII, IX, X, XI, XII;
	
	public static GreenSheetClass getGreenSheetClass(String greenSheetClass){
		if(StringUtils.isBlank(greenSheetClass)){
			return null;
		}
		for(GreenSheetClass greenSheetClassEnum : GreenSheetClass.values()){
			if(greenSheetClassEnum.name().equalsIgnoreCase(greenSheetClass)){
				return greenSheetClassEnum;
			}
		}
		return null;
	}
}
