package com.lernen.cloud.core.api.fees.payment;

import java.util.UUID;

import com.lernen.cloud.core.api.fees.DiscountBasicInfo;
import com.lernen.cloud.core.api.fees.FeeConfigurationBasicInfo;
import com.lernen.cloud.core.api.fees.FeeHeadConfiguration;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentDetailedRow;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeePaymentTransactionRowDetails {

	private Student student;

	private final FeePaymentTransactionMetaData feePaymentTransactionMetaData;

	private final FeeConfigurationBasicInfo feeConfigurationBasicInfo;

	private final FeeHeadConfiguration feeHeadConfiguration;

	private final double paidAmount;

	private final double instantDiscountAmount;

	private final double fineAmount;

	public FeePaymentTransactionRowDetails(Student student, FeePaymentTransactionMetaData feePaymentTransactionMetaData,
			FeeConfigurationBasicInfo feeConfigurationBasicInfo, FeeHeadConfiguration feeHeadConfiguration,
			double paidAmount, double instantDiscountAmount, double fineAmount) {
		this.student = student;
		this.feePaymentTransactionMetaData = feePaymentTransactionMetaData;
		this.feeConfigurationBasicInfo = feeConfigurationBasicInfo;
		this.feeHeadConfiguration = feeHeadConfiguration;
		this.paidAmount = paidAmount;
		this.instantDiscountAmount = instantDiscountAmount;
		this.fineAmount = fineAmount;
	}

	public FeePaymentTransactionRowDetails(FeePaymentTransactionMetaData feePaymentTransactionMetaData,
			FeeConfigurationBasicInfo feeConfigurationBasicInfo, FeeHeadConfiguration feeHeadConfiguration,
			double paidAmount, double instantDiscountAmount, double fineAmount) {
		this.feePaymentTransactionMetaData = feePaymentTransactionMetaData;
		this.feeConfigurationBasicInfo = feeConfigurationBasicInfo;
		this.feeHeadConfiguration = feeHeadConfiguration;
		this.paidAmount = paidAmount;
		this.instantDiscountAmount = instantDiscountAmount;
		this.fineAmount = fineAmount;
	}

	public FeePaymentTransactionMetaData getFeePaymentTransactionMetaData() {
		return feePaymentTransactionMetaData;
	}

	public FeeConfigurationBasicInfo getFeeConfigurationBasicInfo() {
		return feeConfigurationBasicInfo;
	}

	public FeeHeadConfiguration getFeeHeadConfiguration() {
		return feeHeadConfiguration;
	}

	public double getPaidAmount() {
		return paidAmount;
	}

	public double getInstantDiscountAmount() {
		return instantDiscountAmount;
	}

	public Student getStudent() {
		return student;
	}

	public void setStudent(Student student) {
		this.student = student;
	}

	public double getFineAmount() {
		return fineAmount;
	}

	@Override
	public String toString() {
		return "FeePaymentTransactionRowDetails [student=" + student + ", feePaymentTransactionMetaData="
				+ feePaymentTransactionMetaData + ", feeConfigurationBasicInfo=" + feeConfigurationBasicInfo
				+ ", feeHeadConfiguration=" + feeHeadConfiguration + ", paidAmount=" + paidAmount
				+ ", instantDiscountAmount=" + instantDiscountAmount + ", fineAmount=" + fineAmount + "]";
	}

}
