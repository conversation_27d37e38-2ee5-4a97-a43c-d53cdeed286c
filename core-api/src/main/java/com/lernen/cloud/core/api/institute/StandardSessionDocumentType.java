package com.lernen.cloud.core.api.institute;

import com.lernen.cloud.core.api.common.DocumentType;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public enum StandardSessionDocumentType implements DocumentType {

    OTHER(null, "Other");

    private final String documentName;
    private final String displayName;
    private final boolean isThumbnail;

    StandardSessionDocumentType(String documentName, String displayName) {
        this.documentName = documentName;
        this.displayName = displayName;
        this.isThumbnail = false;
    }

    public static StandardSessionDocumentType getStandardSessionDocumentType(String documentType) {
        if (StringUtils.isBlank(documentType)) {
            return null;
        }
        for (StandardSessionDocumentType documentTypeEnum : StandardSessionDocumentType.values()) {
            if (documentTypeEnum.name().equalsIgnoreCase(documentType)) {
                return documentTypeEnum;
            }
        }
        return null;
    }

    public static Set<StandardSessionDocumentType> getDocumentTypes(String documentsTypeStr){
        Set<StandardSessionDocumentType> documentTypes = new HashSet<StandardSessionDocumentType>();
        if(!StringUtils.isEmpty(documentsTypeStr)) {
            String[] types = documentsTypeStr.split(",");
            for(String stats : types) {
                if (StandardSessionDocumentType.getStandardSessionDocumentType(stats) == null) {
                    continue;
                }
                documentTypes.add(StandardSessionDocumentType.getStandardSessionDocumentType(stats));
            }
            return documentTypes;
        }

        StandardSessionDocumentType[] types = StandardSessionDocumentType.values();
        documentTypes.addAll(Arrays.asList(types));
        return documentTypes;
    }

    @Override
    public String getDocumentName() {
        return documentName;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public boolean isThumbnail() { return isThumbnail; }

}
