package com.lernen.cloud.core.api.user;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 
 * <AUTHOR>
 *
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum Module {
	/**
	 * Default module which enables global default feature irrespective of modules
	 */
	DEFAULT("Default"),
	STORE("Store"),
	FEES("Fees"),
	ADMISSION("Admission"),
	TRANSPORT("Transport"),
	COURSES("Courses"), 
	EXAMINATION("Examination"), 
	ATTENDANCE("Attendance"), 
	STAFF_MANAGEMENT("Staff Management"), 
	INCOME_EXPENSE("Income Expense"), 
	USER_MANAGEMENT("User Management"),
	LECTURE_MANAGEMENT("Lecture Management"),
	HOMEWORK_MANAGEMENT("Homework Management"),
	SALARY_MANAGEMENT("Salary Management"),
	NOTICE_BOARD_MANAGEMENT("Notice Board Management"),
	STUDENT_MANAGEMENT("Student Management"),
	AUDIT_LOGS("Audit Logs"),
	COMMUNICATION("Communication"),
	HOLIDAY_CALENDAR("Holiday Calendar"),
	TIMETABLE_MANAGEMENT("Timetable Management"),
	STAFF_ATTENDANCE("Staff Attendance"),
	FRONT_DESK("Front Desk"),
	MOBILE_APPLICATION_MANAGEMENT("Mobile App Management"),
	STUDENT_DIARY("Student Diary"),
	INSTITUTE_MANAGEMENT("Institute Management"),
	LEAVE_MANAGEMENT("Leave Management"),
	LIBRARY_MANAGEMENT("Library Management"),
	COMPLAINT_BOX("Complaint Box"),
	STAFF_DIARY("Staff Diary"),
	PARENTS_APPOINTMENT("Parents Appointment"),
	VISITORS_DESK ("Visitors Desk"),
	ONLINE_ASSESSMENT ("Online Assessment"),
	HOSTEL_MANAGEMENT ("Hostel Management"),
	STUDY_TRACKER("Study Tracker"),
	STUDENT_FINANCE("Student Finance");

	private String displayName;

	private Module(String displayName) {
		this.displayName = displayName;
	}
	
	public String getModuleId() {
		return this.name();
	}

	public String getDisplayName() {
		return displayName;
	}
}
