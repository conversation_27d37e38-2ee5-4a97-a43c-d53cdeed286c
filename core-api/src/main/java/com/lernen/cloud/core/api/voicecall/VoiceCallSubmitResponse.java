/**
 * 
 */
package com.lernen.cloud.core.api.voicecall;

import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class VoiceCallSubmitResponse {

	private final boolean success;

	private final String voiceCallUniqueId;

	private final String mobileNumber;

	private final String errorCode;

	private final String description;

	private final Integer creditCount;

	private final Map<String, Object> metadata;


	public VoiceCallSubmitResponse(boolean success, String voiceCallUniqueId, String mobileNumber, String errorCode, String description, Integer creditCount, Map<String, Object> metadata) {
		this.success = success;
		this.voiceCallUniqueId = voiceCallUniqueId;
		this.mobileNumber = mobileNumber;
		this.errorCode = errorCode;
		this.description = description;
		this.creditCount = creditCount;
		this.metadata = metadata;
	}

	public boolean isSuccess() {
		return success;
	}

	public String getVoiceCallUniqueId() {
		return voiceCallUniqueId;
	}

	public String getMobileNumber() {
		return mobileNumber;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public String getDescription() {
		return description;
	}

	public Integer getCreditCount() {
		return creditCount;
	}

	public Map<String, Object> getMetadata() {
		return metadata;
	}

	@Override
	public String toString() {
		return "VoiceCallSubmitResponse{" +
				"success=" + success +
				", voiceCallUniqueId='" + voiceCallUniqueId + '\'' +
				", mobileNumber='" + mobileNumber + '\'' +
				", errorCode='" + errorCode + '\'' +
				", description='" + description + '\'' +
				", creditCount=" + creditCount +
				", metadata=" + metadata +
				'}';
	}
}
