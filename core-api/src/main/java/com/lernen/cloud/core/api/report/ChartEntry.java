package com.lernen.cloud.core.api.report;

public class ChartEntry {
    private final double value;
    private final String colorHexCode;

    public ChartEntry(double value, String colorHexCode) {
        this.value = value;
        this.colorHexCode = colorHexCode;
    }

    public double getValue() {
        return value;
    }

    public String getColorHexCode() {
        return colorHexCode;
    }

    @Override
    public String toString() {
        return "ChartEntry{" +
                "value=" + value +
                ", colorHexCode='" + colorHexCode + '\'' +
                '}';
    }
}

