package com.lernen.cloud.core.api.fees;

import java.util.UUID;

public class FeeConfigurationUpdatePayload {

	private int instituteId;

	private UUID feeId;

	private String feeName;

	private Integer dueDate;

	private boolean allowPendingEnrollment;
	private boolean fineApplicable;
	private boolean transferToWallet;

	private String description;

	public FeeConfigurationUpdatePayload() {

	}

	public FeeConfigurationUpdatePayload(int instituteId, UUID feeId, String feeName, Integer dueDate,
										 boolean allowPendingEnrollment, boolean fineApplicable, boolean transferToWallet, String description) {
		this.instituteId = instituteId;
		this.feeId = feeId;
		this.feeName = feeName;
		this.dueDate = dueDate;
		this.allowPendingEnrollment = allowPendingEnrollment;
		this.fineApplicable = fineApplicable;
		this.transferToWallet = transferToWallet;
		this.description = description;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getFeeId() {
		return feeId;
	}

	public void setFeeId(UUID feeId) {
		this.feeId = feeId;
	}

	public String getFeeName() {
		return feeName;
	}

	public void setFeeName(String feeName) {
		this.feeName = feeName;
	}

	public Integer getDueDate() {
		return dueDate;
	}

	public void setDueDate(Integer dueDate) {
		this.dueDate = dueDate;
	}

	public boolean isAllowPendingEnrollment() {
		return allowPendingEnrollment;
	}

	public void setAllowPendingEnrollment(boolean allowPendingEnrollment) {
		this.allowPendingEnrollment = allowPendingEnrollment;
	}

	public boolean isFineApplicable() {
		return fineApplicable;
	}

	public void setFineApplicable(boolean fineApplicable) {
		this.fineApplicable = fineApplicable;
	}

	public boolean isTransferToWallet() { return transferToWallet;}

	public void setTransferToWallet(boolean transferToWallet) { this.transferToWallet = transferToWallet;}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Override
	public String toString() {
		return "FeeConfigurationUpdatePayload{" +
				"instituteId=" + instituteId +
				", feeId=" + feeId +
				", feeName='" + feeName + '\'' +
				", dueDate=" + dueDate +
				", allowPendingEnrollment=" + allowPendingEnrollment +
				", fineApplicable=" + fineApplicable +
				", transferToWallet=" + transferToWallet +
				", description='" + description + '\'' +
				'}';
	}

}
