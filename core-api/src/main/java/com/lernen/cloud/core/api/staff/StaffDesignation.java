/**
 * 
 */
package com.lernen.cloud.core.api.staff;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class StaffDesignation {
	
	private int instituteId;
	
	private UUID staffDesignationId;
	
	private String staffDesignationName;

	/**
	 * @param instituteId
	 * @param staffDesignationId
	 * @param staffDesignationName
	 */
	public StaffDesignation(int instituteId, UUID staffDesignationId, String staffDesignationName) {
		this.instituteId = instituteId;
		this.staffDesignationId = staffDesignationId;
		this.staffDesignationName = staffDesignationName;
	}

	/**
	 * 
	 */
	public StaffDesignation() {
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the staffDesignationId
	 */
	public UUID getStaffDesignationId() {
		return staffDesignationId;
	}

	/**
	 * @param staffDesignationId the staffDesignationId to set
	 */
	public void setStaffDesignationId(UUID staffDesignationId) {
		this.staffDesignationId = staffDesignationId;
	}

	/**
	 * @return the staffDesignationName
	 */
	public String getStaffDesignationName() {
		return staffDesignationName;
	}

	/**
	 * @param staffDesignationName the staffDesignationName to set
	 */
	public void setStaffDesignationName(String staffDesignationName) {
		this.staffDesignationName = staffDesignationName;
	}

	@Override
	public String toString() {
		return "StaffDesignation [instituteId=" + instituteId + ", staffDesignationId=" + staffDesignationId
				+ ", staffDesignationName=" + staffDesignationName + "]";
	}

}
