package com.lernen.cloud.core.api.assessment;

import org.apache.commons.lang3.StringUtils;

public enum AnswerSubmitStatus {
    SAVED , SUBMITTED;

    public static AnswerSubmitStatus getAnswerSubmitStatus(String answerStatus) {
		if (StringUtils.isBlank(answerStatus)) {
			return null;
		}
		for (AnswerSubmitStatus answerStatusEnum : AnswerSubmitStatus.values()) {
			if (answerStatusEnum.name().equalsIgnoreCase(answerStatus)) {
				return answerStatusEnum;
			}
		}
		return null;
	}
}
