/**
 * 
 */
package com.lernen.cloud.core.api.staff;

/**
 * <AUTHOR>
 *
 */
public class StaffAddressContactInfo {

	private String permanentAddress1;
	
	private String permanentAddress2;

	private String permanentCity;

	private String permanentState;

	private String permanentZipcode;

	private String permanentCountry;

	private String presentAddress1;
	
	private String presentAddress2;

	private String presentCity;

	private String presentState;

	private String presentZipcode;

	private String presentCountry;
	
	private String emergancyContactNumber;

	/**
	 * @param permanentAddress1
	 * @param permanentAddress2
	 * @param permanentCity
	 * @param permanentState
	 * @param permanentZipcode
	 * @param permanentCountry
	 * @param presentAddress1
	 * @param presentAddress2
	 * @param presentCity
	 * @param presentState
	 * @param presentZipcode
	 * @param presentCountry
	 * @param emergancyContactNumber
	 */
	public StaffAddressContactInfo(String permanentAddress1, String permanentAddress2, String permanentCity,
			String permanentState, String permanentZipcode, String permanentCountry, String presentAddress1,
			String presentAddress2, String presentCity, String presentState, String presentZipcode,
			String presentCountry, String emergancyContactNumber) {
		this.permanentAddress1 = permanentAddress1;
		this.permanentAddress2 = permanentAddress2;
		this.permanentCity = permanentCity;
		this.permanentState = permanentState;
		this.permanentZipcode = permanentZipcode;
		this.permanentCountry = permanentCountry;
		this.presentAddress1 = presentAddress1;
		this.presentAddress2 = presentAddress2;
		this.presentCity = presentCity;
		this.presentState = presentState;
		this.presentZipcode = presentZipcode;
		this.presentCountry = presentCountry;
		this.emergancyContactNumber = emergancyContactNumber;
	}

	
	
	/**
	 * 
	 */
	public StaffAddressContactInfo() {
	}


	/**
	 * @return the permanentAddress1
	 */
	public String getPermanentAddress1() {
		return permanentAddress1;
	}



	/**
	 * @param permanentAddress1 the permanentAddress1 to set
	 */
	public void setPermanentAddress1(String permanentAddress1) {
		this.permanentAddress1 = permanentAddress1;
	}



	/**
	 * @return the permanentAddress2
	 */
	public String getPermanentAddress2() {
		return permanentAddress2;
	}



	/**
	 * @param permanentAddress2 the permanentAddress2 to set
	 */
	public void setPermanentAddress2(String permanentAddress2) {
		this.permanentAddress2 = permanentAddress2;
	}



	/**
	 * @return the permanentCity
	 */
	public String getPermanentCity() {
		return permanentCity;
	}



	/**
	 * @param permanentCity the permanentCity to set
	 */
	public void setPermanentCity(String permanentCity) {
		this.permanentCity = permanentCity;
	}



	/**
	 * @return the permanentState
	 */
	public String getPermanentState() {
		return permanentState;
	}



	/**
	 * @param permanentState the permanentState to set
	 */
	public void setPermanentState(String permanentState) {
		this.permanentState = permanentState;
	}



	/**
	 * @return the permanentZipcode
	 */
	public String getPermanentZipcode() {
		return permanentZipcode;
	}



	/**
	 * @param permanentZipcode the permanentZipcode to set
	 */
	public void setPermanentZipcode(String permanentZipcode) {
		this.permanentZipcode = permanentZipcode;
	}



	/**
	 * @return the permanentCountry
	 */
	public String getPermanentCountry() {
		return permanentCountry;
	}



	/**
	 * @param permanentCountry the permanentCountry to set
	 */
	public void setPermanentCountry(String permanentCountry) {
		this.permanentCountry = permanentCountry;
	}



	/**
	 * @return the presentAddress1
	 */
	public String getPresentAddress1() {
		return presentAddress1;
	}



	/**
	 * @param presentAddress1 the presentAddress1 to set
	 */
	public void setPresentAddress1(String presentAddress1) {
		this.presentAddress1 = presentAddress1;
	}



	/**
	 * @return the presentAddress2
	 */
	public String getPresentAddress2() {
		return presentAddress2;
	}



	/**
	 * @param presentAddress2 the presentAddress2 to set
	 */
	public void setPresentAddress2(String presentAddress2) {
		this.presentAddress2 = presentAddress2;
	}



	/**
	 * @return the presentCity
	 */
	public String getPresentCity() {
		return presentCity;
	}



	/**
	 * @param presentCity the presentCity to set
	 */
	public void setPresentCity(String presentCity) {
		this.presentCity = presentCity;
	}



	/**
	 * @return the presentState
	 */
	public String getPresentState() {
		return presentState;
	}



	/**
	 * @param presentState the presentState to set
	 */
	public void setPresentState(String presentState) {
		this.presentState = presentState;
	}



	/**
	 * @return the presentZipcode
	 */
	public String getPresentZipcode() {
		return presentZipcode;
	}



	/**
	 * @param presentZipcode the presentZipcode to set
	 */
	public void setPresentZipcode(String presentZipcode) {
		this.presentZipcode = presentZipcode;
	}



	/**
	 * @return the presentCountry
	 */
	public String getPresentCountry() {
		return presentCountry;
	}



	/**
	 * @param presentCountry the presentCountry to set
	 */
	public void setPresentCountry(String presentCountry) {
		this.presentCountry = presentCountry;
	}


	/**
	 * @return the emergancyContactNumber
	 */
	public String getEmergancyContactNumber() {
		return emergancyContactNumber;
	}



	/**
	 * @param emergancyContactNumber the emergancyContactNumber to set
	 */
	public void setEmergancyContactNumber(String emergancyContactNumber) {
		this.emergancyContactNumber = emergancyContactNumber;
	}



	@Override
	public String toString() {
		return "StaffAddressContactInfo [permanentAddress1=" + permanentAddress1 + ", permanentAddress2="
				+ permanentAddress2 + ", permanentCity=" + permanentCity + ", permanentState=" + permanentState
				+ ", permanentZipcode=" + permanentZipcode + ", permanentCountry=" + permanentCountry
				+ ", presentAddress1=" + presentAddress1 + ", presentAddress2=" + presentAddress2 + ", presentCity="
				+ presentCity + ", presentState=" + presentState + ", presentZipcode=" + presentZipcode
				+ ", presentCountry=" + presentCountry + ", emergancyContactNumber=" + emergancyContactNumber + "]";
	}	
}
