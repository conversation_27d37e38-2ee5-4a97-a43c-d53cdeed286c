package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.student.StudentLite;

/**
 * <AUTHOR>
 * @created_at 28/08/24 : 16:50
 **/
public class StudentReportCardStatusDetails {

    private final StudentLite studentLite;
    private final ExamReportCardMetadata examReportCardMetadata;
    private final StudentExamDisplayDataStatus studentExamDisplayDataStatus;

    public StudentReportCardStatusDetails(StudentLite studentLite, ExamReportCardMetadata examReportCardMetadata, StudentExamDisplayDataStatus studentExamDisplayDataStatus) {
        this.studentLite = studentLite;
        this.examReportCardMetadata = examReportCardMetadata;
        this.studentExamDisplayDataStatus = studentExamDisplayDataStatus;
    }

    public StudentLite getStudentLite() {
        return studentLite;
    }

    public ExamReportCardMetadata getExamReportCardMetadata() {
        return examReportCardMetadata;
    }

    public StudentExamDisplayDataStatus getStudentExamDisplayDataStatus() {
        return studentExamDisplayDataStatus;
    }

    @Override
    public String toString() {
        return "StudentReportCardStatusDetails{" +
                "studentLite=" + studentLite +
                ", examReportCardMetadata=" + examReportCardMetadata +
                ", studentExamDisplayDataStatus=" + studentExamDisplayDataStatus +
                '}';
    }
}
