package com.lernen.cloud.core.api.notification;

import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.UserType;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentNotificationDetailsLite extends NotificationDetails {

	private final StudentLite studentLite;

	public StudentNotificationDetailsLite(StudentLite studentLite,
			UUID notificationId, int instituteId, UUID userId,
			Integer academicSessionId, NotificationType notificationType,
			DeliveryMode deliveryMode, String deliveryDestination, UUID batchId,
			String batchName, String notificationTitle,
			String notificationContent, NotificationStatus notificationStatus,
			Integer deliveredTime, Integer generatedTime,
			Integer lastOpenedTime, Integer lastClickedTime,
			String externalUniqueId, Map<String, Object> metaData,  Integer creditsUsed, UUID creditTransactionId, Integer refundCredits) {
		/**
		 * SMS provider is set as null since it is not supposed to be exposed to
		 * user
		 */
		super(notificationId, instituteId, userId, UserType.STUDENT,
				academicSessionId, null, notificationType, deliveryMode,
				deliveryDestination, batchId, batchName, notificationTitle,
				notificationContent, notificationStatus, deliveredTime,
				generatedTime, lastOpenedTime, lastClickedTime,
				externalUniqueId, metaData,  creditsUsed, creditTransactionId, refundCredits);
		this.studentLite = studentLite;
	}

	public StudentNotificationDetailsLite(
			StudentNotificationDetails studentNotificationDetails) {
		super(studentNotificationDetails.getNotificationId(),
				studentNotificationDetails.getInstituteId(),
				studentNotificationDetails.getUserId(),
				studentNotificationDetails.getUserType(),
				studentNotificationDetails.getAcademicSessionId(),
				studentNotificationDetails.getCommunicationServiceProvider(),
				studentNotificationDetails.getNotificationType(),
				studentNotificationDetails.getDeliveryMode(),
				studentNotificationDetails.getDeliveryDestination(),
				studentNotificationDetails.getBatchId(),
				studentNotificationDetails.getBatchName(),
				studentNotificationDetails.getNotificationTitle(),
				studentNotificationDetails.getNotificationContent(),
				studentNotificationDetails.getNotificationStatus(),
				studentNotificationDetails.getDeliveredTime(),
				studentNotificationDetails.getGeneratedTime(),
				studentNotificationDetails.getLastOpenedTime(),
				studentNotificationDetails.getLastClickedTime(),
				studentNotificationDetails.getExternalUniqueId(),
				studentNotificationDetails.getMetaData(),
				studentNotificationDetails.getCreditsUsed(),
				studentNotificationDetails.getCreditTransactionId(),
				studentNotificationDetails.getRefundCredits());
		this.studentLite = Student
				.getStudentLite(studentNotificationDetails.getStudent());

	}

	public StudentLite getStudentLite() {
		return studentLite;
	}

}
