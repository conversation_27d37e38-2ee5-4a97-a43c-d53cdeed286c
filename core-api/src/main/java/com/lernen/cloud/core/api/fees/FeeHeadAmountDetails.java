package com.lernen.cloud.core.api.fees;

import java.util.Set;

import com.lernen.cloud.core.api.user.Module;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeHeadAmountDetails extends FeeHeadAmount {

	private final FeeHeadConfiguration feeHeadConfiguration;

	private final Set<Module> authorizedModules;

	public FeeHeadAmountDetails(FeeHeadConfiguration feeHeadConfiguration, Double amount,
			Set<Module> authorizedModules) {
		super(feeHeadConfiguration.getFeeHeadId(), amount);
		this.feeHeadConfiguration = feeHeadConfiguration;
		this.authorizedModules = authorizedModules;
	}

	public FeeHeadAmountDetails(FeeHeadConfiguration feeHeadConfiguration, FeeEntity feeEntity,
			Set<Module> authorizedModules) {
		super(feeHeadConfiguration.getFeeHeadId(), 0d, feeEntity);
		this.feeHeadConfiguration = feeHeadConfiguration;
		this.authorizedModules = authorizedModules;
	}

	public FeeHeadAmountDetails(FeeHeadConfiguration feeHeadConfiguration, double amount, FeeEntity feeEntity,
			Set<Module> authorizedModules) {
		super(feeHeadConfiguration.getFeeHeadId(), amount, feeEntity);
		this.feeHeadConfiguration = feeHeadConfiguration;
		this.authorizedModules = authorizedModules;
	}
	
	public FeeHeadAmountDetails(FeeHeadConfiguration feeHeadConfiguration, Boolean isPercentage, double amount, FeeEntity feeEntity,
			Set<Module> authorizedModules) {
		super(feeHeadConfiguration.getFeeHeadId(), amount, isPercentage, feeEntity);
		this.feeHeadConfiguration = feeHeadConfiguration;
		this.authorizedModules = authorizedModules;
	}

	public FeeHeadConfiguration getFeeHeadConfiguration() {
		return feeHeadConfiguration;
	}

	public Set<Module> getAuthorizedModules() {
		return authorizedModules;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = super.hashCode();
		result = prime * result + ((feeHeadConfiguration == null) ? 0 : feeHeadConfiguration.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!super.equals(obj))
			return false;
		if (getClass() != obj.getClass())
			return false;
		FeeHeadAmountDetails other = (FeeHeadAmountDetails) obj;
		if (feeHeadConfiguration == null) {
			if (other.feeHeadConfiguration != null)
				return false;
		} else if (!feeHeadConfiguration.equals(other.feeHeadConfiguration))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "FeeHeadAmountDetails [feeHeadConfiguration=" + feeHeadConfiguration + ", authorizedModules="
				+ authorizedModules + "]";
	}

}
