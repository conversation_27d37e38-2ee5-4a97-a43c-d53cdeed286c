package com.lernen.cloud.core.api.studytracker;

import java.util.UUID;

public class StudyTrackerTypeResponse {

    private final UUID typeId;
    private final String typeName;
    private final Double value;
    private final String studentRemark;
    private final String facultyRemark;
    
    public StudyTrackerTypeResponse(UUID typeId, String typeName, Double value, String studentRemark,
            String facultyRemark) {
        this.typeId = typeId;
        this.typeName = typeName;
        this.value = value;
        this.studentRemark = studentRemark;
        this.facultyRemark = facultyRemark;
    }

    public UUID getTypeId() {
        return typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public Double getValue() {
        return value;
    }

    public String getStudentRemark() {
        return studentRemark;
    }

    public String getFacultyRemark() {
        return facultyRemark;
    }

    @Override
    public String toString() {
        return "StudyTrackerTypeResponse [typeId=" + typeId + ", typeName=" + typeName + ", value=" + value
                + ", studentRemark=" + studentRemark + ", facultyRemark=" + facultyRemark + "]";
    }

}
