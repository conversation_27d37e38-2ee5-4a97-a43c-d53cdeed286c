package com.lernen.cloud.core.api.fees;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class DefaultFeeAssignmentStructureDetailsRow {

	private final String structureName;

	private final UUID structureId;

	private final FeeStructureType feeStructureType;

	private final FeeAssignmentDetailsRow feeAssignmentDetailsRow;

	public DefaultFeeAssignmentStructureDetailsRow(String structureName, UUID structureId,
			FeeStructureType feeStructureType, FeeAssignmentDetailsRow feeAssignmentDetailsRow) {
		this.structureName = structureName;
		this.structureId = structureId;
		this.feeStructureType = feeStructureType;
		this.feeAssignmentDetailsRow = feeAssignmentDetailsRow;
	}

	public String getStructureName() {
		return structureName;
	}

	public UUID getStructureId() {
		return structureId;
	}

	public FeeStructureType getFeeStructureType() {
		return feeStructureType;
	}

	public FeeAssignmentDetailsRow getFeeAssignmentDetailsRow() {
		return feeAssignmentDetailsRow;
	}

}
