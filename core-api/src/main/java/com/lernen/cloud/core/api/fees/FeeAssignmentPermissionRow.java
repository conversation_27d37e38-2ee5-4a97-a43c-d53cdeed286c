package com.lernen.cloud.core.api.fees;

import java.util.UUID;

import com.lernen.cloud.core.api.user.Module;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeAssignmentPermissionRow {

	private final UUID feeId;

	private final int feeHeadId;

	private final Module module;

	private final int instituteId;

	private final Double feeProportionNumerator;

	private final Double feeProportionDenominator;

	public FeeAssignmentPermissionRow(UUID feeId, int feeHeadId, Module module, int instituteId,
			Double feeProportionNumerator, Double feeProportionDenominator) {
		this.feeId = feeId;
		this.feeHeadId = feeHeadId;
		this.module = module;
		this.instituteId = instituteId;
		this.feeProportionNumerator = feeProportionNumerator;
		this.feeProportionDenominator = feeProportionDenominator;
	}

	public UUID getFeeId() {
		return feeId;
	}

	public int getFeeHeadId() {
		return feeHeadId;
	}

	public Module getModule() {
		return module;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public Double getFeeProportionNumerator() {
		return feeProportionNumerator;
	}

	public Double getFeeProportionDenominator() {
		return feeProportionDenominator;
	}

	@Override
	public String toString() {
		return "FeeAssignmentPermissionRow [feeId=" + feeId + ", feeHeadId=" + feeHeadId + ", module=" + module
				+ ", instituteId=" + instituteId + ", feeProportionNumerator=" + feeProportionNumerator
				+ ", feeProportionDenominator=" + feeProportionDenominator + "]";
	}

}
