package com.lernen.cloud.core.api.transport;

import java.util.*;

public class CloneTransportServiceRoutes {
    private int srcInstituteId;
    private int destInstituteId;
    private int srcAcademicSessionId;
    private int destAcademicSessionId;
    private Set<String> serviceNameSet;
    private Set<RouteType> routeTypeSet;

    public CloneTransportServiceRoutes() {

    }

    public CloneTransportServiceRoutes(int srcInstituteId, int destInstituteId, int srcAcademicSessionId, int destAcademicSessionId, Set<String> serviceNameSet, Set<RouteType> routeTypeSet) {
        this.srcInstituteId = srcInstituteId;
        this.destInstituteId = destInstituteId;
        this.srcAcademicSessionId = srcAcademicSessionId;
        this.destAcademicSessionId = destAcademicSessionId;
        this.serviceNameSet = serviceNameSet;
        this.routeTypeSet = routeTypeSet;
    }

    public int getSrcInstituteId() {
        return srcInstituteId;
    }

    public void setSrcInstituteId(int srcInstituteId) {
        this.srcInstituteId = srcInstituteId;
    }

    public int getDestInstituteId() {
        return destInstituteId;
    }

    public void setDestInstituteId(int destInstituteId) {
        this.destInstituteId = destInstituteId;
    }

    public int getSrcAcademicSessionId() {
        return srcAcademicSessionId;
    }

    public void setSrcAcademicSessionId(int srcAcademicSessionId) {
        this.srcAcademicSessionId = srcAcademicSessionId;
    }

    public int getDestAcademicSessionId() {
        return destAcademicSessionId;
    }

    public void setDestAcademicSessionId(int destAcademicSessionId) {
        this.destAcademicSessionId = destAcademicSessionId;
    }

    public Set<String> getServiceNameSet() {
        return serviceNameSet;
    }

    public void setServiceNameSet(Set<String> serviceNameSet) {
        this.serviceNameSet = serviceNameSet;
    }

    public Set<RouteType> getRouteTypeSet() {
        return routeTypeSet;
    }

    public void setRouteTypeSet(Set<RouteType> routeTypeSet) {
        this.routeTypeSet = routeTypeSet;
    }

    @Override
    public String toString() {
        return "CloneTransportServiceRoutes{" +
                "srcInstituteId=" + srcInstituteId +
                ", destInstituteId=" + destInstituteId +
                ", srcAcademicSessionId=" + srcAcademicSessionId +
                ", destAcademicSessionId=" + destAcademicSessionId +
                ", serviceNameSet=" + serviceNameSet +
                ", routeTypeSet=" + routeTypeSet +
                '}';
    }
}
