package com.lernen.cloud.core.api.inventory;

import java.util.List;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;

/**
 * 
 * <AUTHOR>
 *
 */
public class SalesOrderPayload extends SalesOrder {

	private List<PurchasedProductGroup> purchasedProductGroups;

	public SalesOrderPayload() {
	}

	public SalesOrderPayload(int instituteId,
			InventoryTransactionType inventoryTransactionType, UUID soldBy,
			InventoryUserType inventoryUserType, String purchasedBy,
			Integer returnedToSellerId, String reference, String email,
			long transactionDate, PaymentStatus paymentStatus,
			String description, Double additionalCost,
			Double additionalDiscount, TransactionMode transactionMode,
			boolean useWallet, Double usedWalletAmount,
			Double walletCreditAmount, Double paidAmount,
			List<PurchasedProduct> purchasedProducts,
			List<PurchasedProductGroup> purchasedProductGroups) {
		super(instituteId, inventoryTransactionType, soldBy, inventoryUserType,
				purchasedBy, returnedToSellerId, reference, email,
				transactionDate, paymentStatus, description, additionalCost,
				additionalDiscount, transactionMode, useWallet,
				usedWalletAmount, walletCreditAmount, paidAmount,
				purchasedProducts);
		this.purchasedProductGroups = purchasedProductGroups;
	}

	public List<PurchasedProductGroup> getPurchasedProductGroups() {
		return purchasedProductGroups;
	}

	public void setPurchasedProductGroups(
			List<PurchasedProductGroup> purchasedProductGroups) {
		this.purchasedProductGroups = purchasedProductGroups;
	}

	// TODO: Also validate calculation of discount , net amount etc
	public static boolean isValid(SalesOrderPayload salesOrderPayload) {

		if (salesOrderPayload == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Empty Sales Order"));
		}

		if (salesOrderPayload.getInventoryTransactionType() == null
				|| InventoryTransactionType.isPurchaseTransaction(
						salesOrderPayload.getInventoryTransactionType())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Invalid Order Type"));
		}

		if (salesOrderPayload.getSoldBy() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Empty Seller Name"));
		}

		validateInventoryUser(salesOrderPayload);

		if (salesOrderPayload.getPaymentStatus() == null || !salesOrderPayload
				.getPaymentStatus().equals(PaymentStatus.PAID)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Failed Payment"));
		}

		if (CollectionUtils.isEmpty(salesOrderPayload.getPurchasedProducts())
				&& CollectionUtils.isEmpty(
						salesOrderPayload.getPurchasedProductGroups())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Sell atleast one product"));
		}

		if (salesOrderPayload.getAdditionalCost() != null
				&& salesOrderPayload.getAdditionalCost() < 0d) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Additional cost cannot be negative"));
		}

		if (salesOrderPayload.getAdditionalDiscount() != null
				&& salesOrderPayload.getAdditionalDiscount() < 0d) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Additional discount cannot be negative"));
		}
		boolean isIssueTransaction = salesOrderPayload
				.getInventoryTransactionType() == InventoryTransactionType.ISSUE;
		if (!CollectionUtils
				.isEmpty(salesOrderPayload.getPurchasedProducts())) {
			for (PurchasedProduct purchasedProduct : salesOrderPayload
					.getPurchasedProducts()) {

				if (purchasedProduct.getSkuId() == null) {
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_SALES_ORDER,
							"Blank SKU ID can't be added"));
				}

				if (purchasedProduct.getQuantity() <= 0) {
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_SALES_ORDER,
							"Atleast 1 quantity should be added"));
				}

				if (!isIssueTransaction
						&& purchasedProduct.getTotalPrice() <= 0d) {
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_SALES_ORDER,
							"Total Price should be greater than zero"));
				}

				if (!isIssueTransaction
						&& purchasedProduct.getTotalDiscount() < 0d) {
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_SALES_ORDER,
							"Negative discount is not allowed"));
				}

				if (!isIssueTransaction
						&& purchasedProduct.getTotalTax() < 0d) {
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_SALES_ORDER,
							"Negative tax is not allowed"));
				}
			}
		}

		if (!CollectionUtils
				.isEmpty(salesOrderPayload.getPurchasedProductGroups())) {
			for (PurchasedProductGroup purchasedProductGroup : salesOrderPayload
					.getPurchasedProductGroups()) {

				if (purchasedProductGroup.getGroupId() == null) {
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_SALES_ORDER,
							"Blank Group ID can't be added"));
				}

				if (purchasedProductGroup.getQuantity() <= 0) {
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_SALES_ORDER,
							"Atleast 1 group quantity should be added"));
				}

				if (!isIssueTransaction
						&& purchasedProductGroup.getTotalDiscount() < 0d) {
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_SALES_ORDER,
							"Negative discount is not allowed"));
				}

				if (!isIssueTransaction
						&& purchasedProductGroup.getTotalTax() < 0d) {
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_SALES_ORDER,
							"Negative tax is not allowed"));
				}
			}
		}

		return true;
	}

	private static void validateInventoryUser(
			SalesOrderPayload salesOrderPayload) {

		if (salesOrderPayload.getInventoryUserType() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Invalid inventory user type"));
		}

		if (salesOrderPayload.getInventoryUserType() == InventoryUserType.SELLER
				&& salesOrderPayload
						.getInventoryTransactionType() != InventoryTransactionType.RETURN) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_SALES_ORDER,
					"Only return transaction is allowd for seller user"));
		}

		if (salesOrderPayload.getInventoryUserType() == InventoryUserType.SELLER
				&& salesOrderPayload
						.getInventoryTransactionType() == InventoryTransactionType.RETURN
				&& (salesOrderPayload.getReturnedToSellerId() == null
						|| salesOrderPayload.getReturnedToSellerId() <= 0)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Invalid seller to return"));
		}

		if (salesOrderPayload
				.getInventoryUserType() == InventoryUserType.STUDENT
				|| salesOrderPayload
						.getInventoryUserType() == InventoryUserType.STAFF) {
			try {
				UUID.fromString(salesOrderPayload.getPurchasedBy());
			} catch (Exception e) {
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Invalid purchased by user"));
			}
		}

		if (salesOrderPayload.getInventoryUserType() == InventoryUserType.OTHER
				&& StringUtils.isBlank(salesOrderPayload.getPurchasedBy())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Blank purchase person name"));
		}
	}

	@Override
	public String toString() {
		return "SalesOrderPayload [purchasedProductGroups="
				+ purchasedProductGroups + "]";
	}

}
