package com.lernen.cloud.core.api.attendance;

import java.time.Month;
import java.util.Map;

public class StudentAttendanceMonthlyStats {

    private int instituteId;

    private int academicSessionId;

    private Month month;

    Map<AttendanceStatus, Double> attendanceStatusMap;

    public StudentAttendanceMonthlyStats(int instituteId, int academicSessionId, Month month, Map<AttendanceStatus, Double> attendanceStatusMap) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.month = month;
        this.attendanceStatusMap = attendanceStatusMap;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public Month getMonth() {
        return month;
    }

    public void setMonth(Month month) {
        this.month = month;
    }

    public Map<AttendanceStatus, Double> getAttendanceStatusMap() {
        return attendanceStatusMap;
    }

    public void setAttendanceStatusMap(Map<AttendanceStatus, Double> attendanceStatusMap) {
        this.attendanceStatusMap = attendanceStatusMap;
    }

    @Override
    public String toString() {
        return "StudentAttendanceMonthlyStats{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", month=" + month +
                ", attendanceStatusMap=" + attendanceStatusMap +
                '}';
    }
}
