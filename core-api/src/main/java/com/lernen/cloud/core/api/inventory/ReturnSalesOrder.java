package com.lernen.cloud.core.api.inventory;

import com.lernen.cloud.core.api.common.TransactionMode;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class ReturnSalesOrder {

    private UUID transactionId;

    private UUID returnedTo;

    private long returnDate;

    private boolean refundToWallet;

    private PaymentStatus paymentStatus;

    private TransactionMode transactionMode;

    private String description;

    public ReturnSalesOrder() {
    }

    public UUID getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(UUID transactionId) {
        this.transactionId = transactionId;
    }

    public UUID getReturnedTo() {
        return returnedTo;
    }

    public void setReturnedTo(UUID returnedTo) {
        this.returnedTo = returnedTo;
    }

    public long getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(long returnDate) {
        this.returnDate = returnDate;
    }

    public boolean isRefundToWallet() {
        return refundToWallet;
    }

    public void setRefundToWallet(boolean refundToWallet) {
        this.refundToWallet = refundToWallet;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public TransactionMode getTransactionMode() {
        return transactionMode;
    }

    public void setTransactionMode(TransactionMode transactionMode) {
        this.transactionMode = transactionMode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
