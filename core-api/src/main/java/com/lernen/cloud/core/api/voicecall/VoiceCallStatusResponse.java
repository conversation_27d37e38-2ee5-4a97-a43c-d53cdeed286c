package com.lernen.cloud.core.api.voicecall;

import com.lernen.cloud.core.api.notification.NotificationStatus;

/**
 *
 * <AUTHOR>
 *
 */
public class VoiceCallStatusResponse {

	private final NotificationStatus notificationStatus;

	private final Long statusUpdateTime;

	private final Integer startTime;

	private final Integer endTime;

	private final Integer duration;

	private final Integer retryCount;

	private final Integer credits;

	public VoiceCallStatusResponse(NotificationStatus notificationStatus, Long statusUpdateTime, Integer startTime, Integer endTime, Integer duration, Integer retryCount, Integer credits) {
		this.notificationStatus = notificationStatus;
		this.statusUpdateTime = statusUpdateTime;
		this.startTime = startTime;
		this.endTime = endTime;
		this.duration = duration;
		this.retryCount = retryCount;
		this.credits = credits;
	}

	public NotificationStatus getNotificationStatus() {
		return notificationStatus;
	}

	public Long getStatusUpdateTime() {
		return statusUpdateTime;
	}

	public Integer getStartTime() {
		return startTime;
	}

	public Integer getEndTime() {
		return endTime;
	}

	public Integer getDuration() {
		return duration;
	}

	public Integer getRetryCount() {
		return retryCount;
	}

	public Integer getCredits() {
		return credits;
	}

	@Override
	public String toString() {
		return "VoiceCallStatusResponse{" +
				"notificationStatus=" + notificationStatus +
				", statusUpdateTime=" + statusUpdateTime +
				", startTime=" + startTime +
				", endTime=" + endTime +
				", duration=" + duration +
				", retryCount=" + retryCount +
				", credits=" + credits +
				'}';
	}
}

