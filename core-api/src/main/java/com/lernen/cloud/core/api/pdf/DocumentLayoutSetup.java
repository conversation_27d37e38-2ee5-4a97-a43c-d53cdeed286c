package com.lernen.cloud.core.api.pdf;

import com.itextpdf.kernel.geom.PageSize;

/**
 * 
 * <AUTHOR>
 *
 */
public class DocumentLayoutSetup {

	private boolean officeCopy;
	private PageSize pageSize;
	private float sideMargin;
	private float topMargin;
	private float bottomMargin;
	private float doubleContentMargin;
	private boolean noDoubleContentBorder;
	private int noOfCopies;

	public DocumentLayoutSetup(boolean officeCopy, PageSize pageSize,
			float sideMargin, float topMargin, float bottomMargin,
			float doubleContentMargin, int noOfCopies) {
		this.officeCopy = officeCopy;
		this.pageSize = pageSize;
		this.sideMargin = sideMargin;
		this.topMargin = topMargin;
		this.bottomMargin = bottomMargin;
		this.doubleContentMargin = doubleContentMargin;
		this.noOfCopies = noOfCopies;
	}

	public boolean isOfficeCopy() {
		return officeCopy;
	}

	public void setOfficeCopy(boolean officeCopy) {
		this.officeCopy = officeCopy;
	}

	public PageSize getPageSize() {
		return pageSize;
	}

	public void setPageSize(PageSize pageSize) {
		this.pageSize = pageSize;
	}

	public float getSideMargin() {
		return sideMargin;
	}

	public void setSideMargin(float sideMargin) {
		this.sideMargin = sideMargin;
	}

	public float getTopMargin() {
		return topMargin;
	}

	public void setTopMargin(float topMargin) {
		this.topMargin = topMargin;
	}

	public float getBottomMargin() {
		return bottomMargin;
	}

	public void setBottomMargin(float bottomMargin) {
		this.bottomMargin = bottomMargin;
	}

	public float getDoubleContentMargin() {
		return doubleContentMargin;
	}

	public void setDoubleContentMargin(float doubleContentMargin) {
		this.doubleContentMargin = doubleContentMargin;
	}

	public boolean getNoDoubleContentBorder() {
		return noDoubleContentBorder;
	}

	public void setNoDoubleContentBorder(boolean noDoubleContentBorder) {
		this.noDoubleContentBorder = noDoubleContentBorder;
	}

	public boolean isNoDoubleContentBorder() {
		return noDoubleContentBorder;
	}

	public int getNoOfCopies() {
		return noOfCopies;
	}

	public void setNoOfCopies(int noOfCopies) {
		this.noOfCopies = noOfCopies;
	}

	@Override
	public String toString() {
		return "DocumentLayoutSetup{" +
				"officeCopy=" + officeCopy +
				", pageSize=" + pageSize +
				", sideMargin=" + sideMargin +
				", topMargin=" + topMargin +
				", bottomMargin=" + bottomMargin +
				", doubleContentMargin=" + doubleContentMargin +
				", noDoubleContentBorder=" + noDoubleContentBorder +
				", noOfCopies=" + noOfCopies +
				'}';
	}

}
