package com.lernen.cloud.core.api.transport;

import com.lernen.cloud.core.api.common.TransactionMode;

import java.util.UUID;

public class VehicleLogPayload {
    private UUID logId;
    private int vehicleId;
    private UUID serviceRouteId;
    private Double tollTax;
    private Integer logDate;
    private UUID createdUserId;
    private UUID updatedUserId;
    private UUID transactionBy;
    private Double morningKmReading;
    private Double eveningKmReading;
    private String remarks;

    public VehicleLogPayload(){

    }

    public VehicleLogPayload(UUID logId, int vehicleId, UUID serviceRouteId, Double tollTax, Integer logDate, UUID createdUserId, UUID updatedUserId, UUID transactionBy, Double morningKmReading, Double eveningKmReading, String remarks) {
        this.logId = logId;
        this.vehicleId = vehicleId;
        this.serviceRouteId = serviceRouteId;
        this.tollTax = tollTax;
        this.logDate = logDate;
        this.createdUserId = createdUserId;
        this.updatedUserId = updatedUserId;
        this.transactionBy = transactionBy;
        this.morningKmReading = morningKmReading;
        this.eveningKmReading = eveningKmReading;
        this.remarks = remarks;
    }

    public UUID getLogId() {
        return logId;
    }

    public void setLogId(UUID logId) {
        this.logId = logId;
    }

    public int getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(int vehicleId) {
        this.vehicleId = vehicleId;
    }

    public UUID getServiceRouteId() {
        return serviceRouteId;
    }

    public void setServiceRouteId(UUID serviceRouteId) {
        this.serviceRouteId = serviceRouteId;
    }

    public Double getTollTax() {
        return tollTax;
    }

    public void setTollTax(Double tollTax) {
        this.tollTax = tollTax;
    }

    public Integer getLogDate() {
        return logDate;
    }

    public void setLogDate(Integer logDate) {
        this.logDate = logDate;
    }

    public UUID getCreatedUserId() {
        return createdUserId;
    }

    public void setCreatedUserId(UUID createdUserId) {
        this.createdUserId = createdUserId;
    }

    public UUID getUpdatedUserId() {
        return updatedUserId;
    }

    public void setUpdatedUserId(UUID updatedUserId) {
        this.updatedUserId = updatedUserId;
    }

    public UUID getTransactionBy() {
        return transactionBy;
    }

    public void setTransactionBy(UUID transactionBy) {
        this.transactionBy = transactionBy;
    }

    public Double getMorningKmReading() {
        return morningKmReading;
    }

    public void setMorningKmReading(Double morningKmReading) {
        this.morningKmReading = morningKmReading;
    }

    public Double getEveningKmReading() {
        return eveningKmReading;
    }

    public void setEveningKmReading(Double eveningKmReading) {
        this.eveningKmReading = eveningKmReading;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "VehicleExpensePayload{" +
                "logId=" + logId +
                ", vehicleId=" + vehicleId +
                ", serviceRouteId=" + serviceRouteId +
                ", tollTax=" + tollTax +
                ", logDate=" + logDate +
                ", createdUserId=" + createdUserId +
                ", updatedUserId=" + updatedUserId +
                ", transactionBy=" + transactionBy +
                ", morningKmReading=" + morningKmReading +
                ", eveningKmReading=" + eveningKmReading +
                ", remarks='" + remarks + '\'' +
                '}';
    }
}
