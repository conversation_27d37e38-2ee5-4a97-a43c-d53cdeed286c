package com.lernen.cloud.core.api.student;

import com.lernen.cloud.core.api.user.Gender;

public class StudentGuardianInfo {

	private String guardianName;
	
	private String relation;
	
	private Integer age;
	
	private Gender gender;
	
	private String occupation;

	private String email;
	
	private String contactNumber;
	
	private String address;
	
	private String city;
	
	private String state;
		
	private String zipcode;

	public String getGuardianName() {
		return guardianName;
	}

	public void setGuardianName(String guardianName) {
		this.guardianName = guardianName;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}

	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	public Gender getGender() {
		return gender;
	}

	public void setGender(Gender gender) {
		this.gender = gender;
	}

	public String getOccupation() {
		return occupation;
	}

	public void setOccupation(String occupation) {
		this.occupation = occupation;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getZipcode() {
		return zipcode;
	}

	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}

	@Override
	public String toString() {
		return "StudentGuardianInfo [guardianName=" + guardianName + ", relation=" + relation + ", age=" + age
				+ ", gender=" + gender + ", occupation=" + occupation + ", email=" + email + ", contactNumber="
				+ contactNumber + ", address=" + address + ", city=" + city + ", state=" + state + ", zipcode="
				+ zipcode + "]";
	}
	
}
