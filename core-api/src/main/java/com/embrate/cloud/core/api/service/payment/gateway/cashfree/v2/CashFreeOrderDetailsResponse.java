package com.embrate.cloud.core.api.service.payment.gateway.cashfree.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CashFreeOrderDetailsResponse {
	@JsonProperty("cf_order_id")
	private String cfOrderId;

	@JsonProperty("created_at")
	private String createdAt;

	@JsonProperty("customer_details")
	private CustomerDetailsResponse customerDetails;

	@JsonProperty("entity")
	private String entity;

	@JsonProperty("order_amount")
	private double orderAmount;

	@JsonProperty("payment_session_id")
	private String paymentSessionId;

	@JsonProperty("order_currency")
	private String orderCurrency;

	@JsonProperty("order_expiry_time")
	private String orderExpiryTime;

	@JsonProperty("order_id")
	private String orderId;

	@JsonProperty("order_meta")
	private OrderMetaResponse orderMeta;

	@JsonProperty("order_note")
	private String orderNote;

	@JsonProperty("order_splits")
	private Object[] orderSplits;

	@JsonProperty("order_status")
	private String orderStatus;

	@JsonProperty("order_tags")
	private Map<String, String> orderTags;

	@JsonProperty("terminal_data")
	private Object terminalData;

	@JsonProperty("cart_details")
	private CartDetailsResponse cartDetails;

	public String getCfOrderId() {
		return cfOrderId;
	}

	public void setCfOrderId(String cfOrderId) {
		this.cfOrderId = cfOrderId;
	}

	public String getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(String createdAt) {
		this.createdAt = createdAt;
	}

	public CustomerDetailsResponse getCustomerDetails() {
		return customerDetails;
	}

	public void setCustomerDetails(CustomerDetailsResponse customerDetails) {
		this.customerDetails = customerDetails;
	}

	public String getEntity() {
		return entity;
	}

	public void setEntity(String entity) {
		this.entity = entity;
	}

	public double getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(double orderAmount) {
		this.orderAmount = orderAmount;
	}

	public String getPaymentSessionId() {
		return paymentSessionId;
	}

	public void setPaymentSessionId(String paymentSessionId) {
		this.paymentSessionId = paymentSessionId;
	}

	public String getOrderCurrency() {
		return orderCurrency;
	}

	public void setOrderCurrency(String orderCurrency) {
		this.orderCurrency = orderCurrency;
	}

	public String getOrderExpiryTime() {
		return orderExpiryTime;
	}

	public void setOrderExpiryTime(String orderExpiryTime) {
		this.orderExpiryTime = orderExpiryTime;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public OrderMetaResponse getOrderMeta() {
		return orderMeta;
	}

	public void setOrderMeta(OrderMetaResponse orderMeta) {
		this.orderMeta = orderMeta;
	}

	public String getOrderNote() {
		return orderNote;
	}

	public void setOrderNote(String orderNote) {
		this.orderNote = orderNote;
	}

	public Object[] getOrderSplits() {
		return orderSplits;
	}

	public void setOrderSplits(Object[] orderSplits) {
		this.orderSplits = orderSplits;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public Map<String, String> getOrderTags() {
		return orderTags;
	}

	public void setOrderTags(Map<String, String> orderTags) {
		this.orderTags = orderTags;
	}

	public Object getTerminalData() {
		return terminalData;
	}

	public void setTerminalData(Object terminalData) {
		this.terminalData = terminalData;
	}

	public CartDetailsResponse getCartDetails() {
		return cartDetails;
	}

	public void setCartDetails(CartDetailsResponse cartDetails) {
		this.cartDetails = cartDetails;
	}

	@Override
	public String toString() {
		return "CashFreeOrderDetailsResponse{" +
				"cfOrderId='" + cfOrderId + '\'' +
				", createdAt='" + createdAt + '\'' +
				", customerDetails=" + customerDetails +
				", entity='" + entity + '\'' +
				", orderAmount=" + orderAmount +
				", paymentSessionId='" + paymentSessionId + '\'' +
				", orderCurrency='" + orderCurrency + '\'' +
				", orderExpiryTime='" + orderExpiryTime + '\'' +
				", orderId='" + orderId + '\'' +
				", orderMeta=" + orderMeta +
				", orderNote='" + orderNote + '\'' +
				", orderSplits=" + Arrays.toString(orderSplits) +
				", orderStatus='" + orderStatus + '\'' +
				", orderTags=" + orderTags +
				", terminalData=" + terminalData +
				", cartDetails=" + cartDetails +
				'}';
	}
}
