package com.embrate.cloud.core.api.dashboards.attendance;

/**
 * POJO class to hold student attendance count data from database queries
 * Used for attendance dashboard queries that return attendance counts by type and status
 * 
 * <AUTHOR>
 */
public class StudentAttendanceCount {

    private final int instituteId;
    private final int academicSessionId;
    private final String attendanceTypeName;
    private final String attendanceStatus;
    private final long count;

    public StudentAttendanceCount(int instituteId, int academicSessionId, String attendanceTypeName, 
                                String attendanceStatus, long count) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.attendanceTypeName = attendanceTypeName;
        this.attendanceStatus = attendanceStatus;
        this.count = count;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public String getAttendanceTypeName() {
        return attendanceTypeName;
    }

    public String getAttendanceStatus() {
        return attendanceStatus;
    }

    public long getCount() {
        return count;
    }

    @Override
    public String toString() {
        return "StudentAttendanceCount{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", attendanceTypeName='" + attendanceTypeName + '\'' +
                ", attendanceStatus='" + attendanceStatus + '\'' +
                ", count=" + count +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        StudentAttendanceCount that = (StudentAttendanceCount) o;

        if (instituteId != that.instituteId) return false;
        if (academicSessionId != that.academicSessionId) return false;
        if (count != that.count) return false;
        if (attendanceTypeName != null ? !attendanceTypeName.equals(that.attendanceTypeName) : that.attendanceTypeName != null)
            return false;
        return attendanceStatus != null ? attendanceStatus.equals(that.attendanceStatus) : that.attendanceStatus == null;
    }

    @Override
    public int hashCode() {
        int result = instituteId;
        result = 31 * result + academicSessionId;
        result = 31 * result + (attendanceTypeName != null ? attendanceTypeName.hashCode() : 0);
        result = 31 * result + (attendanceStatus != null ? attendanceStatus.hashCode() : 0);
        result = 31 * result + (int) (count ^ (count >>> 32));
        return result;
    }
}
