package com.embrate.cloud.core.api.attendance;

import com.lernen.cloud.core.api.attendance.StudentAttendancePayload;
import com.lernen.cloud.core.api.user.UserType;

import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class MarkStudentAbsentResult {

    private final boolean success;

    private final int academicSessionId;

    private final int attendanceDate;
    private final List<StudentAttendancePayload> absentMarkedStudents;


    public MarkStudentAbsentResult(boolean success, int academicSessionId, int attendanceDate, List<StudentAttendancePayload> absentMarkedStudents) {
        this.success = success;
        this.academicSessionId = academicSessionId;
        this.attendanceDate = attendanceDate;
        this.absentMarkedStudents = absentMarkedStudents;
    }

    public boolean isSuccess() {
        return success;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public int getAttendanceDate() {
        return attendanceDate;
    }

    public List<StudentAttendancePayload> getAbsentMarkedStudents() {
        return absentMarkedStudents;
    }

    public static MarkStudentAbsentResult forStudentFailure(int attendanceDate){
        return new MarkStudentAbsentResult(false, 0, attendanceDate, null);
    }

    @Override
    public String toString() {
        return "MarkStudentAbsentResult{" +
                "success=" + success +
                ", academicSessionId=" + academicSessionId +
                ", attendanceDate=" + attendanceDate +
                ", absentMarkedStudents=" + absentMarkedStudents +
                '}';
    }
}
