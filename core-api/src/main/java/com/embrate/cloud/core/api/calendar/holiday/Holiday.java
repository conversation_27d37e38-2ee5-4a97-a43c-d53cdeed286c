package com.embrate.cloud.core.api.calendar.holiday;

/**
 * <AUTHOR>
 */

public abstract class Holiday implements Comparable<Holiday> {
    private final static int SECONDS_IN_DAY = 86400;
    private final int holidayId;

    private final HolidayType holidayType;

    private final int start;

    private final int end;

    private final String name;

    private final String description;

    private final long addedAt;

    private final boolean singleDay;

    public Holiday(int holidayId, HolidayType holidayType, int start, int end, String name, String description, long addedAt) {
        this.holidayId = holidayId;
        this.holidayType = holidayType;
        this.start = start;
        this.end = end;
        this.name = name;
        this.description = description;
        this.addedAt = addedAt;
        this.singleDay = end - start <= SECONDS_IN_DAY;
    }

    public int getHolidayId() {
        return holidayId;
    }

    public HolidayType getHolidayType() {
        return holidayType;
    }

    public String getHolidayTypeDisplayName() {
        return holidayType.getDisplayName();
    }

    public int getStart() {
        return start;
    }

    public int getEnd() {
        return end;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public long getAddedAt() {
        return addedAt;
    }

    public boolean isSingleDay() {
        return singleDay;
    }

    @Override
    public int compareTo(Holiday o) {
        return this.getStart() - o.getStart();
    }

    @Override
    public String toString() {
        return "Holiday{" +
                "holidayId=" + holidayId +
                ", holidayType=" + holidayType +
                ", start=" + start +
                ", end=" + end +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", addedAt=" + addedAt +
                ", singleDay=" + singleDay +
                '}';
    }
}
