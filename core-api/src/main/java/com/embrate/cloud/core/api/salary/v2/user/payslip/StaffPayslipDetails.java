package com.embrate.cloud.core.api.salary.v2.user.payslip;

import com.embrate.cloud.core.api.salary.v2.payslip.PayslipMetadata;
import com.embrate.cloud.core.api.salary.v2.payslip.StaffPayslipMetadata;
import com.embrate.cloud.core.api.salary.v2.user.StaffSalaryCycleStructureDetails;
import com.embrate.cloud.core.api.salary.v2.utils.StaffSalaryAttendanceSummary;
import com.lernen.cloud.core.api.staff.Staff;

import java.util.List;

/**
 * <AUTHOR>
 */

public class StaffPayslipDetails {

    private final List<GeneratedStaffPayslip> generatedStaffPayslipList;

    private final List<StaffPayslipMetadata> submittedPayslips;

    public StaffPayslipDetails(List<GeneratedStaffPayslip> generatedStaffPayslipList, List<StaffPayslipMetadata> submittedPayslips) {
        this.generatedStaffPayslipList = generatedStaffPayslipList;
        this.submittedPayslips = submittedPayslips;
    }

    public List<GeneratedStaffPayslip> getGeneratedStaffPayslipList() {
        return generatedStaffPayslipList;
    }

    public List<StaffPayslipMetadata> getSubmittedPayslips() {
        return submittedPayslips;
    }

    @Override
    public String toString() {
        return "StaffPayslipDetails{" +
                "generatedStaffPayslipList=" + generatedStaffPayslipList +
                ", submittedPayslips=" + submittedPayslips +
                '}';
    }
}
