package com.embrate.cloud.core.api.inventory.v2;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.inventory.PaymentStatus;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class NewPurchasePayload {

    private UUID supplierId;

    private Integer inventoryUserInstituteId;

    private TransactionMode transactionMode;

    private PaymentStatus paymentStatus;

    private Double paymentAmount;

    private String reference;

    private long transactionDate;

    private String description;

    private Double additionalCost;

    private Double additionalDiscount;

    private List<TradeProductPayload> purchasedProductPayloadList;

    public NewPurchasePayload() {
    }

    public NewPurchasePayload(UUID supplierId, Integer inventoryUserInstituteId, TransactionMode transactionMode, PaymentStatus paymentStatus, Double paymentAmount, String reference, long transactionDate, String description, Double additionalCost, Double additionalDiscount, List<TradeProductPayload> purchasedProductPayloadList) {
        this.supplierId = supplierId;
        this.inventoryUserInstituteId = inventoryUserInstituteId;
        this.transactionMode = transactionMode;
        this.paymentStatus = paymentStatus;
        this.paymentAmount = paymentAmount;
        this.reference = reference;
        this.transactionDate = transactionDate;
        this.description = description;
        this.additionalCost = additionalCost;
        this.additionalDiscount = additionalDiscount;
        this.purchasedProductPayloadList = purchasedProductPayloadList;
    }

    public UUID getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(UUID supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getInventoryUserInstituteId() {
        return inventoryUserInstituteId;
    }

    public void setInventoryUserInstituteId(Integer inventoryUserInstituteId) {
        this.inventoryUserInstituteId = inventoryUserInstituteId;
    }

    public TransactionMode getTransactionMode() {
        return transactionMode;
    }

    public void setTransactionMode(TransactionMode transactionMode) {
        this.transactionMode = transactionMode;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public Double getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Double paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public long getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(long transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getAdditionalCost() {
        return additionalCost;
    }

    public void setAdditionalCost(Double additionalCost) {
        this.additionalCost = additionalCost;
    }

    public Double getAdditionalDiscount() {
        return additionalDiscount;
    }

    public void setAdditionalDiscount(Double additionalDiscount) {
        this.additionalDiscount = additionalDiscount;
    }

    public List<TradeProductPayload> getPurchasedProductPayloadList() {
        return purchasedProductPayloadList;
    }

    public void setPurchasedProductPayloadList(List<TradeProductPayload> purchasedProductPayloadList) {
        this.purchasedProductPayloadList = purchasedProductPayloadList;
    }

    @Override
    public String toString() {
        return "NewPurchasePayload{" +
                "supplierId=" + supplierId +
                ", inventoryUserInstituteId=" + inventoryUserInstituteId +
                ", transactionMode=" + transactionMode +
                ", paymentStatus=" + paymentStatus +
                ", paymentAmount=" + paymentAmount +
                ", reference='" + reference + '\'' +
                ", transactionDate=" + transactionDate +
                ", description='" + description + '\'' +
                ", additionalCost=" + additionalCost +
                ", additionalDiscount=" + additionalDiscount +
                ", purchasedProductPayloadList=" + purchasedProductPayloadList +
                '}';
    }
}
