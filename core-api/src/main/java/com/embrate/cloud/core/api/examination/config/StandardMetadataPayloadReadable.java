package com.embrate.cloud.core.api.examination.config;

import java.util.List;

public class StandardMetadataPayloadReadable {

	private int instituteId;
	private int academicSessionId;
	private List<String> standardNameList;
	private boolean isScholasticGradingEnabled;
	private boolean isCoscholasticGradingEnabled;

	public StandardMetadataPayloadReadable() {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.standardNameList = standardNameList;
		this.isScholasticGradingEnabled = isScholasticGradingEnabled;
		this.isCoscholasticGradingEnabled = isCoscholasticGradingEnabled;
	}

	public int getInstituteId() { return instituteId;}

	public void setInstituteId(int instituteId) { this.instituteId = instituteId;}

	public int getAcademicSessionId() { return academicSessionId;}

	public void setAcademicSessionId(int academicSessionId) { this.academicSessionId = academicSessionId;}

	public List<String> getStandardNameList() {
		return standardNameList;
	}

	public void setStandardNameList(List<String> standardNameList) {
		this.standardNameList = standardNameList;
	}

	public boolean isScholasticGradingEnabled() {
		return isScholasticGradingEnabled;
	}

	public void setScholasticGradingEnabled(boolean isScholasticGradingEnabled) {
		this.isScholasticGradingEnabled = isScholasticGradingEnabled;
	}

	public boolean isCoscholasticGradingEnabled() {
		return isCoscholasticGradingEnabled;
	}

	public void setCoscholasticGradingEnabled(boolean isCoscholasticGradingEnabled) {
		this.isCoscholasticGradingEnabled = isCoscholasticGradingEnabled;
	}

	@Override
	public String toString() {
		return "StandardMetadataPayloadReadable{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", standardNameList=" + standardNameList +
				", isScholasticGradingEnabled=" + isScholasticGradingEnabled +
				", isCoscholasticGradingEnabled=" + isCoscholasticGradingEnabled +
				'}';
	}
}
