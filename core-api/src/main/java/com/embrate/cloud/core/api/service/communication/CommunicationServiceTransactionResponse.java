package com.embrate.cloud.core.api.service.communication;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class CommunicationServiceTransactionResponse {

    private final UUID transactionId;
    private final String errorReason;

    public CommunicationServiceTransactionResponse(UUID transactionId, String errorReason) {
        this.transactionId = transactionId;
        this.errorReason = errorReason;
    }

    public UUID getTransactionId() {
        return transactionId;
    }

    public String getErrorReason() {
        return errorReason;
    }

    @Override
    public String toString() {
        return "CommunicationServiceTransactionResponse{" +
                "transactionId=" + transactionId +
                ", errorReason='" + errorReason + '\'' +
                '}';
    }
}
