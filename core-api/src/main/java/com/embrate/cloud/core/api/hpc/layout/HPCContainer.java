package com.embrate.cloud.core.api.hpc.layout;

import com.embrate.cloud.core.api.hpc.utils.HPCExamType;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HPCContainer extends HPCElement {

	private String id;

	private HPCElementAlignment childAlignment;

	private Float overrideHeight;

	private HPCExamType associatedExamType;

	private List<HPCContainer> childContainers;

	private HPCImageContainer imageContainer;

	private List<HPCTextElement> textElements;

	private HPCTable table;

	private HPCPDFAttributes pdfAttributes;

	@Override
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public HPCElementAlignment getChildAlignment() {
		return childAlignment;
	}

	public void setChildAlignment(HPCElementAlignment childAlignment) {
		this.childAlignment = childAlignment;
	}

	public Float getOverrideHeight() {
		return overrideHeight;
	}

	public void setOverrideHeight(Float overrideHeight) {
		this.overrideHeight = overrideHeight;
	}

	public HPCExamType getAssociatedExamType() {
		return associatedExamType;
	}

	public void setAssociatedExamType(HPCExamType associatedExamType) {
		this.associatedExamType = associatedExamType;
	}

	public List<HPCContainer> getChildContainers() {
		return childContainers;
	}

	public void setChildContainers(List<HPCContainer> childContainers) {
		this.childContainers = childContainers;
	}

	public HPCImageContainer getImageContainer() {
		return imageContainer;
	}

	public void setImageContainer(HPCImageContainer imageContainer) {
		this.imageContainer = imageContainer;
	}

	public List<HPCTextElement> getTextElements() {
		return textElements;
	}

	public void setTextElements(List<HPCTextElement> textElements) {
		this.textElements = textElements;
	}

	public HPCTable getTable() {
		return table;
	}

	public void setTable(HPCTable table) {
		this.table = table;
	}

	public HPCPDFAttributes getPdfAttributes() {
		return pdfAttributes;
	}

	public void setPdfAttributes(HPCPDFAttributes pdfAttributes) {
		this.pdfAttributes = pdfAttributes;
	}

	@Override
	public String toString() {
		return "HPCContainer{" +
				"id='" + id + '\'' +
				", childAlignment=" + childAlignment +
				", overrideHeight=" + overrideHeight +
				", associatedExamType=" + associatedExamType +
				", childContainers=" + childContainers +
				", imageContainer=" + imageContainer +
				", textElements=" + textElements +
				", table=" + table +
				", pdfAttributes=" + pdfAttributes +
				'}';
	}
}
