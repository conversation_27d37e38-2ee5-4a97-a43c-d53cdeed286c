/**
 * 
 */
package com.embrate.cloud.core.api.whatsapp;

import com.embrate.cloud.core.api.service.communication.UserCommunicationServicePayload;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class UserWhatsappPayload extends UserCommunicationServicePayload {

	private final String message;

	private final String imageLink;

	private final List<String> headerVariableValues;

	private final List<String> bodyVariableValues;

	/**
	 * @param userId
	 * @param mobileNumbers
	 * @param message
	 */
	public UserWhatsappPayload(UUID userId, String message, List<String> mobileNumbers, String dltTemplateId, String imageLink, List<String> headerVariableValues, List<String> bodyVariableValues) {
		super(userId, mobileNumbers, null, 1, dltTemplateId, imageLink, headerVariableValues, bodyVariableValues);
		this.message = message;
		this.imageLink = imageLink;
		this.headerVariableValues = headerVariableValues;
		this.bodyVariableValues = bodyVariableValues;
	}

	@Override
	public String getTitle() {
		return null;
	}

	@Override
	public String getMessagePayload() {
		return message;
	}

	@Override
	public String getDisplayMessage() {
		return message;
	}

	@Override
	public Float getMessageDuration() {
		return null;
	}

	public String getMessage() {
		return message;
	}

	public String getImageLink() {
		return imageLink;
	}

	public List<String> getHeaderVariableValues() {
		return headerVariableValues;
	}

	public List<String> getBodyVariableValues() {
		return bodyVariableValues;
	}

	@Override
	public String toString() {
		return "UserWhatsappPayload{" +
				"message='" + message + '\'' +
				", imageLink='" + imageLink + '\'' +
				", headerVariableValues=" + headerVariableValues +
				", bodyVariableValues=" + bodyVariableValues +
				'}';
	}
}