/**
 * 
 */
package com.embrate.cloud.core.api.timetable;

import java.time.DayOfWeek;
import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.institute.Time;

/**
 * <AUTHOR>
 *
 */
public class SchoolShiftDetailsRow {
	
	private final int instituteId;
	
	private final int academicSessionId;
	
	private final UUID shiftId;
	
	private final String shiftName;
	
	private final Time shiftStartTime;
	
	private final Time shiftEndTime;
	
	private final UUID createdBy;
	
	private final Integer createdOn;
	
	private final UUID updatedBy;
	
	private final Integer updatedOn;
	
	private final List<DayOfWeek> days;
	
	private final UUID periodId;
	
	private boolean havePeriod;
	
	private final String periodName;
	
	private final Time periodStartTime;
	
	private final Time periodEndTime;

	/**
	 * @param instituteId
	 * @param academicSessionId
	 * @param shiftId
	 * @param shiftName
	 * @param shiftStartTime
	 * @param shiftEndTime
	 * @param createdBy
	 * @param createdOn
	 * @param updatedBy
	 * @param updatedOn
	 * @param days
	 * @param periodId
	 * @param periodName
	 * @param periodStartTime
	 * @param periodEndTime
	 */
	public SchoolShiftDetailsRow(int instituteId, int academicSessionId, UUID shiftId, String shiftName,
			Time shiftStartTime, Time shiftEndTime, UUID createdBy, Integer createdOn, UUID updatedBy,
			Integer updatedOn, List<DayOfWeek> days, UUID periodId, boolean havePeriod, String periodName, 
			Time periodStartTime, Time periodEndTime) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.shiftId = shiftId;
		this.shiftName = shiftName;
		this.shiftStartTime = shiftStartTime;
		this.shiftEndTime = shiftEndTime;
		this.createdBy = createdBy;
		this.createdOn = createdOn;
		this.updatedBy = updatedBy;
		this.updatedOn = updatedOn;
		this.days = days;
		this.periodId = periodId;
		this.havePeriod = havePeriod;
		this.periodName = periodName;
		this.periodStartTime = periodStartTime;
		this.periodEndTime = periodEndTime;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @return the academicSessionId
	 */
	public int getAcademicSessionId() {
		return academicSessionId;
	}

	/**
	 * @return the shiftId
	 */
	public UUID getShiftId() {
		return shiftId;
	}

	/**
	 * @return the shiftName
	 */
	public String getShiftName() {
		return shiftName;
	}

	/**
	 * @return the shiftStartTime
	 */
	public Time getShiftStartTime() {
		return shiftStartTime;
	}

	/**
	 * @return the shiftEndTime
	 */
	public Time getShiftEndTime() {
		return shiftEndTime;
	}

	/**
	 * @return the createdBy
	 */
	public UUID getCreatedBy() {
		return createdBy;
	}

	/**
	 * @return the createdOn
	 */
	public Integer getCreatedOn() {
		return createdOn;
	}

	/**
	 * @return the updatedBy
	 */
	public UUID getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @return the updatedOn
	 */
	public Integer getUpdatedOn() {
		return updatedOn;
	}

	/**
	 * @return the days
	 */
	public List<DayOfWeek> getDays() {
		return days;
	}

	/**
	 * @return the periodId
	 */
	public UUID getPeriodId() {
		return periodId;
	}

	
	/**
	 * @return the havePeriod
	 */
	public boolean isHavePeriod() {
		return havePeriod;
	}

	/**
	 * @param havePeriod the havePeriod to set
	 */
	public void setHavePeriod(boolean havePeriod) {
		this.havePeriod = havePeriod;
	}

	/**
	 * @return the periodName
	 */
	public String getPeriodName() {
		return periodName;
	}

	/**
	 * @return the periodStartTime
	 */
	public Time getPeriodStartTime() {
		return periodStartTime;
	}

	/**
	 * @return the periodEndTime
	 */
	public Time getPeriodEndTime() {
		return periodEndTime;
	}

	@Override
	public String toString() {
		return "SchoolShiftDetailsRow [instituteId=" + instituteId + ", academicSessionId=" + academicSessionId
				+ ", shiftId=" + shiftId + ", shiftName=" + shiftName + ", shiftStartTime=" + shiftStartTime
				+ ", shiftEndTime=" + shiftEndTime + ", createdBy=" + createdBy + ", createdOn=" + createdOn
				+ ", updatedBy=" + updatedBy + ", updatedOn=" + updatedOn + ", days=" + days + ", periodId=" + periodId
				+ ", havePeriod=" + havePeriod + ", periodName=" + periodName + ", periodStartTime=" + periodStartTime
				+ ", periodEndTime=" + periodEndTime + "]";
	}
}
