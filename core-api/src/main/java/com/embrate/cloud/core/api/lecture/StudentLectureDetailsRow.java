/**
 * 
 */
package com.embrate.cloud.core.api.lecture;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.student.StudentLite;

/**
 * <AUTHOR>
 *
 */
public class StudentLectureDetailsRow {
	
	private int instituteId;
	
	private StudentLite studentLite;
	
	private Course course;
	
	private LectureDetails lectureDetails;
	
	private StudentLectureViewDetails studentLectureViewDetails;
	
	private StudentLectureCompleteDetails studentLectureCompleteDetails;

	/**
	 * @param instituteId
	 * @param studentLite
	 * @param course
	 * @param lectureDetails
	 * @param studentLectureViewDetails
	 * @param studentLectureCompleteDetails
	 */
	public StudentLectureDetailsRow(int instituteId, StudentLite studentLite, Course course,
			LectureDetails lectureDetails, StudentLectureViewDetails studentLectureViewDetails,
			StudentLectureCompleteDetails studentLectureCompleteDetails) {
		this.instituteId = instituteId;
		this.studentLite = studentLite;
		this.course = course;
		this.lectureDetails = lectureDetails;
		this.studentLectureViewDetails = studentLectureViewDetails;
		this.studentLectureCompleteDetails = studentLectureCompleteDetails;
	}

	/**
	 * 
	 */
	public StudentLectureDetailsRow() {
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the studentLite
	 */
	public StudentLite getStudentLite() {
		return studentLite;
	}

	/**
	 * @param studentLite the studentLite to set
	 */
	public void setStudentLite(StudentLite studentLite) {
		this.studentLite = studentLite;
	}

	/**
	 * @return the course
	 */
	public Course getCourse() {
		return course;
	}

	/**
	 * @param course the course to set
	 */
	public void setCourse(Course course) {
		this.course = course;
	}

	/**
	 * @return the lectureDetails
	 */
	public LectureDetails getLectureDetails() {
		return lectureDetails;
	}

	/**
	 * @param lectureDetails the lectureDetails to set
	 */
	public void setLectureDetails(LectureDetails lectureDetails) {
		this.lectureDetails = lectureDetails;
	}

	/**
	 * @return the studentLectureViewDetails
	 */
	public StudentLectureViewDetails getStudentLectureViewDetails() {
		return studentLectureViewDetails;
	}

	/**
	 * @param studentLectureViewDetails the studentLectureViewDetails to set
	 */
	public void setStudentLectureViewDetails(StudentLectureViewDetails studentLectureViewDetails) {
		this.studentLectureViewDetails = studentLectureViewDetails;
	}

	/**
	 * @return the studentLectureCompleteDetails
	 */
	public StudentLectureCompleteDetails getStudentLectureCompleteDetails() {
		return studentLectureCompleteDetails;
	}

	/**
	 * @param studentLectureCompleteDetails the studentLectureCompleteDetails to set
	 */
	public void setStudentLectureCompleteDetails(StudentLectureCompleteDetails studentLectureCompleteDetails) {
		this.studentLectureCompleteDetails = studentLectureCompleteDetails;
	}

	@Override
	public String toString() {
		return "StudentLectureDetailsRow [instituteId=" + instituteId + ", studentLite=" + studentLite + ", course="
				+ course + ", lectureDetails=" + lectureDetails + ", studentLectureViewDetails="
				+ studentLectureViewDetails + ", studentLectureCompleteDetails=" + studentLectureCompleteDetails + "]";
	}
}
