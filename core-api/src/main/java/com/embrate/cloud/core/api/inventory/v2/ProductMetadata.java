package com.embrate.cloud.core.api.inventory.v2;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class ProductMetadata {

    private final UUID outletId;

    private final UUID skuId;

    private final String name;

    private final int categoryId;

    private final UUID brandId;

    private final String description;

    public ProductMetadata(UUID outletId, UUID skuId, String name, int categoryId, UUID brandId, String description) {
        this.outletId = outletId;
        this.skuId = skuId;
        this.name = name;
        this.categoryId = categoryId;
        this.brandId = brandId;
        this.description = description;
    }

    public UUID getOutletId() {
        return outletId;
    }

    public UUID getSkuId() {
        return skuId;
    }

    public String getName() {
        return name;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public UUID getBrandId() {
        return brandId;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return "ProductMetadata{" +
                "outletId=" + outletId +
                ", skuId=" + skuId +
                ", name='" + name + '\'' +
                ", categoryId=" + categoryId +
                ", brandId=" + brandId +
                ", description='" + description + '\'' +
                '}';
    }
}
