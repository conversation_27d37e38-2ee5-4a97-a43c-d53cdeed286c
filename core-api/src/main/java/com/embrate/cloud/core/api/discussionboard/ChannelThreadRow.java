/**
 * 
 */
package com.embrate.cloud.core.api.discussionboard;

import com.lernen.cloud.core.api.user.User;

/**
 * <AUTHOR>
 *
 */
public class ChannelThreadRow {
	
	private ChannelDetailsPayload channelDetailsPayload;
	
	private ThreadDetailsPayload threadDetailsPayload;
	
	private User user;

	/**
	 * @param instituteId
	 * @param channelDetailsPayload
	 * @param threadDetailsPayload
	 * @param user
	 */
	public ChannelThreadRow(ChannelDetailsPayload channelDetailsPayload,
			ThreadDetailsPayload threadDetailsPayload, User user) {
		this.channelDetailsPayload = channelDetailsPayload;
		this.threadDetailsPayload = threadDetailsPayload;
		this.user = user;
	}

	/**
	 * 
	 */
	public ChannelThreadRow() {
	}

	/**
	 * @return the channelDetailsPayload
	 */
	public ChannelDetailsPayload getChannelDetailsPayload() {
		return channelDetailsPayload;
	}

	/**
	 * @param channelDetailsPayload the channelDetailsPayload to set
	 */
	public void setChannelDetailsPayload(ChannelDetailsPayload channelDetailsPayload) {
		this.channelDetailsPayload = channelDetailsPayload;
	}

	/**
	 * @return the threadDetailsPayload
	 */
	public ThreadDetailsPayload getThreadDetailsPayload() {
		return threadDetailsPayload;
	}

	/**
	 * @param threadDetailsPayload the threadDetailsPayload to set
	 */
	public void setThreadDetailsPayload(ThreadDetailsPayload threadDetailsPayload) {
		this.threadDetailsPayload = threadDetailsPayload;
	}

	/**
	 * @return the user
	 */
	public User getUser() {
		return user;
	}

	/**
	 * @param user the user to set
	 */
	public void setUser(User user) {
		this.user = user;
	}

	@Override
	public String toString() {
		return "ChannelThreadRow [channelDetailsPayload=" + channelDetailsPayload
				+ ", threadDetailsPayload=" + threadDetailsPayload + ", user=" + user + "]";
	}

}
