package com.embrate.cloud.core.api.service.payment.gateway;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

public enum  PaymentGatewayStage {
    TEST, PROD;

    public static PaymentGatewayStage getPaymentGatewayStage(String stage) {
        if (StringUtils.isBlank(stage)) {
            return null;
        }
        for (PaymentGatewayStage paymentGatewayStage : PaymentGatewayStage
                .values()) {
            if (paymentGatewayStage.name()
                    .equalsIgnoreCase(stage)) {
                return paymentGatewayStage;
            }
        }
        return null;
    }
}
