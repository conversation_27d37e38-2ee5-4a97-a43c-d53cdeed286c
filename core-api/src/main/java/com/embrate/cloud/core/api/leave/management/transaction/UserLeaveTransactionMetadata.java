package com.embrate.cloud.core.api.leave.management.transaction;

import com.embrate.cloud.core.api.calendar.holiday.Holiday;
import com.embrate.cloud.core.api.leave.management.LeaveDocumentType;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.UserType;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class UserLeaveTransactionMetadata implements Comparable<UserLeaveTransactionMetadata>{

    private  UUID transactionId;

    private  int academicSessionId;

    private  UUID userId;

    private UserType userType;

    private  LeaveTransactionType transactionType;

    private  LeaveTransactionStatus transactionStatus;

    private  LeaveTransactionCategory category;

    private  UUID appliedBy;

    private  int appliedAt;

    private  String description;

    private int attachmentCount;

    private long attachmentSize;

    private String allowedMimeTypes;
    private List<Document<LeaveDocumentType>> leaveAttachments;

    private  UUID reviewedBy;

    private  Integer reviewedAt;

    private  String reviewRemarks;

    public UserLeaveTransactionMetadata() {
    }

    public UserLeaveTransactionMetadata(UUID transactionId, int academicSessionId, UUID userId, UserType userType,
                                        LeaveTransactionType transactionType, LeaveTransactionStatus transactionStatus,
                                        LeaveTransactionCategory category, UUID appliedBy, int appliedAt,
                                        String description, List<Document<LeaveDocumentType>> leaveAttachments,
                                        UUID reviewedBy, Integer reviewedAt, String reviewRemarks) {
        this.transactionId = transactionId;
        this.academicSessionId = academicSessionId;
        this.userId = userId;
        this.userType = userType;
        this.transactionType = transactionType;
        this.transactionStatus = transactionStatus;
        this.category = category;
        this.appliedBy = appliedBy;
        this.appliedAt = appliedAt;
        this.description = description;
        this.leaveAttachments = leaveAttachments;
        this.reviewedBy = reviewedBy;
        this.reviewedAt = reviewedAt;
        this.reviewRemarks = reviewRemarks;
        this.attachmentCount = 10;
        this.attachmentSize = 1024 * 1024 * 5;
        this.allowedMimeTypes = "image/*,application/pdf";
    }

    public UUID getTransactionId() {
        return transactionId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public UUID getUserId() {
        return userId;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public int getAttachmentCount() {
        return attachmentCount;
    }

    public void setAttachmentCount(int attachmentCount) {
        this.attachmentCount = attachmentCount;
    }

    public long getAttachmentSize() {
        return attachmentSize;
    }

    public void setAttachmentSize(long attachmentSize) {
        this.attachmentSize = attachmentSize;
    }

    public String getAllowedMimeTypes() {
        return allowedMimeTypes;
    }

    public void setAllowedMimeTypes(String allowedMimeTypes) {
        this.allowedMimeTypes = allowedMimeTypes;
    }

    public LeaveTransactionType getTransactionType() {
        return transactionType;
    }

    public LeaveTransactionStatus getTransactionStatus() {
        return transactionStatus;
    }

    public LeaveTransactionCategory getCategory() {
        return category;
    }

    public UUID getAppliedBy() {
        return appliedBy;
    }

    public int getAppliedAt() {
        return appliedAt;
    }

    public String getDescription() {
        return description;
    }

    public List<Document<LeaveDocumentType>> getLeaveAttachments() {
        return leaveAttachments;
    }

    public void setLeaveAttachments(List<Document<LeaveDocumentType>> leaveAttachments) {
        this.leaveAttachments = leaveAttachments;
    }

    public UUID getReviewedBy() {
        return reviewedBy;
    }

    public Integer getReviewedAt() {
        return reviewedAt;
    }

    public String getReviewRemarks() {
        return reviewRemarks;
    }

    public void setTransactionId(UUID transactionId) {
        this.transactionId = transactionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public void setTransactionType(LeaveTransactionType transactionType) {
        this.transactionType = transactionType;
    }

    public void setTransactionStatus(LeaveTransactionStatus transactionStatus) {
        this.transactionStatus = transactionStatus;
    }

    public void setCategory(LeaveTransactionCategory category) {
        this.category = category;
    }

    public void setAppliedBy(UUID appliedBy) {
        this.appliedBy = appliedBy;
    }

    public void setAppliedAt(int appliedAt) {
        this.appliedAt = appliedAt;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setReviewedBy(UUID reviewedBy) {
        this.reviewedBy = reviewedBy;
    }

    public void setReviewedAt(Integer reviewedAt) {
        this.reviewedAt = reviewedAt;
    }

    public void setReviewRemarks(String reviewRemarks) {
        this.reviewRemarks = reviewRemarks;
    }

    @Override
    public String toString() {
        return "UserLeaveTransactionMetadata{" +
                "transactionId=" + transactionId +
                ", academicSessionId=" + academicSessionId +
                ", userId=" + userId +
                ", transactionType=" + transactionType +
                ", transactionStatus=" + transactionStatus +
                ", category=" + category +
                ", appliedBy=" + appliedBy +
                ", appliedAt=" + appliedAt +
                ", description='" + description + '\'' +
                ", leaveAttachments=" + leaveAttachments +
                ", reviewedBy=" + reviewedBy +
                ", reviewedAt=" + reviewedAt +
                ", reviewRemarks='" + reviewRemarks + '\'' +
                '}';
    }

    @Override
    public int compareTo(UserLeaveTransactionMetadata o) {
        return 0;
    }
}
