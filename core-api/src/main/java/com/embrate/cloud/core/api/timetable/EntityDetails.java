package com.embrate.cloud.core.api.timetable;

import java.util.UUID;

public class EntityDetails {

    private final Entity entity;

    private final UUID entityId;

    private final String entityIdVal;

    public EntityDetails(Entity entity, UUID entityId, String entityIdVal) {
        this.entity = entity;
        this.entityId = entityId;
        this.entityIdVal = entityIdVal;
    }

    public Entity getEntity() {
        return entity;
    }

    public UUID getEntityId() {
        return entityId;
    }

    public String getEntityIdVal() {
        return entityIdVal;
    }

    @Override
    public String toString() {
        return "EntityDetails{" +
                "entity=" + entity +
                ", entityId=" + entityId +
                ", entityIdVal='" + entityIdVal + '\'' +
                '}';
    }
}
