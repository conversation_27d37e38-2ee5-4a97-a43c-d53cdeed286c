package com.embrate.cloud.core.api.salary;

public class SalaryCycleDetailsReadable {

	private String startDate;

	private String endDate;

	private String cycleName;

	public SalaryCycleDetailsReadable() {
		this.startDate = startDate;
		this.cycleName = cycleName;
		this.endDate = endDate;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getCycleName() {
		return cycleName;
	}

	public void setCycleName(String cycleName) {
		this.cycleName = cycleName;
	}

	@Override
	public String toString() {
		return "SalaryCycleDetailsReadable{" +
				"startDate='" + startDate + '\'' +
				", endDate='" + endDate + '\'' +
				", cycleName='" + cycleName + '\'' +
				'}';
	}
}
