package com.embrate.cloud.core.api.whatsapp.webpay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class WebPaySendWhatsappResponse {

	@JsonProperty("success")
	private String success;

	@JsonProperty("statusDesc")
	private String statusDesc;

	@JsonProperty("statusCode")
	private int statusCode;

	@JsonProperty("data")
	private List<WebPaySendWhatsappData> data;

	public String getSuccess() {
		return success;
	}

	public void setSuccess(String success) {
		this.success = success;
	}

	public String getStatusDesc() {
		return statusDesc;
	}

	public void setStatusDesc(String statusDesc) {
		this.statusDesc = statusDesc;
	}

	public int getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(int statusCode) {
		this.statusCode = statusCode;
	}

	public List<WebPaySendWhatsappData> getData() {
		return data;
	}

	public void setData(List<WebPaySendWhatsappData> data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return "WebPaySendWhatsappResponse{" +
				"success='" + success + '\'' +
				", statusDesc='" + statusDesc + '\'' +
				", statusCode=" + statusCode +
				", data=" + data +
				'}';
	}
}
