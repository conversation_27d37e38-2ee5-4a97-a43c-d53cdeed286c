package com.embrate.cloud.core.api.inventory.v2.sale;

import com.embrate.cloud.core.api.inventory.v2.TradeProductBatchPayload;
import com.embrate.cloud.core.api.inventory.v2.TradeProductPayload;
import com.embrate.cloud.core.api.inventory.v2.product.group.TradeProductGroupPayload;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.inventory.InventoryUserType;
import com.lernen.cloud.core.api.inventory.PaymentStatus;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExchangeOrderPayload {

    private ReturnOrderPayload returnOrderPayload;

    private NewTradePayload newTradePayload;

    public ReturnOrderPayload getReturnOrderPayload() {
        return returnOrderPayload;
    }

    public void setReturnOrderPayload(ReturnOrderPayload returnOrderPayload) {
        this.returnOrderPayload = returnOrderPayload;
    }

    public NewTradePayload getNewTradePayload() {
        return newTradePayload;
    }

    public void setNewTradePayload(NewTradePayload newTradePayload) {
        this.newTradePayload = newTradePayload;
    }

    @Override
    public String toString() {
        return "ExchangeOrderPayload{" +
                "returnOrderPayload=" + returnOrderPayload +
                ", newTradePayload=" + newTradePayload +
                '}';
    }
}
