/**
 * 
 */
package com.embrate.cloud.core.api.discussionboard;

import java.util.UUID;

import com.lernen.cloud.core.api.user.UserType;

/**
 * <AUTHOR>
 *
 */
public class ThreadDetailsPayload {
	
	private UUID threadId;
	
	private UUID channelId;
	
	private String name;
	
	private String description;
	
	private UUID createdBy;
	
	private Integer createdTimestamp;
	
	private Integer updatedTimestamp;

	private String attachments;
	
	private UserType userType;

	/**
	 * @param threadId
	 * @param channelId
	 * @param name
	 * @param description
	 * @param createdBy
	 * @param createdTimestamp
	 * @param updatedTimestamp
	 * @param attachments
	 * @param userType
	 */
	public ThreadDetailsPayload(UUID threadId, UUID channelId, String name, String description, UUID createdBy,
			Integer createdTimestamp, Integer updatedTimestamp, String attachments, UserType userType) {
		this.threadId = threadId;
		this.channelId = channelId;
		this.name = name;
		this.description = description;
		this.createdBy = createdBy;
		this.createdTimestamp = createdTimestamp;
		this.updatedTimestamp = updatedTimestamp;
		this.attachments = attachments;
		this.userType = userType;
	}

	/**
	 * @param threadId
	 * @param channelId
	 * @param name
	 * @param description
	 * @param createdBy
	 * @param attachments
	 * @param userType
	 */
	public ThreadDetailsPayload(UUID threadId, UUID channelId, String name, String description, UUID createdBy,
			String attachments, UserType userType) {
		this.threadId = threadId;
		this.channelId = channelId;
		this.name = name;
		this.description = description;
		this.createdBy = createdBy;
		this.attachments = attachments;
		this.userType = userType;
	}

	/**
	 * 
	 */
	public ThreadDetailsPayload() {
	}

	/**
	 * @return the threadId
	 */
	public UUID getThreadId() {
		return threadId;
	}

	/**
	 * @param threadId the threadId to set
	 */
	public void setThreadId(UUID threadId) {
		this.threadId = threadId;
	}

	/**
	 * @return the channelId
	 */
	public UUID getChannelId() {
		return channelId;
	}

	/**
	 * @param channelId the channelId to set
	 */
	public void setChannelId(UUID channelId) {
		this.channelId = channelId;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}

	/**
	 * @param name the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @param description the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * @return the createdBy
	 */
	public UUID getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(UUID createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the createdTimestamp
	 */
	public Integer getCreatedTimestamp() {
		return createdTimestamp;
	}

	/**
	 * @param createdTimestamp the createdTimestamp to set
	 */
	public void setCreatedTimestamp(Integer createdTimestamp) {
		this.createdTimestamp = createdTimestamp;
	}

	/**
	 * @return the updatedTimestamp
	 */
	public Integer getUpdatedTimestamp() {
		return updatedTimestamp;
	}

	/**
	 * @param updatedTimestamp the updatedTimestamp to set
	 */
	public void setUpdatedTimestamp(Integer updatedTimestamp) {
		this.updatedTimestamp = updatedTimestamp;
	}

	/**
	 * @return the attachments
	 */
	public String getAttachments() {
		return attachments;
	}

	/**
	 * @param attachments the attachments to set
	 */
	public void setAttachments(String attachments) {
		this.attachments = attachments;
	}

	/**
	 * @return the userType
	 */
	public UserType getUserType() {
		return userType;
	}

	/**
	 * @param userType the userType to set
	 */
	public void setUserType(UserType userType) {
		this.userType = userType;
	}

	@Override
	public String toString() {
		return "ThreadDetailsPayload [threadId=" + threadId + ", channelId=" + channelId + ", name=" + name
				+ ", description=" + description + ", createdBy=" + createdBy + ", attachments=" + attachments
				+ ", userType=" + userType + "]";
	}
}
