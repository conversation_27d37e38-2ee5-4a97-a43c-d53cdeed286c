package com.embrate.cloud.core.api.application.mobile;

/**
 * <AUTHOR>
 */
public class StandardAppVersion implements IMobileAppVersion {

    public static final String VERSION_DELIMITER ="\\.";

    private final String version;
    private final int majorVersion;
    private final int minorVersion;
    private final int revision;

    public StandardAppVersion(String version) {
        this.version = version;
        String [] tokens = version.split(VERSION_DELIMITER);
        this.majorVersion = Integer.parseInt(tokens[0]);
        this.minorVersion = Integer.parseInt(tokens[1]);
        this.revision = Integer.parseInt(tokens[2]);
    }


    public String getVersion() {
        return version;
    }

    public int getMajorVersion() {
        return majorVersion;
    }

    public int getMinorVersion() {
        return minorVersion;
    }

    public int getRevision() {
        return revision;
    }

    public static Integer compareVersion(StandardAppVersion v1, StandardAppVersion v2){
        if(v1 == null || v2 == null){
            return null;
        }

        if(v1.getMajorVersion() < v2.getMajorVersion()){
            return -1;
        }

        if(v1.getMajorVersion() > v2.getMajorVersion()){
            return 1;
        }

        if(v1.getMinorVersion() < v2.getMinorVersion()){
            return -1;
        }

        if(v1.getMinorVersion() > v2.getMinorVersion()){
            return 1;
        }

        if(v1.getRevision() < v2.getRevision()){
            return -1;
        }

        if(v1.getRevision() > v2.getRevision()){
            return 1;
        }

        return 0;
    }
}
