/**
 * 
 */
package com.embrate.cloud.core.api.whatsapp;

import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.NameFormat;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class WhatsappPreferences {

	public static final String WHATSAPP_SERVICE_ENABLED = "whatsapp_service_enabled"; //

	public static final String WHATSAPP_PROVIDER = "whatsapp_provider";

	public static final String BUFFER_WHATSAPP_COUNT = "buffer_whatsapp_count";

	public static final String WHATSAPP_API_KEY = "whatsapp_api_key";

	public static final String WHATSAPP_CHANNEL_ID = "whatsapp_channel_id";

	private boolean whatsappServiceEnabled;

	private CommunicationServiceProvider whatsappProvider;

	private Integer bufferWhatsappCount;

	private String whatsappAPIKey;

	private String whatsappChannelId;

	public WhatsappPreferences() {

	}

	public static String getConfigType() {
		return "whatsapp_preferences";
	}

	public boolean isWhatsappServiceEnabled() {
		return whatsappServiceEnabled;
	}

	public void setWhatsappServiceEnabled(boolean whatsappServiceEnabled) {
		this.whatsappServiceEnabled = whatsappServiceEnabled;
	}

	public CommunicationServiceProvider getWhatsappProvider() {
		return whatsappProvider;
	}

	public void setWhatsappProvider(CommunicationServiceProvider whatsappProvider) {
		this.whatsappProvider = whatsappProvider;
	}

	public Integer getBufferWhatsappCount() {
		return bufferWhatsappCount;
	}

	public void setBufferWhatsappCount(Integer bufferWhatsappCount) {
		this.bufferWhatsappCount = bufferWhatsappCount;
	}

	public String getWhatsappAPIKey() {
		return whatsappAPIKey;
	}

	public void setWhatsappAPIKey(String whatsappAPIKey) {
		this.whatsappAPIKey = whatsappAPIKey;
	}

	public String getWhatsappChannelId() {
		return whatsappChannelId;
	}

	public void setWhatsappChannelId(String whatsappChannelId) {
		this.whatsappChannelId = whatsappChannelId;
	}

	@Override
	public String toString() {
		return "WhatsappPreferences{" +
				"whatsappServiceEnabled=" + whatsappServiceEnabled +
				", whatsappProvider=" + whatsappProvider +
				", bufferWhatsappCount=" + bufferWhatsappCount +
				", whatsappAPIKey='" + whatsappAPIKey + '\'' +
				", whatsappChannelId='" + whatsappChannelId + '\'' +
				'}';
	}
}