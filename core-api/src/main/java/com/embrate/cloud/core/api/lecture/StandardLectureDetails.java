/**
 * 
 */
package com.embrate.cloud.core.api.lecture;

import java.util.List;

import com.lernen.cloud.core.api.institute.Standard;

/**
 * <AUTHOR>
 *
 */
public class StandardLectureDetails {
	
	private int instituteId;
	
	private Standard standard;
	
	private List<StandardCoursesLectureDetails> standardCoursesLectureDetails;

	/**
	 * @param instituteId
	 * @param standard
	 * @param standardCoursesLectureDetails
	 */
	public StandardLectureDetails(int instituteId, Standard standard,
			List<StandardCoursesLectureDetails> standardCoursesLectureDetails) {
		this.instituteId = instituteId;
		this.standard = standard;
		this.standardCoursesLectureDetails = standardCoursesLectureDetails;
	}

	/**
	 * 
	 */
	public StandardLectureDetails() {
	}


	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the standard
	 */
	public Standard getStandard() {
		return standard;
	}

	/**
	 * @param standard the standard to set
	 */
	public void setStandard(Standard standard) {
		this.standard = standard;
	}

	/**
	 * @return the standardCoursesLectureDetails
	 */
	public List<StandardCoursesLectureDetails> getStandardCoursesLectureDetails() {
		return standardCoursesLectureDetails;
	}

	/**
	 * @param standardCoursesLectureDetails the standardCoursesLectureDetails to set
	 */
	public void setStandardCoursesLectureDetails(List<StandardCoursesLectureDetails> standardCoursesLectureDetails) {
		this.standardCoursesLectureDetails = standardCoursesLectureDetails;
	}

	@Override
	public String toString() {
		return "StandardLectureDetails [instituteId=" + instituteId + ", standard=" + standard
				+ ", standardCoursesLectureDetails=" + standardCoursesLectureDetails + "]";
	}
	
}
