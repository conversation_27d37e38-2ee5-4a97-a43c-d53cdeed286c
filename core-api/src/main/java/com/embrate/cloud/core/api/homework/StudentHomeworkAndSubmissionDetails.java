/**
 * 
 */
package com.embrate.cloud.core.api.homework;

/**
 * <AUTHOR>
 *
 */
public class StudentHomeworkAndSubmissionDetails {
	
	private final HomeworkDetails homeworkDetails;
	
	private final StudentHomeworkState studentHomeworkState;
	
	private final HomeworkSubmissionDetails homeworkSubmissionDetails;

	/**
	 * @param homeworkDetails
	 * @param homeworkSubmissionDetails
	 */
	public StudentHomeworkAndSubmissionDetails(HomeworkDetails homeworkDetails,
			HomeworkSubmissionDetails homeworkSubmissionDetails) {
		this.homeworkDetails = homeworkDetails;
		this.homeworkSubmissionDetails = homeworkSubmissionDetails;
		this.studentHomeworkState = setStudentHomeworkState();
	}

	/**
	 * @return the homeworkDetails
	 */
	public HomeworkDetails getHomeworkDetails() {
		return homeworkDetails;
	}

	/**
	 * @return the homeworkSubmissionDetails
	 */
	public HomeworkSubmissionDetails getHomeworkSubmissionDetails() {
		return homeworkSubmissionDetails;
	}

	/**
	 * @return the studentHomeworkState
	 */
	public StudentHomeworkState getStudentHomeworkState() {
		return studentHomeworkState;
	}

	private StudentHomeworkState setStudentHomeworkState() {
		if(this.homeworkSubmissionDetails == null) {
			return StudentHomeworkState.PENDING;
		}
		if(homeworkSubmissionDetails.getHomeworkSubmissionStatus() == HomeworkSubmissionStatus.ACCEPTED
				|| homeworkSubmissionDetails.getHomeworkSubmissionStatus() == HomeworkSubmissionStatus.SUBMITTED) {
			return StudentHomeworkState.SUBMITTED;
		}
		return StudentHomeworkState.PENDING;
	}

	@Override
	public String toString() {
		return "StudentHomeworkAndSubmissionDetails [homeworkDetails=" + homeworkDetails + ", studentHomeworkState="
				+ studentHomeworkState + ", homeworkSubmissionDetails=" + homeworkSubmissionDetails + "]";
	}

}
