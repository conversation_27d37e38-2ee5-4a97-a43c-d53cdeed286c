package com.embrate.cloud.core.api.hostelmanagement;

import java.util.UUID;

public class HostelDetails {
    private int instituteId;
    private UUID hostelId;
    private String hostelName;
    private String address;
    private String primaryContactNo;
    private String secondaryContactNo;
    private String emailId;

    public HostelDetails() {}

    public HostelDetails(int instituteId, UUID hostelId, String hostelName,
                         String address, String primaryContactNo, String secondaryContactNo, String emailId) {
        this.instituteId = instituteId;
        this.hostelId = hostelId;
        this.hostelName = hostelName;
        this.address = address;
        this.primaryContactNo = primaryContactNo;
        this.secondaryContactNo = secondaryContactNo;
        this.emailId = emailId;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public UUID getHostelId() {
        return hostelId;
    }

    public void setHostelId(UUID hostelId) {
        this.hostelId = hostelId;
    }

    public String getHostelName() {
        return hostelName;
    }

    public void setHostelName(String hostelName) {
        this.hostelName = hostelName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPrimaryContactNo() {
        return primaryContactNo;
    }

    public void setPrimaryContactNo(String primaryContactNo) {
        this.primaryContactNo = primaryContactNo;
    }

    public String getSecondaryContactNo() {
        return secondaryContactNo;
    }

    public void setSecondaryContactNo(String secondaryContactNo) {
        this.secondaryContactNo = secondaryContactNo;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    @Override
    public String toString() {
        return "HostelDetails{" +
                "instituteId=" + instituteId +
                ", hostelId='" + hostelId + '\'' +
                ", hostelName='" + hostelName + '\'' +
                ", address='" + address + '\'' +
                ", primaryContactNo='" + primaryContactNo + '\'' +
                ", secondaryContactNo='" + secondaryContactNo + '\'' +
                ", emailId='" + emailId + '\'' +
                '}';
    }
}
