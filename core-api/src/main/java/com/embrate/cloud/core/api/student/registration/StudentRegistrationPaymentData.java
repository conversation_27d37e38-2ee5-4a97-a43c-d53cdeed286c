package com.embrate.cloud.core.api.student.registration;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.fees.FeeIdFeeHeadDetails;
import com.lernen.cloud.core.api.fees.ResolvedDefaultEntityFeeAssignmentStructure;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentRegistrationPaymentData {

	private double assignedAmount;
	private double paidAmount;
	private double discountAmount;
	private TransactionMode transactionMode;
	private Integer transactionDate;
	private String transactionReference;
	private Long transactionAddedAt;
	private UUID transactionAddedBy;

	public StudentRegistrationPaymentData() {
	}

	public StudentRegistrationPaymentData(double assignedAmount, double paidAmount, double discountAmount, TransactionMode transactionMode, Integer transactionDate, String transactionReference, Long transactionAddedAt, UUID transactionAddedBy) {
		this.assignedAmount = assignedAmount;
		this.paidAmount = paidAmount;
		this.discountAmount = discountAmount;
		this.transactionMode = transactionMode;
		this.transactionDate = transactionDate;
		this.transactionReference = transactionReference;
		this.transactionAddedAt = transactionAddedAt;
		this.transactionAddedBy = transactionAddedBy;
	}

	public double getAssignedAmount() {
		return assignedAmount;
	}

	public void setAssignedAmount(double assignedAmount) {
		this.assignedAmount = assignedAmount;
	}

	public double getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(double paidAmount) {
		this.paidAmount = paidAmount;
	}

	public double getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(double discountAmount) {
		this.discountAmount = discountAmount;
	}

	public TransactionMode getTransactionMode() {
		return transactionMode;
	}

	public String getTransactionModeDisplayName() {
		return transactionMode == null ? null : transactionMode.getDisplayName();
	}

	public void setTransactionMode(TransactionMode transactionMode) {
		this.transactionMode = transactionMode;
	}

	public Integer getTransactionDate() {
		return transactionDate;
	}

	public void setTransactionDate(int transactionDate) {
		this.transactionDate = transactionDate;
	}

	public String getTransactionReference() {
		return transactionReference;
	}

	public void setTransactionReference(String transactionReference) {
		this.transactionReference = transactionReference;
	}

	public Long getTransactionAddedAt() {
		return transactionAddedAt;
	}

	public void setTransactionAddedAt(long transactionAddedAt) {
		this.transactionAddedAt = transactionAddedAt;
	}

	public UUID getTransactionAddedBy() {
		return transactionAddedBy;
	}

	public void setTransactionAddedBy(UUID transactionAddedBy) {
		this.transactionAddedBy = transactionAddedBy;
	}

	public void setTransactionDate(Integer transactionDate) {
		this.transactionDate = transactionDate;
	}

	public void setTransactionAddedAt(Long transactionAddedAt) {
		this.transactionAddedAt = transactionAddedAt;
	}

	@Override
	public String toString() {
		return "StudentRegistrationPaymentData{" +
				"assignedAmount=" + assignedAmount +
				", paidAmount=" + paidAmount +
				", discountAmount=" + discountAmount +
				", transactionMode=" + transactionMode +
				", transactionDate=" + transactionDate +
				", transactionReference='" + transactionReference + '\'' +
				", transactionAddedAt=" + transactionAddedAt +
				", transactionAddedBy=" + transactionAddedBy +
				'}';
	}
}
