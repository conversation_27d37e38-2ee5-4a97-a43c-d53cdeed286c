package com.embrate.cloud.core.api.onboarding.institute.setup;

/**
 * <AUTHOR>
 */

public class UserCredentials {

    private String userName;

    private String password;

    public UserCredentials() {
    }

    public UserCredentials(String userName, String password) {
        this.userName = userName;
        this.password = password;
    }

    public String getUserName() {
        return userName;
    }

    public String getPassword() {
        return password;
    }

    @Override
    public String toString() {
        return "UserCredentials{" +
                "userName='" + userName + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
