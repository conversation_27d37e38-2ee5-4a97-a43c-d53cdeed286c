/**
 * 
 */
package com.embrate.cloud.core.api.homework;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class StudentHomeworkStats {
	
private int instituteId;
	
	private Map<StudentHomeworkState, Integer> studentHomeworkStateMap;
	
	private List<CourseWiseStudentHomeworkStats> courseWiseStudentHomeworkStatsList;

	/**
	 * @param instituteId
	 * @param studentHomeworkStateMap
	 * @param courseWiseStudentHomeworkStatsList
	 */
	public StudentHomeworkStats(int instituteId, Map<StudentHomeworkState, Integer> studentHomeworkStateMap,
			List<CourseWiseStudentHomeworkStats> courseWiseStudentHomeworkStatsList) {
		this.instituteId = instituteId;
		this.studentHomeworkStateMap = studentHomeworkStateMap;
		this.courseWiseStudentHomeworkStatsList = courseWiseStudentHomeworkStatsList;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the studentHomeworkStateMap
	 */
	public Map<StudentHomeworkState, Integer> getStudentHomeworkStateMap() {
		return studentHomeworkStateMap;
	}

	/**
	 * @param studentHomeworkStateMap the studentHomeworkStateMap to set
	 */
	public void setStudentHomeworkStateMap(Map<StudentHomeworkState, Integer> studentHomeworkStateMap) {
		this.studentHomeworkStateMap = studentHomeworkStateMap;
	}

	/**
	 * @return the courseWiseStudentHomeworkStatsList
	 */
	public List<CourseWiseStudentHomeworkStats> getCourseWiseStudentHomeworkStatsList() {
		return courseWiseStudentHomeworkStatsList;
	}

	/**
	 * @param courseWiseStudentHomeworkStatsList the courseWiseStudentHomeworkStatsList to set
	 */
	public void setCourseWiseStudentHomeworkStatsList(
			List<CourseWiseStudentHomeworkStats> courseWiseStudentHomeworkStatsList) {
		this.courseWiseStudentHomeworkStatsList = courseWiseStudentHomeworkStatsList;
	}

	@Override
	public String toString() {
		return "StudentHomeworkStats [instituteId=" + instituteId + ", studentHomeworkStateMap="
				+ studentHomeworkStateMap + ", courseWiseStudentHomeworkStatsList=" + courseWiseStudentHomeworkStatsList
				+ "]";
	}

}
