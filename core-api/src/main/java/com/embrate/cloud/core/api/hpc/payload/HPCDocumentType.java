/**
 * 
 */
package com.embrate.cloud.core.api.hpc.payload;

import com.lernen.cloud.core.api.common.DocumentType;

/**
 * <AUTHOR>
 *
 */
public enum HPCDocumentType implements DocumentType{

	MYSELF("My Photo", "Homework Attachments"),
	MY_FAMILY("My Family Image", "Homework Submission Attachments"),
	LEARNERS_PORTFOLIO("Learners Portfolio", "Learners Portfolio"),
	PRINCIPAL_SIGNATURE("Principal Signature", "Principal Signature"),
	CLASS_TEACHER_SIGNATURE("Class Teacher Signature", "Class Teacher Signature"),
	PARENTS_SIGNATURE("Parents Signature", "Parents Signature");

	private String documentName;
	private String displayName;
	private boolean isThumbnail;

	private HPCDocumentType(String documentName, String displayName, boolean isThumbnail) {
		this.documentName = documentName;
		this.displayName = displayName;
		this.isThumbnail = isThumbnail;
	}

	private HPCDocumentType(String documentName, String displayName) {
		this.documentName = documentName;
		this.displayName = displayName;
		this.isThumbnail = false;
	}
	
	@Override
	public String getDocumentName() {
		return documentName;
	}
	
	@Override
	public String getDisplayName() {
		return displayName;
	}

	@Override
	public boolean isThumbnail() { return isThumbnail; }

}
