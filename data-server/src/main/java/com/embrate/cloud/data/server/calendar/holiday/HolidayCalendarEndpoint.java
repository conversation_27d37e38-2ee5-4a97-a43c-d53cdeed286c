package com.embrate.cloud.data.server.calendar.holiday;

import com.embrate.cloud.core.api.calendar.holiday.Holiday;
import com.embrate.cloud.core.api.calendar.holiday.HolidayPayload;
import com.embrate.cloud.core.api.calendar.holiday.StaticHoliday;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplate;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplateDetails;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplatePayload;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplateType;
import com.embrate.cloud.core.api.calendar.holiday.template.assignment.UserHolidayTemplateAssignPayload;
import com.embrate.cloud.core.api.calendar.holiday.template.assignment.UserHolidayTemplateDetails;
import com.embrate.cloud.core.lib.calendar.holiday.HolidayCalendarManager;
import com.lernen.cloud.core.api.user.UserType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

@Path("/2.0/calendar/holiday")
public class HolidayCalendarEndpoint {
    private static final Logger logger = LogManager.getLogger(HolidayCalendarEndpoint.class);

    private final HolidayCalendarManager holidayCalendarManager;

    public HolidayCalendarEndpoint(HolidayCalendarManager holidayCalendarManager) {
        this.holidayCalendarManager = holidayCalendarManager;
    }

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Response getHolidays(@QueryParam("institute_id") int instituteId, @QueryParam("start") int start, @QueryParam("end") int end) {
        List<Holiday> holidays = holidayCalendarManager.getHolidays(instituteId, start, end);
        if (holidays == null) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(holidays).build();
    }

    @GET
    @Path("session")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getHolidays(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
        List<Holiday> holidays = holidayCalendarManager.getSessionHolidays(instituteId, academicSessionId);
        if (holidays == null) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(holidays).build();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addHoliday(@QueryParam("institute_id") int instituteId, HolidayPayload holidayPayload) {
        boolean success = holidayCalendarManager.addHoliday(instituteId, holidayPayload);
        if (success) {
            return Response.status(Response.Status.OK.getStatusCode()).build();
        }
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();

    }

    @POST
    @Path("global")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addHoliday(HolidayPayload holidayPayload) {
        boolean success = holidayCalendarManager.addGlobalHoliday(holidayPayload);
        if (success) {
            return Response.status(Response.Status.OK.getStatusCode()).build();
        }
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
    }

    @GET
    @Path("global")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getGlobalHolidays(@QueryParam("start") int start, @QueryParam("end") int end) {
        List<Holiday> holidays = holidayCalendarManager.getGlobalHolidays(start, end);
        if (holidays == null) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(holidays).build();
    }

    @PUT
    @Path("{holiday_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateHoliday(@QueryParam("institute_id") int instituteId, @PathParam("holiday_id") int holidayId, HolidayPayload holidayPayload) {
        boolean success = holidayCalendarManager.updateHoliday(instituteId, holidayId, holidayPayload);
        if (success) {
            return Response.status(Response.Status.OK.getStatusCode()).build();
        }
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
    }

    @PUT
    @Path("global/{holiday_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateGlobalHoliday(@PathParam("holiday_id") int holidayId, HolidayPayload holidayPayload) {
        boolean success = holidayCalendarManager.updateGlobalHoliday(holidayId, holidayPayload);
        if (success) {
            return Response.status(Response.Status.OK.getStatusCode()).build();
        }
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
    }


    @DELETE
    @Path("{holiday_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteHoliday(@QueryParam("institute_id") int instituteId, @PathParam("holiday_id") int holidayId) {
        boolean success = holidayCalendarManager.deleteHoliday(instituteId, holidayId);
        if (success) {
            return Response.status(Response.Status.OK.getStatusCode()).build();
        }
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
    }

    @DELETE
    @Path("global/{holiday_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteGlobalHoliday(@PathParam("holiday_id") int holidayId) {
        boolean success = holidayCalendarManager.deleteGlobalHoliday(holidayId);
        if (success) {
            return Response.status(Response.Status.OK.getStatusCode()).build();
        }
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
    }

    @POST
    @Path("new-template")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addHolidayTemplate(@QueryParam("institute_id") int instituteId, HolidayTemplatePayload holidayTemplatePayload) {
        UUID templateId = holidayCalendarManager.addHolidayTemplate(instituteId, holidayTemplatePayload);
        if (templateId == null) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(Collections.singletonMap("templateId", templateId)).build();
    }


    @GET
    @Path("session-templates")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getHolidayTemplates(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
                                        @QueryParam("primary_template_type") HolidayTemplateType primaryTemplateType) {
        List<HolidayTemplate> holidayTemplates = holidayCalendarManager.getHolidayTemplates(instituteId, academicSessionId, primaryTemplateType);
        if (holidayTemplates == null) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(holidayTemplates).build();
    }

    @GET
    @Path("templates/{template_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getHolidayTemplateDetails(@QueryParam("institute_id") int instituteId, @PathParam("template_id") UUID templateId) {
        HolidayTemplateDetails holidayTemplateDetails = holidayCalendarManager.getHolidayTemplateDetails(instituteId, templateId);
        if (holidayTemplateDetails == null) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(holidayTemplateDetails).build();
    }

    @PUT
    @Path("templates/{template_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateHolidayTemplate(@QueryParam("institute_id") int instituteId, @PathParam("template_id") UUID templateId, HolidayTemplatePayload holidayTemplatePayload) {
        boolean success = holidayCalendarManager.updateHolidayTemplate(instituteId, templateId, holidayTemplatePayload);
        if (success) {
            return Response.status(Response.Status.OK.getStatusCode()).build();
        }
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
    }

    @DELETE
    @Path("templates/{template_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteHolidayTemplate(@QueryParam("institute_id") int instituteId, @PathParam("template_id") UUID templateId) {
        boolean success = holidayCalendarManager.deleteHolidayTemplate(instituteId, templateId);
        if (success) {
            return Response.status(Response.Status.OK.getStatusCode()).build();
        }
        return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
    }

    @GET
    @Path("template-assignment")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getHolidayTemplates(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
                                        @QueryParam("user_type") UserType userType,  @QueryParam("standard_ids") String standardIdsCsv,
                                        @QueryParam("template_not_assigned_users_only") boolean templateNotAssignedUsersOnly ) {

        List<UserHolidayTemplateDetails> userHolidayTemplateDetails = holidayCalendarManager.getUserTemplateAssignment(instituteId, academicSessionId, userType, standardIdsCsv, templateNotAssignedUsersOnly);
        if (userHolidayTemplateDetails == null) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(userHolidayTemplateDetails).build();
    }

    @POST
    @Path("user-template-assignment")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response assignStudentHolidayTemplate(@QueryParam("institute_id") int instituteId, UserHolidayTemplateAssignPayload userHolidayTemplateAssignPayload) {
        boolean success = holidayCalendarManager.assignHolidayTemplate(instituteId, userHolidayTemplateAssignPayload);
        if (!success) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("students/{student_id}/resolved")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getResolvedStudentHolidays(@PathParam("student_id") UUID studentId, @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {

        List<StaticHoliday> resolvedHolidays = holidayCalendarManager.getResolvedStudentHolidayInSession(instituteId, academicSessionId, studentId);
        if (resolvedHolidays == null) {
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(resolvedHolidays).build();
    }

//    @GET
//    @Path("users/{user_id}")
//    @Produces(MediaType.APPLICATION_JSON)
//    public Response getUserHolidayAssignments(@QueryParam("institute_id") int instituteId, @PathParam("user_id") UUID userId,
//                                              @QueryParam("start") int start, @QueryParam("end") int end) {
//        List<Holiday> holidays = holidayCalendarManager.getUserHolidayAssignments(instituteId, userId, start, end);
//        if (holidays == null) {
//            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
//        }
//        return Response.status(Response.Status.OK.getStatusCode()).entity(holidays).build();
//    }
}
