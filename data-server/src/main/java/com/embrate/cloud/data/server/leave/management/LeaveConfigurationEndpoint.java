package com.embrate.cloud.data.server.leave.management;

import com.embrate.cloud.core.api.leave.management.LeaveType;
import com.embrate.cloud.core.api.leave.management.policy.LeavePolicyMetadata;
import com.embrate.cloud.core.api.leave.management.policy.LeavePolicyTemplate;
import com.embrate.cloud.core.api.leave.management.policy.LeavePolicyTemplatePayload;
import com.embrate.cloud.core.lib.leave.management.LeaveConfigurationManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

@Path("2.0/leave-configuration")
public class LeaveConfigurationEndpoint {

    private final LeaveConfigurationManager leaveConfigurationManager;

    public LeaveConfigurationEndpoint(LeaveConfigurationManager leaveConfigurationManager) {
        this.leaveConfigurationManager = leaveConfigurationManager;
    }

    @GET
    @Path("leave-type")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getLeaveTypes(@QueryParam("institute_id") int instituteId) {

        List<LeaveType> leaveTypes = leaveConfigurationManager.getLeaveTypes(instituteId);
        if (leaveTypes == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid leave types"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(leaveTypes).build();
    }

    @POST
    @Path("leave-type")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response createLeaveType(@QueryParam("institute_id") int instituteId, LeaveType leaveType) {

        final boolean success = leaveConfigurationManager.createLeaveType(instituteId, leaveType, true);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to create leave type"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @PUT
    @Path("leave-type")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateLeaveType(@QueryParam("institute_id") int instituteId, LeaveType leaveType) {

        final boolean success = leaveConfigurationManager.updateLeaveType(instituteId, leaveType);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update leave type"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @DELETE
    @Path("leave-type/{leave_type_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteLeaveType(@PathParam("leave_type_id") int leaveTypeId, @QueryParam("institute_id") int instituteId) {

        final boolean success = leaveConfigurationManager.deleteLeaveType(instituteId, leaveTypeId);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete leave type"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    // Leave policy configuration

    @GET
    @Path("leave-policies")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getLeavePolicies(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {

        List<LeavePolicyMetadata> leavePolicyMetadataList = leaveConfigurationManager.getLeavePolicyTemplates(instituteId, academicSessionId);
        if (leavePolicyMetadataList == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(leavePolicyMetadataList).build();
    }

    @GET
    @Path("leave-policy/{template_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getLeavePolicy(@PathParam("template_id") UUID templateId, @QueryParam("institute_id") int instituteId) {

        LeavePolicyTemplate leavePolicyTemplate = leaveConfigurationManager.getLeavePolicyTemplate(instituteId, templateId);
        if (leavePolicyTemplate == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(leavePolicyTemplate).build();
    }

    @POST
    @Path("leave-policy")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addLeavePolicy(@QueryParam("institute_id") int instituteId, LeavePolicyTemplatePayload leavePolicyTemplatePayload) {

        final UUID templateId = leaveConfigurationManager.addLeavePolicyTemplate(instituteId, leavePolicyTemplatePayload);
        if (templateId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to create leave policy"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @PUT
    @Path("leave-policy/{template_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateLeavePolicy(@PathParam("template_id") UUID templateId, @QueryParam("institute_id") int instituteId, LeavePolicyTemplatePayload leavePolicyTemplatePayload) {

        final boolean success = leaveConfigurationManager.updateLeavePolicyTemplate(instituteId, templateId, leavePolicyTemplatePayload);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update leave policy"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @DELETE
    @Path("leave-policy/{template_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteLeavePolicy(@PathParam("template_id") UUID templateId, @QueryParam("institute_id") int instituteId) {

        final boolean success = leaveConfigurationManager.deleteLeavePolicyTemplate(instituteId, templateId);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete leave policy"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }
}
