package com.embrate.cloud.data.server.student.finance;

import com.embrate.cloud.core.api.student.finance.ServiceExpensePayload;
import com.embrate.cloud.core.api.student.finance.StudentExpenseService;
import com.embrate.cloud.core.lib.student.finance.StudentFinanceManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.UUID;

@Path("/2.0/student-finance")
public class StudentFinanceEndPoint {

	private final StudentFinanceManager studentFinanceManager;

	public StudentFinanceEndPoint(StudentFinanceManager studentFinanceManager) {
		this.studentFinanceManager = studentFinanceManager;
	}

	@POST
	@Path("transaction")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addServiceExpenseTransaction(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, ServiceExpensePayload serviceExpensePayload) {
		UUID transactionId = studentFinanceManager.addServiceExpenseTransaction(instituteId, userId, serviceExpensePayload);
		if (transactionId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Failed to add service expense transaction"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("expense-services")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentExpenseServices(@QueryParam("institute_id") int instituteId) {

		final List<StudentExpenseService> studentExpenseServicesList = studentFinanceManager.getStudentExpenseServices(instituteId);
			return Response.status(Response.Status.OK.getStatusCode()).entity(studentExpenseServicesList).build();
	}

}
