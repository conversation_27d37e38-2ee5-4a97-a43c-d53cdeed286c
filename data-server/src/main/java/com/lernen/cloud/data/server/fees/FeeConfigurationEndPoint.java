package com.lernen.cloud.data.server.fees;

import com.embrate.cloud.core.api.fee.BulkStudentFeeStructureAssignmentReadablePayload;
import com.embrate.cloud.core.api.fee.BulkStudentFeesDeletionPayload;
import com.embrate.cloud.core.api.fee.BulkStudentFeesDeletionReadablePayload;
import com.embrate.cloud.core.api.fee.configuration.FeeAssignmentAmountsPayload;
import com.embrate.cloud.pdf.fees.FeeChallaHandler;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.embrate.cloud.core.api.fee.BulkStudentFeeStructureAssignmentPayload;
import com.lernen.cloud.core.api.fees.*;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.embrate.cloud.pdf.studentfeeschart.StudentFeesChartHandler;

import org.apache.commons.lang3.StringUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import java.io.ByteArrayInputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
@Path("/2.0/fees")
public class FeeConfigurationEndPoint {

	private final FeeConfigurationManager feeConfigurationManager;
	private final StudentFeesChartHandler studentFeesChartHandler;
	private final FeeChallaHandler feeChallaHandler;


	public FeeConfigurationEndPoint(FeeConfigurationManager feeConfigurationManager, StudentFeesChartHandler studentFeesChartHandler, FeeChallaHandler feeChallaHandler) {
		this.feeConfigurationManager = feeConfigurationManager;
		this.studentFeesChartHandler = studentFeesChartHandler;
		this.feeChallaHandler = feeChallaHandler;
	}

	// Fee Category APIs
	@GET
	@Path("/fee-category/instituteId/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getCategoryList(@PathParam("institute_id") int instituteId) {
		final List<FeeCategory> feeCategoryList = feeConfigurationManager.getCategoryList(instituteId);
		if (feeCategoryList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CATEGORY, "Unable to fetch fee category"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feeCategoryList).build();
	}

	@POST
	@Path("/regular-fee-configuration/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addRegularFeeConfiguration(@PathParam("institute_id") int instituteId,
			@QueryParam("duration") int duration, @QueryParam("academic_session_id") int academicSessionId,
											   @QueryParam("fine_applicable") boolean fineApplicable,
											   @QueryParam("transfer_to_wallet") boolean transferToWallet,
											   @QueryParam("user_id") UUID userId) {
		final Boolean addedFeeConfiguration = feeConfigurationManager.addRegularFeeConfiguration(instituteId, duration,
				academicSessionId, fineApplicable, transferToWallet, userId);
		if (!addedFeeConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to add regular fee configuration"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("/special-fee-configuration")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addSpecialFeeConfiguration(SpecialFeeConfigurationPayload specialFeeConfigurationPayload,
			@QueryParam("user_id") UUID userId) {
		final Boolean addedFeeConfiguration = feeConfigurationManager
				.addSpecialFeeConfiguration(specialFeeConfigurationPayload, userId);
		if (!addedFeeConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to add special fee configuration"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("/one-time-fee-configuration")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addOneTimeFeeConfiguration(FeeConfigurationBasicInfo feeConfigurationBasicInfo,
			@QueryParam("user_id") UUID userId) {
		final Boolean addedOneTimeFeeConfiguration = feeConfigurationManager
				.addOneTimeFeeConfiguration(feeConfigurationBasicInfo, userId);
		if (!addedOneTimeFeeConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to add one time fee configuration"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@PUT
	@Path("/update-regular-one-time-fee-configuration")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateFeeConfiguration(FeeConfigurationUpdatePayload feeConfigurationUpdatePayload,
			@QueryParam("user_id") UUID userId) {
		final UUID feeConfigurationId = feeConfigurationManager.updateFeeConfiguration(feeConfigurationUpdatePayload,
				userId);
		if (feeConfigurationId == null || StringUtils.isBlank(feeConfigurationId.toString())) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to update fee configurations"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feeConfigurationId).build();
	}

	@PUT
	@Path("/update-special-fee-configuration")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateSpecialFeeConfiguration(
			SpecialFeeConfigurationUpdatePayload specialFeeConfigurationUpdatePayload,
			@QueryParam("user_id") UUID userId) {
		final UUID feeConfigurationId = feeConfigurationManager
				.updateSpecialFeeConfiguration(specialFeeConfigurationUpdatePayload, userId);
		if (feeConfigurationId == null || StringUtils.isBlank(feeConfigurationId.toString())) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to update fee configurations"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feeConfigurationId).build();
	}

	@DELETE
	@Path("/delete-regular-fee-configuration/instituteId/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteRegularFeeConfigurationByAcademicSession(@PathParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId) {
		final boolean deletedRegularFeeConfiguration = feeConfigurationManager
				.deleteRegularFeeConfigurationByAcademicSession(instituteId, academicSessionId, userId);
		if (!deletedRegularFeeConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to delete regular fee configurations"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("/delete-special-fee-configuration/instituteId/{institute_id}/feeId/{fee_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteSpecialFeeConfigurationByFeeId(@PathParam("institute_id") int instituteId,
			@PathParam("fee_id") UUID feeId, @QueryParam("user_id") UUID userId) {
		final boolean deletedRegularFeeConfiguration = feeConfigurationManager
				.deleteFeeConfigurationByFeeId(instituteId, feeId, FeeType.SPECIAL, userId);
		if (!deletedRegularFeeConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to delete Special fee configurations"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("/delete-one-time-fee-configuration/instituteId/{institute_id}/feeId/{fee_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteOneTimeFeeConfigurationByFeeId(@PathParam("institute_id") int instituteId,
			@PathParam("fee_id") UUID feeId, @QueryParam("user_id") UUID userId) {
		final boolean deletedRegularFeeConfiguration = feeConfigurationManager
				.deleteFeeConfigurationByFeeId(instituteId, feeId, FeeType.ONE_TIME, userId);
		if (!deletedRegularFeeConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to delete one time fee configurations"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("/fee-configuration/instituteId/{institute_id}/feeId/{fee_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getFeeConfiguration(@PathParam("institute_id") int instituteId, @PathParam("fee_id") UUID feeId) {

		final FeeConfigurationResponse feeConfigurationResponse = feeConfigurationManager
				.getFeeConfiguration(instituteId, feeId);
		if (feeConfigurationResponse == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to fetch fee configuration"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feeConfigurationResponse).build();
	}

	@GET
	@Path("/authorized-fee-assignment/instituteId/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAuthorizedFeeAssignment(@PathParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") int academicSessionId) {

		final List<AuthorizedFeeAssignment> authorizedFeeAssignments = feeConfigurationManager
				.getAuthorizedFeeAssignment(instituteId, academicSessionId);
		if (authorizedFeeAssignments == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to fetch authorized fee assignments"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(authorizedFeeAssignments).build();
	}

	@GET
	@Path("/fee-configuration/instituteId/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getFeeConfigurationByAcademicYear(@PathParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") int academicSessionId) {

		if (academicSessionId > 0) {
			final List<FeeConfigurationResponse> feeConfigurationfeeConfigurationList = feeConfigurationManager
					.getFeeConfigurationByAcademicYear(instituteId, academicSessionId);
			if (feeConfigurationfeeConfigurationList == null) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
						"Unable to fetch fee configuration list"));
			}
			return Response.status(Response.Status.OK.getStatusCode()).entity(feeConfigurationfeeConfigurationList)
					.build();
		}

		final Map<Integer, List<FeeConfigurationResponse>> feeConfigurationfeeConfigurationMap = feeConfigurationManager
				.getAllFeeConfiguration(instituteId);
		if (feeConfigurationfeeConfigurationMap == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_CONFIGURATION,
					"Unable to fetch fee configuration list"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feeConfigurationfeeConfigurationMap).build();

	}

	@GET
	@Path("fee-data/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getFeeIdFeeHeadData(@PathParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") int academicSessionId) {

		final FeeIdFeeHeadData feeIdFeeHeadData = feeConfigurationManager.getFeeIdFeeHeadData(instituteId,
				academicSessionId);

		return Response.status(Response.Status.OK.getStatusCode()).entity(feeIdFeeHeadData).build();
	}

	// Fee Head APIs

	@POST
	@Path("/fee-head-configuration")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addFeeHeadConfigurations(List<FeeHeadConfiguration> feeHeadConfigurationList,
			@QueryParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId) {
		final boolean savedFeeHeadConfiguration = feeConfigurationManager.addFeeHeadConfigurations(instituteId,
				feeHeadConfigurationList, userId);
		if (!savedFeeHeadConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION,
					"Unable to add fee head configuration."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(savedFeeHeadConfiguration).build();
	}

	@PUT
	@Path("/update-fee-head-configuration")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateFeeHeadConfiguration(FeeHeadConfiguration feeHeadConfiguration,
			@QueryParam("user_id") UUID userId) {
		final int updatedFeeHeadConfigurationId = feeConfigurationManager
				.updateFeeHeadConfiguration(feeHeadConfiguration, userId);
		if (updatedFeeHeadConfigurationId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION,
					"Unable to update fee head configuration."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(updatedFeeHeadConfigurationId).build();
	}

	@DELETE
	@Path("/delete-fee-head-configuration/{institute_id}/{fee_head_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteFeeHeadConfiguration(@PathParam("institute_id") int instituteId,
			@PathParam("fee_head_id") int feeHeadId, @QueryParam("user_id") UUID userId) {
		final boolean deletedFeeHeadConfiguration = feeConfigurationManager.deleteFeeHeadConfiguration(instituteId,
				feeHeadId, userId);
		if (!deletedFeeHeadConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION,
					"Unable to update fee head configuration."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("/fee-head-configuration/instituteId/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getFeeHeadConfiguration(@PathParam("institute_id") int instituteId) {

		final List<FeeHeadConfigurationResponse> feeHeadConfigurationResponseList = feeConfigurationManager
				.getFeeHeadConfiguration(instituteId);
		if (feeHeadConfigurationResponseList == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION,
					"Unable to fetch fee head configuration"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feeHeadConfigurationResponseList).build();
	}

	// Fee Assignment APIs

	@POST
	@Path("/fee-assignment")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response assignFees(FeeAssignmentDetails feeAssignmentDetails, @QueryParam("user_id") UUID userId) {
		final boolean savedFeeHeadConfiguration = feeConfigurationManager.assignFees(feeAssignmentDetails, Module.FEES,
				FeeAssignmentState.NEW, false, true, userId);
		if (!savedFeeHeadConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
					"Unable to add students fee configurations."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(savedFeeHeadConfiguration).build();
	}

	@PUT
	@Path("update-fee-assignment-amounts")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateAssignedFees(@QueryParam("institute_id") int instituteId,
									   @QueryParam("academic_session_id") Integer academicSessionId,
									   @QueryParam("user_id") UUID userId, FeeAssignmentAmountsPayload feeAssignmentAmountsPayload) {

		final boolean updatedAssignedFees = feeConfigurationManager.updateAssignedFeeAmounts(instituteId, academicSessionId, feeAssignmentAmountsPayload, userId);

		if (!updatedAssignedFees) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
					"Unable to update students fee configurations."));
		}

		return Response.status(Response.Status.OK.getStatusCode()).entity(updatedAssignedFees).build();
	}

	@PUT
	@Path("/update-fee-assignment")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateAssignedFees(FeeAssignmentDetails feeAssignmentDetails, @QueryParam("user_id") UUID userId) {
		final boolean updatedAssignedFees = feeConfigurationManager.assignFees(feeAssignmentDetails, Module.FEES,
				FeeAssignmentState.COMPLETE_UPDATE, false, true, userId);
		if (!updatedAssignedFees) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
					"Unable to update students fee configurations."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(updatedAssignedFees).build();
	}

	@DELETE
	@Path("/delete-fee-assignment/{fee_id}/{entity_name}/{entity_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteAssignedFees(@QueryParam("institute_id") int instituteId, @PathParam("fee_id") UUID feeId,
			@PathParam("entity_name") FeeEntity feeEntity, @PathParam("entity_id") String entityId,
			@QueryParam("user_id") UUID userId) {
		final boolean deletedAssignedFees = feeConfigurationManager.deleteAssignedFees(instituteId, feeId, feeEntity,
				entityId, Module.FEES, userId);
		if (!deletedAssignedFees) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
					"Unable to add students fee configurations."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(deletedAssignedFees).build();
	}

	@GET
	@Path("/fee-assignment/{entity}/{entity_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getFeeAssigment(@PathParam("entity") FeeEntity feeEntity, @PathParam("entity_id") String entityId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") Integer academicSessionId) {
		final EntityFeeAssignment entityFeeAssignment = feeConfigurationManager.getFeeAssignment(instituteId, feeEntity,
				entityId, academicSessionId, true);
		if (entityFeeAssignment == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT, "Unable to fetch fee assignment"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(entityFeeAssignment).build();
	}

	@POST
	@Path("/bulk-fee-assignment/status")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getBulkFeeAssignmentStatus(List<UUID> students, @QueryParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") Integer academicSessionId, @QueryParam("user_id") UUID userId) {
		final BulkAssignmentFeesStatus bulkAssignmentFeesStatus = feeConfigurationManager
				.getBulkFeeAssignmentStatus(instituteId, academicSessionId, students, userId);
		if (bulkAssignmentFeesStatus == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT,
					"Unable to get bulk fee assignment status."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(bulkAssignmentFeesStatus).build();
	}

	@POST
	@Path("/generate-fees-charts")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentFeesChart(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("student_per_page") int  studentPerPage, @QueryParam("due_amount") boolean  includeDueAmount, @QueryParam("collected_amount") boolean  includeCollectedAmount,
			@QueryParam("disounted_amount") boolean  includeDiscountedAmount, @QueryParam("due_amount_till_today") boolean  includeDueAmountTillToday, Set<UUID> studentIds) {
				
			final DocumentOutput documentOutput = studentFeesChartHandler.generateStudentFeesChart(instituteId,academicSessionId,
			studentIds, studentPerPage,includeCollectedAmount, includeDueAmount, includeDiscountedAmount, includeDueAmountTillToday);
			// Just write filename= no need for attachment/inline for displaying pdf
			if (documentOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
					.header("Content-Disposition", "filename=" + documentOutput.getName())
					.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}


	@GET
	@Path("/default-fee-assignment-structure/{standard_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getDefaultFeeAssignmentStructure(@PathParam("standard_id") UUID standardId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") Integer academicSessionId,
			@QueryParam("feeStructureType") FeeStructureType feeStructureType,
			@QueryParam("onlyPendingEnrollmentAllowedFees") boolean onlyPendingEnrollmentAllowedFees) {
		final List<ResolvedDefaultEntityFeeAssignmentStructure> resolvedDefaultEntityFeeAssignmentStructures = feeConfigurationManager
				.getDefaultFeeAssignmentStructure(instituteId, academicSessionId, standardId,
						feeStructureType == null ? null : Arrays.asList(feeStructureType),
						onlyPendingEnrollmentAllowedFees);
		if (resolvedDefaultEntityFeeAssignmentStructures == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
					"Unable to fetch fee assignment structure"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(resolvedDefaultEntityFeeAssignmentStructures)
				.build();
	}

	@GET
	@Path("enrollment/default-fee-assignment-structure/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getEnrollmentDefaultFeeAssignmentStructure(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId) {
		final List<ResolvedDefaultEntityFeeAssignmentStructure> resolvedDefaultEntityFeeAssignmentStructures = feeConfigurationManager
				.getEnrollmentDefaultFeeAssignmentStructure(instituteId, studentId);
		if (resolvedDefaultEntityFeeAssignmentStructures == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
					"Unable to fetch fee assignment structure"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(resolvedDefaultEntityFeeAssignmentStructures)
				.build();
	}

	@GET
	@Path("/default-fee-assignment-structure")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getDefaultFeeAssignmentStructure(@QueryParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") Integer academicSessionId) {
		final List<DefaultEntityFeeAssignmentStructure> defaultEntityFeeAssignmentStructures = feeConfigurationManager
				.getDefaultFeeAssignmentStructure(instituteId, academicSessionId);
		if (defaultEntityFeeAssignmentStructures == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
					"Unable to fetch default fee assignment structure"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(defaultEntityFeeAssignmentStructures).build();
	}

	@POST
	@Path("/default-fee-assignment-structure")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response createDefaultFeeAssignmentStructure(
			List<DefaultEntityFeeAssignmentStructurePayload> defaultEntityFeeAssignmentStructurePayloads,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") Integer academicSessionId,
			@QueryParam("user_id") UUID userId) {
		final boolean status = feeConfigurationManager.createDefaultFeeAssignmentStructure(instituteId,
				academicSessionId, defaultEntityFeeAssignmentStructurePayloads, userId, false);
		if (!status) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_ASSIGNMENT_STRUCTURE,
					"Unable to create default fee assignment structure."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("/default-fee-assignment-structure/{structure_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteDefaultFeeAssignmentStructure(@PathParam("structure_id") UUID structureId,
			@QueryParam("academic_session_id") int academicSessionId, @QueryParam("institute_id") int instituteId,
			@QueryParam("user_id") UUID userId) {
		final boolean deleteDefaultFeeAssignmentStructureConfiguration = feeConfigurationManager
				.deleteDefaultFeeAssignmentStructure(instituteId, academicSessionId, structureId, userId);
		if (!deleteDefaultFeeAssignmentStructureConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE__STRUCTURE_CONFIGURATION,
					"Unable to delete fee structure configurations"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@PUT
	@Path("/update-default-fee-assignment-structure/{structure_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateDefaultFeeAssignmentStructure(@PathParam("structure_id") UUID structureId,
			List<DefaultEntityFeeAssignmentStructurePayload> defaultEntityFeeAssignmentStructurePayloads,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") Integer academicSessionId,
			@QueryParam("user_id") UUID userId) {
		final boolean updateDefaultFeeAssignmentStructureConfiguration = feeConfigurationManager
				.updateDefaultFeeAssignmentStructure(instituteId, academicSessionId, structureId,
						defaultEntityFeeAssignmentStructurePayloads, userId, false);
		if (!updateDefaultFeeAssignmentStructureConfiguration) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE__STRUCTURE_CONFIGURATION,
					"Unable to update fee structure configurations"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
	
	@GET
	@Path("/fee-structure-meta-data/{standard_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getFeeStructureMetaDataForStandard(@PathParam("standard_id") UUID standardId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("fee_structure_type") String feeStructureTypeStr) {
		FeeStructureType feeStructureType = FeeStructureType.getFeeStructureType(feeStructureTypeStr);
		final List<FeeStructureMetaData> feeStructureMetaDataList = feeConfigurationManager
				.getFeeStructureMetaDataForStandard(instituteId, academicSessionId, standardId, feeStructureType);
		return Response.status(Response.Status.OK.getStatusCode()).entity(feeStructureMetaDataList).build();
	}
	
	@GET
	@Path("/student-fee-structure-change/{student_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response checkStudentFeeStructureChangeEligibility(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		
		final List<String> eligibleList = feeConfigurationManager.checkStudentFeeStructureChangeEligibility(instituteId, 
				academicSessionId, studentId);
		
		return Response.status(Response.Status.OK.getStatusCode()).entity(eligibleList).build();
	}
	
	@POST
	@Path("/fee-structure-change/{student_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response studentFeeStructureChange(@PathParam("student_id") UUID studentId, @QueryParam("institute_id") int instituteId,
			@QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId, 
			List<UUID> feeStructureIdsList) {
		final boolean feeStructureChanged = feeConfigurationManager.studentFeeStructureChange(instituteId, academicSessionId,
				studentId, feeStructureIdsList, userId);
		if (!feeStructureChanged) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to change fee structure students."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	/**
	 * this one is to assign fee structures to all the students in institute,
	 * mainly when fee structure is missed whiling ingesting students
	 * @param instituteId
	 * @param academicSessionId
	 * @param userId
	 * @param feeStructureIdsList
	 * @param update
	 * @return
	 */
	@POST
	@Path("/bulk-assign-fee-structure/{institute_id}/{academic_session_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response assignInstituteStudentFeeStructure(@PathParam("institute_id") int instituteId, @PathParam("academic_session_id") int academicSessionId,
											  @QueryParam("user_id") UUID userId, List<UUID> feeStructureIdsList, @QueryParam("update") boolean update) {
		final boolean feeStructureAssigned = feeConfigurationManager.assignInstituteStudentFeeStructure(instituteId, academicSessionId,
				feeStructureIdsList, userId, update);
		if (!feeStructureAssigned) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Error while assigning fee structure to institute students."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}


	/**
	 * @param instituteId
	 * @param academicSessionId
	 * @param userId
	 * @param bulkStudentFeeStructureAssignmentReadablePayload
	 * @return
	 */
	@POST
	@Path("/bulk-fee-structure-assignment/{institute_id}/{academic_session_id}/readable")
	@Produces(MediaType.APPLICATION_JSON)
	public Response bulkFeeStructureAssignmentByAdmissionNumber(@PathParam("institute_id") int instituteId,
					@PathParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId,
					BulkStudentFeeStructureAssignmentReadablePayload bulkStudentFeeStructureAssignmentReadablePayload) {
		final List<String> errorDetailsList = feeConfigurationManager.readableBulkFeeStructureAssignment(
				instituteId, academicSessionId, userId, bulkStudentFeeStructureAssignmentReadablePayload);
		return Response.status(Response.Status.OK.getStatusCode()).entity(errorDetailsList).build();
	}

	/**
	 * @param instituteId
	 * @param academicSessionId
	 * @param userId
	 * @param bulkStudentFeeStructureAssignmentPayload
	 * @return
	 */
	@POST
	@Path("/bulk-fee-structure-assignment/{institute_id}/{academic_session_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response bulkFeeStructureAssignment(@PathParam("institute_id") int instituteId,
						@PathParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId,
						BulkStudentFeeStructureAssignmentPayload bulkStudentFeeStructureAssignmentPayload) {
		final List<String> errorDetailsList = feeConfigurationManager.bulkFeeStructureAssignment(
				instituteId, academicSessionId, userId, bulkStudentFeeStructureAssignmentPayload);
		return Response.status(Response.Status.OK.getStatusCode()).entity(errorDetailsList).build();
	}


	/**
	 * this one is to delete assigned fees to selected students as added in bulkStudentFeesDeletionReadablePayload
	 * @param instituteId
	 * @param academicSessionId
	 * @param userId
	 * @param bulkStudentFeesDeletionReadablePayload
	 * @return
	 */
	@POST
	@Path("/bulk-fees-delete/{institute_id}/{academic_session_id}/readable")
	@Produces(MediaType.APPLICATION_JSON)
	public Response bulkFeeAssignmentDeletionReadable(@PathParam("institute_id") int instituteId, @PathParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId,
				BulkStudentFeesDeletionReadablePayload bulkStudentFeesDeletionReadablePayload) {
		final List<String> errorDetailsList = feeConfigurationManager.readableBulkFeeAssignmentDeletion(
				instituteId, academicSessionId, userId, bulkStudentFeesDeletionReadablePayload);
		return Response.status(Response.Status.OK.getStatusCode()).entity(errorDetailsList).build();
	}

	/**
	 * this one is to delete assigned fees to selected students as added in bulkStudentFeesDeletionPayload
	 * @param instituteId
	 * @param academicSessionId
	 * @param userId
	 * @param bulkStudentFeesDeletionPayload
	 * @return
	 */
	@POST
	@Path("/bulk-fees-delete/{institute_id}/{academic_session_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response bulkFeeAssignmentDeletion(@PathParam("institute_id") int instituteId,
											   @PathParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId,
											  BulkStudentFeesDeletionPayload bulkStudentFeesDeletionPayload) {
		final List<String> errorDetailsList = feeConfigurationManager.bulkFeeAssignmentDeletion(
				instituteId, academicSessionId, userId, bulkStudentFeesDeletionPayload);
		return Response.status(Response.Status.OK.getStatusCode()).entity(errorDetailsList).build();
	}

	@GET
	@Path("fee-challan/{student_id}/pdf")
	@Produces("application/pdf")
	public Response generateChallanDocument(@PathParam("student_id") UUID studentId, @QueryParam("institute_id") int instituteId,
											@QueryParam("academic_session_id") int academicSessionId,
											@QueryParam("user_id") UUID userId) {
		final DocumentOutput documentOutput = feeChallaHandler.generateChallanDocument(instituteId, academicSessionId, studentId, userId);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate fee challan pdf."));
		}

		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}
}
