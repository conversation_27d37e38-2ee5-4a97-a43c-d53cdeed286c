package com.lernen.cloud.data.server.application.mobile;

import com.embrate.cloud.core.api.application.mobile.*;
import com.embrate.cloud.core.api.helpandsupport.HelpAndSupport;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.lib.application.mobile.MobileApplicationManager;
import com.lernen.cloud.core.lib.application.mobile.MobileConfigurationManager;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */

@Path("/2.0/mobileapp")
public class MobileApplicationManagementEndpoint {

	private final MobileApplicationManager mobileApplicationManager;

	private final MobileConfigurationManager mobileConfigurationManager;

	public MobileApplicationManagementEndpoint(MobileApplicationManager mobileApplicationManager,
											   MobileConfigurationManager mobileConfigurationManager) {
		this.mobileApplicationManager = mobileApplicationManager;
		this.mobileConfigurationManager = mobileConfigurationManager;
	}


	/**
	 * This API is depricated and apps < AUTH_FLOW_UPGRADE_VERSION are only using this, so forcing them to update to new version
	 * @return
	 */
	@GET
	@Path("{app_platform}/{app_version}/metadata/{user_id}")
	@Produces(MediaType.APPLICATION_JSON)
	@Deprecated
	public Response getUserAppMetadata(@PathParam("app_platform") MobileAppPlatform appPlatform,
									   @PathParam("app_version") String appVersion, @PathParam("user_id") UUID userId, @QueryParam("institute_id") Integer instituteId,
									   @QueryParam("last_access_time") Integer  lastAccessTime,
									   @QueryParam("last_login_time") Integer lastLoginTime, @QueryParam("manufacturer") String manufacturer, @QueryParam("model") String  model,
									   @QueryParam("sdkVersion") int  sdkVersion,  @QueryParam("versionRelease") String  versionRelease) {
		final MobileAppUserMetadata mobileAppUserMetadata = new MobileAppUserMetadata(null);
		if (mobileAppUserMetadata == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_MOBILE_APP_DETAILS,
					"Could not get requested information"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(mobileAppUserMetadata).build();
	}
	
	@GET
	@Path("/help-and-support")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getHelpAndSupportDetails() {
		final List<HelpAndSupport> helpAndSupportDetailsList = mobileApplicationManager.getHelpAndSupportDetails();
		return Response.status(Response.Status.OK.getStatusCode()).entity(helpAndSupportDetailsList).build();
	}

	@POST
	@Path("{institute_id}/users/{restriction_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getUserDetails(@PathParam("institute_id") int instituteId,
								   @PathParam("restriction_type") RestrictionType restrictionType,
								   FilterCriteria filterCriteria) {
		final List<StudentUserDueFeesData> usersList = mobileConfigurationManager.getUserDetails(instituteId, restrictionType,
				filterCriteria);
		return Response.status(Response.Status.OK.getStatusCode()).entity(usersList).build();
	}

	@PUT
	@Path("user-module-permissions")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateUserModulePermissions(@QueryParam("institute_id") int instituteId,
										 @QueryParam("user_id") UUID userId,
										 UserModulePermissionPayload userModulePermissionPayload) {

		final boolean permissionUpdated = mobileConfigurationManager.updateUserModulePermissions(
				instituteId, userId, userModulePermissionPayload);
		if (!permissionUpdated) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Error while updating user permissions"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(true).build();

	}

	@POST
	@Path("student-session-restriction")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateStudentSessionRestriction(@QueryParam("institute_id") int instituteId,
										 @QueryParam("user_id") UUID userId,
										 Set<Integer> restrictedAcademicSessionIdSet) {

		final boolean permissionUpdated = mobileConfigurationManager.updateUserSessionRestriction(
				instituteId, userId, restrictedAcademicSessionIdSet);
		if (!permissionUpdated) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Error while updating student session restriction!"));
		}
		final MetaDataPreferences metaDataPreferences = mobileConfigurationManager.getMetaDataPreferences(instituteId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(metaDataPreferences).build();

	}
}
