package com.lernen.cloud.data.server.attendance.staff;

import com.embrate.cloud.core.api.attendance.AttendanceInputType;
import com.embrate.cloud.push.notifications.handler.StaffAttendancePushNotificationHandler;
import com.embrate.cloud.push.notifications.handler.StaffAttendancePushNotificationHandlerV2;
import com.embrate.cloud.push.notifications.handler.StaffAttendanceStatusPushNotificationHandler;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceDateData;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendancePayloadV2;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceRegisterData;
import com.lernen.cloud.core.api.attendance.staff.v3.StaffAttendancePayloadV3;
import com.lernen.cloud.core.api.attendance.staff.v3.StaffMarkAttendancePayload;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.lib.attendance.staff.StaffAttendanceManager;
import com.lernen.cloud.sms.handler.StaffAttendanceSMSHandler;
import com.lernen.cloud.sms.handler.StaffAttendanceSMSHandlerV2;
import com.lernen.cloud.sms.handler.StaffAttendanceStatusSMSHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.Set;
import java.util.UUID;


@Path("/2.1/staff-attendance")
public class StaffAttendanceEndPointV2 {
    private static final Logger logger = LogManager.getLogger(StaffAttendanceEndPointV2.class);

    private final StaffAttendanceManager staffAttendanceManager;

    private final StaffAttendancePushNotificationHandler staffAttendancePushNotificationHandler;

    private final StaffAttendanceSMSHandler staffAttendanceSMSHandler;

    private final StaffAttendanceStatusPushNotificationHandler staffAttendanceStatusPushNotificationHandler;

    private final StaffAttendancePushNotificationHandlerV2 staffAttendancePushNotificationHandlerV2;

    private final StaffAttendanceStatusSMSHandler staffAttendanceStatusSMSHandler;

    private final StaffAttendanceSMSHandlerV2 staffAttendanceSMSHandlerV2;

    public StaffAttendanceEndPointV2(StaffAttendanceManager staffAttendanceManager, StaffAttendancePushNotificationHandler staffAttendancePushNotificationHandler, StaffAttendanceSMSHandler staffAttendanceSMSHandler, StaffAttendanceStatusPushNotificationHandler staffAttendanceStatusPushNotificationHandler, StaffAttendanceStatusSMSHandler staffAttendanceStatusSMSHandler, StaffAttendancePushNotificationHandlerV2 staffAttendancePushNotificationHandlerV2, StaffAttendanceSMSHandlerV2 staffAttendanceSMSHandlerV2) {
        this.staffAttendanceManager = staffAttendanceManager;
        this.staffAttendancePushNotificationHandler = staffAttendancePushNotificationHandler;
        this.staffAttendanceSMSHandler = staffAttendanceSMSHandler;
        this.staffAttendanceStatusPushNotificationHandler = staffAttendanceStatusPushNotificationHandler;
        this.staffAttendanceStatusSMSHandler = staffAttendanceStatusSMSHandler;
        this.staffAttendancePushNotificationHandlerV2 = staffAttendancePushNotificationHandlerV2;
        this.staffAttendanceSMSHandlerV2 = staffAttendanceSMSHandlerV2;
    }

    @GET
    @Path("staff-attendance-details/{institute_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getStaffAttendanceDetailsV2(@PathParam("institute_id") int instituteId,
                                                @QueryParam("attendance_date") Integer attendanceDate) {
        final List<StaffAttendanceRegisterData> staffAttendanceDetailsList = staffAttendanceManager.getStaffAttendanceRegister(instituteId, attendanceDate);
        return Response.status(Response.Status.OK.getStatusCode()).entity(staffAttendanceDetailsList).build();
    }


    @POST
    @Path("save")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response saveStaffAttendanceDetailsV2(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                                 StaffAttendancePayloadV2 staffAttendancePayload) {
        final boolean success = staffAttendanceManager.saveAPIStaffAttendanceDetails(instituteId, userId, staffAttendancePayload);

        if(!success){
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }

        /**
         * currently supporting staff attendance from web
         * TODO: when mobile staff attendance flow is added need to update the flow here.
         */
        staffAttendancePushNotificationHandler.sendAttendanceUpdateNotificationsAsync(instituteId, AttendanceInputType.WEB, staffAttendancePayload);

        staffAttendanceSMSHandler.bulkSend(instituteId, staffAttendancePayload, userId);

        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @POST
    @Path("recompute-logs")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response recomputeStaffAttendanceDetails(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, @QueryParam("start") int startDate,
                                                    @QueryParam("end") int endDate) {
        final boolean success = staffAttendanceManager.recomputeAttendance(instituteId, userId, startDate, endDate);
        if(success){
            return Response.status(Response.Status.OK.getStatusCode()).build();
        }

        return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
    }

    @GET
    @Path("staff-date-attendance-details")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getStaffAttendanceDateDataList(@QueryParam("institute_id") int instituteId, @QueryParam("attendance_date") int date,
                                                    @QueryParam("staff_category_ids_str") String staffCategoryIdsStr,
                                                    @QueryParam("staff_status_str") String staffStatusStr) {

        Set<UUID> staffCategoryIdSet = StringHelper.convertStringToSetUUID(staffCategoryIdsStr);
        Set<StaffStatus> staffStatusSet = StaffStatus.getStaffStatusSet(staffStatusStr);
        final List<StaffAttendanceDateData> staffAttendanceDateDataList = staffAttendanceManager.getStaffAttendanceDateDataList(
                instituteId, date, staffCategoryIdSet, staffStatusSet);
        return Response.status(Response.Status.OK.getStatusCode()).entity(staffAttendanceDateDataList).build();
    }

    @POST
    @Path("save-attendance-details")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response saveStaffAttendanceDetailsV2(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                                 StaffAttendancePayloadV3 staffAttendancePayload) {
        final StaffAttendancePayloadV3 filteredStaffAttendancePayload = staffAttendanceManager.saveAPIStaffAttendanceDetailsV3(instituteId, userId, staffAttendancePayload);

        if(filteredStaffAttendancePayload == null){
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }

        /**
         * currently supporting staff attendance from web
         * TODO: when mobile staff attendance flow is added need to update the flow here.
         */
        staffAttendancePushNotificationHandlerV2.sendAttendanceUpdateNotificationsAsync(instituteId, AttendanceInputType.WEB, filteredStaffAttendancePayload);

        staffAttendanceSMSHandlerV2.bulkSend(instituteId, filteredStaffAttendancePayload, userId);

        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @POST
    @Path("save-attendance-status")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response saveStaffAttendanceStatus(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
                                              StaffMarkAttendancePayload staffMarkAttendancePayload) {
        final boolean success = staffAttendanceManager.saveAPIStaffAttendanceStatus(instituteId, userId, staffMarkAttendancePayload);

        if(!success){
            return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).build();
        }

        staffAttendanceStatusPushNotificationHandler.sendAttendanceUpdateNotificationsAsync(instituteId, AttendanceInputType.WEB, staffMarkAttendancePayload);

        staffAttendanceStatusSMSHandler.bulkSend(instituteId, staffMarkAttendancePayload, userId);

        return Response.status(Response.Status.OK.getStatusCode()).build();
    }
}
