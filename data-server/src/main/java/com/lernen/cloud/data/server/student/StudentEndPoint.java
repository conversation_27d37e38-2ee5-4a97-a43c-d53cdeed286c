package com.lernen.cloud.data.server.student;

import com.embrate.cloud.core.api.student.management.StudentManagementFieldDetails;
import com.embrate.cloud.core.api.student.management.StudentManagementInputFieldData;
import com.embrate.cloud.core.api.student.management.StudentManagementUpdateFieldPayload;
import com.embrate.cloud.emailer.handler.StudentAdmissionEmailHandler;
import com.embrate.cloud.lambda.utils.pdf.idcard.StudentIdentityCardLambdaHandler;
import com.embrate.cloud.pdf.admission.form.AdmissionFormHandler;
import com.embrate.cloud.pdf.boardregistrationform.BoardRegistrationFormHandler;
import com.embrate.cloud.pdf.bookreceipt.BookReceiptDocumentHandler;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.*;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.FeeStructureType;
import com.lernen.cloud.core.api.institute.AutoSuggestHousePayload;
import com.lernen.cloud.core.api.institute.InstituteHousesWithCount;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.student.lite.StudentSearchEntry;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.api.user.authentication.RegeneratePasswordUserData;
import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.data.server.RestFormDataHandler;
import com.lernen.cloud.pdf.certificates.birthday.BirthdayCertificateDocumentHandler;
import com.lernen.cloud.pdf.certificates.bonafide.BonafideCertificateDocumentHandler;
import com.lernen.cloud.pdf.certificates.character.CharacterCertificateDocumentHandler;
import com.lernen.cloud.pdf.certificates.dynamic.DynamicDocumentHandler;
import com.lernen.cloud.pdf.certificates.promotion.PromotionCertificateDocumentHandler;
import com.lernen.cloud.pdf.certificates.study.StudyCertificateDocumentHandler;
import com.lernen.cloud.pdf.certificates.tuition.TuitionFeesCertificateDocumentHandler;
import com.lernen.cloud.pdf.certificates.transfer.TransferCertificateHandler;
import com.lernen.cloud.pdf.identitycard.student.StudentIdentityCardHandler;
import com.lernen.cloud.sms.handler.RegenerateUserCredentialSMSHandler;
import com.lernen.cloud.sms.handler.StudentSMSHandler;
import com.sun.jersey.multipart.FormDataBodyPart;
import com.sun.jersey.multipart.FormDataParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */

@Path("/2.0/student")
public class StudentEndPoint {
	private static final Logger logger = LogManager.getLogger(StudentEndPoint.class);

	private final StudentAdmissionManager studentAdmissionManager;
	private final StudentManager studentManager;
	private final AdmissionFormHandler admissionFormHandler;
	private final BoardRegistrationFormHandler boardRegistrationFormHandler;
	private final StudentIdentityCardHandler studentIdentityCardHandler;
	private final TransferCertificateHandler transferCertificateHandler;
	private final StudyCertificateDocumentHandler studyCertificateDocumentHandler;
	private final BonafideCertificateDocumentHandler bonafideCertificateDocumentHandler;
	private final PromotionCertificateDocumentHandler promotionCertificateDocumentHandler;
	private final TuitionFeesCertificateDocumentHandler tuitionFeesCertificateDocumentHandler;
	private final RegenerateUserCredentialSMSHandler regenerateUserCredentialSMSHandler;
	private final StudentSMSHandler studentSMSHandler;
	private final CharacterCertificateDocumentHandler characterCertificateDocumentHandler;
	private final BirthdayCertificateDocumentHandler birthdayCertificateDocumentHandler;
	private final DynamicDocumentHandler dynamicDocumentHandler;
	private final StudentIdentityCardLambdaHandler studentIdentityCardLambdaHandler;
	private final StudentAdmissionEmailHandler studentAdmissionEmailHandler;
	private final BookReceiptDocumentHandler bookReceiptDocumentHandler;

	private static final Gson GSON = new Gson();

	public StudentEndPoint(StudentAdmissionManager studentAdmissionManager, StudentManager studentManager,
		    AdmissionFormHandler admissionFormHandler,
			StudentIdentityCardHandler studentIdentityCardHandler,
			StudyCertificateDocumentHandler studyCertificateDocumentHandler,
			TransferCertificateHandler transferCertificateHandler,
			RegenerateUserCredentialSMSHandler regenerateUserCredentialSMSHandler,
			PromotionCertificateDocumentHandler promotionCertificateDocumentHandler,
						   StudentSMSHandler studentSMSHandler,
						   CharacterCertificateDocumentHandler characterCertificateDocumentHandler,
						   DynamicDocumentHandler dynamicDocumentHandler,TuitionFeesCertificateDocumentHandler tuitionFeesCertificateDocumentHandler,
						   StudentIdentityCardLambdaHandler studentIdentityCardLambdaHandler, BoardRegistrationFormHandler boardRegistrationFormHandler,
						   BirthdayCertificateDocumentHandler birthdayCertificateDocumentHandler,
						   StudentAdmissionEmailHandler studentAdmissionEmailHandler,
						   BookReceiptDocumentHandler bookReceiptDocumentHandler,
						   BonafideCertificateDocumentHandler bonafideCertificateDocumentHandler) {
		this.studentAdmissionManager = studentAdmissionManager;
		this.studentManager = studentManager;
		this.admissionFormHandler = admissionFormHandler;
		this.studentIdentityCardHandler = studentIdentityCardHandler;
		this.studyCertificateDocumentHandler = studyCertificateDocumentHandler;
		this.transferCertificateHandler = transferCertificateHandler;
		this.regenerateUserCredentialSMSHandler = regenerateUserCredentialSMSHandler;
		this.promotionCertificateDocumentHandler = promotionCertificateDocumentHandler;
		this.studentSMSHandler = studentSMSHandler;
		this.characterCertificateDocumentHandler = characterCertificateDocumentHandler;
		this.dynamicDocumentHandler = dynamicDocumentHandler;
		this.tuitionFeesCertificateDocumentHandler = tuitionFeesCertificateDocumentHandler;
		this.studentIdentityCardLambdaHandler = studentIdentityCardLambdaHandler;
		this.boardRegistrationFormHandler = boardRegistrationFormHandler;
		this.birthdayCertificateDocumentHandler = birthdayCertificateDocumentHandler;
		this.studentAdmissionEmailHandler = studentAdmissionEmailHandler;
		this.bookReceiptDocumentHandler = bookReceiptDocumentHandler;
		this.bonafideCertificateDocumentHandler = bonafideCertificateDocumentHandler;
	}

	@POST
	@Path("/add-student")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addStudent(@FormDataParam("add_student_payload") String registerStudentPayloadJson,
			@FormDataParam("file") List<FormDataBodyPart> bodyParts, @FormDataParam("documentName") String documentName,
			@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {

		final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
		FileData studentImage = null;

		if (!CollectionUtils.isEmpty(files)) {
			studentImage = files.get(0);
		}

		final RegisterStudentPayload registerStudentPayload = GSON.fromJson(registerStudentPayloadJson,
				RegisterStudentPayload.class);
		documentName = getValidDocumentName(documentName, StudentDocumentType.STUDENT_PROFILE_IMAGE);
		final UUID studentId = studentAdmissionManager.registerStudent(registerStudentPayload, instituteId,
				documentName, studentImage, Arrays.asList(FeeStructureType.REGISTRATION), true, userId);
		if (studentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to add student"));
		}

		final Student student = studentManager.getStudentByAcademicSessionStudentId(
				registerStudentPayload.getStudentPayload().getInstituteId(),
				registerStudentPayload.getStudentPayload().getAdmissionAcademicSession(), studentId);

//		if(registerStudentPayload.isSendNotification()) {
			studentSMSHandler.sendRegistrationSMSAsync(instituteId, student, userId);
//		}

		return Response.status(Response.Status.OK.getStatusCode()).entity(student).build();

	}

	@GET
	@Path("{student_id}/academic-session/{academic_session_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentByAcademicSessionStudentId(@PathParam("student_id") UUID studentId,
			@PathParam("academic_session_id") int academicSessionId, @QueryParam("institute_id") int instituteId,
			@QueryParam("includeWalletAmount") boolean includeWalletAmount,
			@QueryParam("add_thumbnail") boolean addThumbnail) {
		final Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId,
				studentId, addThumbnail);
		if (includeWalletAmount) {
			final Double walletAmount = studentManager.getWalletAmount(student.getStudentId(), DBLockMode.NONE);
			student.setWalletAmount(walletAmount);
		}
		if (student == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to get student"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(student).build();
	}

	@GET
	@Path("{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentInLatestAcademicSessionStudentId(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId,
			@QueryParam("includeWalletAmount") boolean includeWalletAmount) {
		final Student student = studentManager.getStudentByLatestAcademicSession(studentId,
				Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING, StudentStatus.RELIEVED,
						StudentStatus.NSO, StudentStatus.DELETED));
		if (includeWalletAmount) {
			final Double walletAmount = studentManager.getWalletAmount(student.getStudentId(), DBLockMode.NONE);
			student.setWalletAmount(walletAmount);
		}
		if (student == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to get student"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(student).build();
	}

	@GET
	@Path("/search")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentByAcademicSessionRegistrationNumber(
			@QueryParam("admission_number") String admissionNumber, @QueryParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("includeWalletAmount") boolean includeWalletAmount) {
		final Student student = studentManager.getStudentByAcademicSessionAdmissionNumber(instituteId,
				academicSessionId, admissionNumber);
		if (student == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to get student"));
		}
		if (includeWalletAmount) {
			final Double walletAmount = studentManager.getWalletAmount(student.getStudentId(), DBLockMode.NONE);
			student.setWalletAmount(walletAmount);
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(student).build();
	}

	@POST
	@Path("/upload-document/{student_id}")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.APPLICATION_JSON)
	public Response uploadStudentDocument(@PathParam("student_id") UUID studentId,
			@FormDataParam("file") List<FormDataBodyPart> bodyParts,
			@FormDataParam("documentType") StudentDocumentType studentDocumentType,
			@FormDataParam("documentName") String documentName, @QueryParam("institute_id") int instituteId,
			@QueryParam("user_id") UUID userId) {

		final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
		if (CollectionUtils.isEmpty(files)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "No file attached for upload."));
		}
		documentName = getValidDocumentName(documentName, studentDocumentType);
		final List<Document<StudentDocumentType>> updatedDocuments = studentManager.uploadDocument(instituteId,
				studentId, studentDocumentType, documentName, files.get(0));
		if (updatedDocuments == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to upload file"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(updatedDocuments).build();
	}

	@GET
	@Path("/download-document/{student_id}/{document_id}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response downloadStudentDocument(@PathParam("student_id") UUID studentId,
			@PathParam("document_id") UUID documentId, @QueryParam("institute_id") int instituteId) {
		final DownloadDocumentWrapper<Document<StudentDocumentType>> documentWrapper = studentManager
				.downloadDocument(instituteId, studentId, documentId);
		if (documentWrapper == null || documentWrapper.getDocumentContent() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to download file"));
		}

		String documentName = getValidDocumentName(documentWrapper.getDocumentDetails().getDocumentName(),
				documentWrapper.getDocumentDetails().getDocumentType());
		return Response.status(Response.Status.OK.getStatusCode())
				.header("Content-Disposition",
						"attachment;filename=" + documentName + "."
								+ documentWrapper.getDocumentDetails().getFileExtension())
				.entity(new ByteArrayInputStream(documentWrapper.getDocumentContent().toByteArray())).build();
	}

	@POST
	@Path("/delete-document/{student_id}/{document_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteStudentDocument(@PathParam("student_id") UUID studentId,
			@PathParam("document_id") UUID documentId, @QueryParam("institute_id") int instituteId,
			@QueryParam("user_id") UUID userId) {
		final List<Document<StudentDocumentType>> updatedDocuments = studentManager.deleteDocument(instituteId,
				studentId, documentId);
		if (updatedDocuments == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to delete document"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(updatedDocuments).build();
	}

	@POST
	@Path("/update-student/{academic_session_id}")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateStudent(@FormDataParam("update_student_payload") String updateStudentPaylaodJson,
			@FormDataParam("file") List<FormDataBodyPart> bodyParts, @FormDataParam("documentName") String documentName,
			@QueryParam("institute_id") int instituteId, @PathParam("academic_session_id") int academicSessionId,
			@QueryParam("user_id") UUID userId) {
		final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
		FileData imageUpload = null;

		if (!CollectionUtils.isEmpty(files)) {
			imageUpload = files.get(0);
		}

		final StudentPayload updateStudentPaylaod = GSON.fromJson(updateStudentPaylaodJson, StudentPayload.class);
		documentName = getValidDocumentName(documentName, StudentDocumentType.STUDENT_PROFILE_IMAGE);
		final UUID studentId = studentManager.updateStudent(updateStudentPaylaod, academicSessionId, instituteId,
				documentName, imageUpload, userId);
		if (studentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to update student"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentId).build();
	}

	@GET
	@Path("session/{academic_session_id}/{institute_id}/lite")
	@Produces(MediaType.APPLICATION_JSON)
	public Response liteSearchStudentsInAcademicSesison(@PathParam("institute_id") int instituteId,
			@PathParam("academic_session_id") int academicSessionId, @QueryParam("search_text") String searchText,
			@QueryParam("status") String status, @QueryParam("offset") Integer offset,
			@QueryParam("limit") Integer limit, @QueryParam("isHosteller") Boolean isHosteller, @QueryParam("includeUserStatus") String includeUserStatus) {

		SearchResultWithPagination<StudentLite> studentsInAcademicSession = null;
		final List<StudentStatus> studentStatusList = new ArrayList<>();
		if (StringUtils.isBlank(status)) {
			studentStatusList.add(StudentStatus.ENROLLED);
		} else {
			final String[] studentStatusArray = status.split(",");
			for (final String studentStatus : studentStatusArray) {
				studentStatusList.add(StudentStatus.getStudentStatus(studentStatus));
			}
		}

		studentsInAcademicSession = studentManager.liteSearchStudentsInAcademicSesison(instituteId, searchText,
				academicSessionId, studentStatusList, offset, limit, includeUserStatus, isHosteller);

		if (studentsInAcademicSession == null || studentsInAcademicSession.getResult() == null
				|| studentsInAcademicSession.getPaginationInfo() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Could not get students"));
		}

		return Response.status(Response.Status.OK.getStatusCode()).entity(studentsInAcademicSession).build();
	}

	@GET
	@Path("session/{academic_session_id}/{institute_id}/lite-v2")
	@Produces(MediaType.APPLICATION_JSON)
	public Response liteV2SearchStudentsInAcademicSession(@PathParam("institute_id") int instituteId,
														@PathParam("academic_session_id") int academicSessionId, @QueryParam("search_text") String searchText,
														@QueryParam("status") String status) {

		final Set<StudentStatus> studentStatusSet = new HashSet<>();
		if (StringUtils.isBlank(status)) {
			studentStatusSet.add(StudentStatus.ENROLLED);
		} else {
			final String[] studentStatusArray = status.split(",");
			for (final String studentStatus : studentStatusArray) {
				StudentStatus statusEntry = StudentStatus.getStudentStatus(studentStatus);
				if(statusEntry != null){
					studentStatusSet.add(statusEntry);
				}
			}
		}

		List<StudentSearchEntry> searchStudents = studentManager.liteV2SearchStudentsInAcademicSession(instituteId, academicSessionId, searchText, studentStatusSet);

		if (searchStudents == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid request"));
		}

		return Response.status(Response.Status.OK.getStatusCode()).entity(searchStudents).build();
	}

	@GET
	@Path("session/{academic_session_id}/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response searchStudentsInAcademicSesison(@PathParam("institute_id") int instituteId,
			@PathParam("academic_session_id") int academicSessionId, @QueryParam("search_text") String searchText,
			@QueryParam("status") String status, @QueryParam("offset") Integer offset,
			@QueryParam("limit") Integer limit,
			@QueryParam("requiredStandards") String requiredStandards, @QueryParam("tagged_actions") String taggedActionsStr, @QueryParam("requiredHostels") String requiredHostels) {
		SearchResultWithPagination<Student> studentsInAcademicSession = null;
		final List<StudentStatus> studentStatusList = new ArrayList<>();
		if (StringUtils.isBlank(status)) {
			studentStatusList.add(StudentStatus.ENROLLED);
		} else {
			final String[] studentStatusArray = status.split(",");
			for (final String studentStatus : studentStatusArray) {
				studentStatusList.add(StudentStatus.getStudentStatus(studentStatus));
			}
		}

		Set<TaggedActions> taggedActionsSet = new HashSet<>();
		if(!StringUtils.isEmpty(taggedActionsStr)){
			 taggedActionsSet = TaggedActions.getTaggedActionsSet(taggedActionsStr);
		}

		Set<UUID> hostelIdSet = StringHelper.convertStringToSetUUID(requiredHostels);

		studentsInAcademicSession = studentManager.searchStudentsInAcademicSesison(instituteId, searchText,
				academicSessionId, studentStatusList, offset, limit, null, requiredStandards, false, taggedActionsSet, hostelIdSet);

		if (studentsInAcademicSession == null || studentsInAcademicSession.getResult() == null
				|| studentsInAcademicSession.getPaginationInfo() == null) {
			final PaginationInfo paginationInfo = new PaginationInfo(0, limit, offset);
			studentsInAcademicSession = new SearchResultWithPagination<>(paginationInfo, null);
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentsInAcademicSession).build();
	}

	@GET
	@Path("session/relieved-students/{academic_session_id}/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response searchRelievedStudentsInAcademicSession(@PathParam("institute_id") int instituteId,
					@PathParam("academic_session_id") int academicSessionId, @QueryParam("search_text") String searchText,
					@QueryParam("offset") Integer offset, @QueryParam("limit") Integer limit,
					@QueryParam("requiredStandards") String requiredStandards) {
		SearchResultWithPagination<Student> relievedStudentsInAcademicSession = studentManager
				.searchRelievedStudentsInAcademicSession(instituteId, searchText, academicSessionId, offset, limit,
						null, requiredStandards);
		if (relievedStudentsInAcademicSession == null || relievedStudentsInAcademicSession.getResult() == null
				|| relievedStudentsInAcademicSession.getPaginationInfo() == null) {
			final PaginationInfo paginationInfo = new PaginationInfo(0, limit, offset);
			relievedStudentsInAcademicSession = new SearchResultWithPagination<>(paginationInfo, null);
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(relievedStudentsInAcademicSession).build();
	}

	@GET
	@Path("institute-students/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response searchStudentsWithoutAcademicSesison(@PathParam("institute_id") int instituteId,
			@QueryParam("search_text") String searchText, @QueryParam("student_status") String studentStatus,
			@QueryParam("offset") Integer offset, @QueryParam("limit") Integer limit,
														 @QueryParam("requiredStandards") String requiredStandards) {
		final StudentStatus status = StringUtils.isBlank(studentStatus) ? null : StudentStatus.valueOf(studentStatus);
		final List<Student> studentsWithoutAcademicSession = studentManager
				.searchStudentsWithoutAcademicSesison(instituteId, searchText, status, requiredStandards);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentsWithoutAcademicSession).build();
	}

	@GET
	@Path("institute-students/{institute_id}/lite")
	@Produces(MediaType.APPLICATION_JSON)
	public Response searchLiteStudentsWithoutAcademicSesison(@PathParam("institute_id") int instituteId,
						 @QueryParam("search_text") String searchText, @QueryParam("student_status") String studentStatus) {
		final StudentStatus status = StringUtils.isBlank(studentStatus) ? null : StudentStatus.valueOf(studentStatus);
		final List<StudentLite> studentsWithoutAcademicSessionLite = studentManager
				.searchStudentsWithoutAcademicSesisonLite(instituteId, searchText, status);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentsWithoutAcademicSessionLite).build();
	}

	/**
	 * Returns the list of all students in a particular class with section filter in given academic
	 * session
	 *
	 * @param academicSessionId
	 * @param standardId
	 * @param instituteId
	 * @return
	 */
	@GET
	@Path("students-list/{standard_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassStudents(@PathParam("standard_id") UUID standardId, 
			@QueryParam("academic_session_id") int academicSessionId, @QueryParam("institute_id") int instituteId,
			@QueryParam("section_ids") String sectionIdsStr) {
		Set<Integer> sectionIds = new HashSet<>();
		if(!StringUtils.isBlank(sectionIdsStr)) {
			final String[] sectionTokens = sectionIdsStr.split(",");
			for (final String sectionId : sectionTokens) {
				sectionIds.add(Integer.parseInt(sectionId));
			}
		}
		if(CollectionUtils.isEmpty(sectionIds)) {
			sectionIds = null;
		}
		final List<Student> studentsInAcademicSession = studentManager.getClassStudents(instituteId, academicSessionId,
				standardId, sectionIds);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentsInAcademicSession).build();
	}
	
	/**
	 * Returns the list of all students in a particular class in given academic
	 * session
	 *
	 * @param academicSessionId
	 * @param standardId
	 * @param instituteId
	 * @return
	 */
	@GET
	@Path("class-students/{academic_session_id}/{standard_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassStudents(@PathParam("academic_session_id") int academicSessionId,
			@PathParam("standard_id") UUID standardId, @QueryParam("institute_id") int instituteId) {
		final List<Student> studentsInAcademicSession = studentManager.getClassStudents(instituteId, academicSessionId,
				standardId);
		if (studentsInAcademicSession == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Could not get students"));
		}

		return Response.status(Response.Status.OK.getStatusCode()).entity(studentsInAcademicSession).build();
	}

	/**
	 * Returns the list of all students in requested classes in given academic
	 * session
	 *
	 * @param academicSessionId
	 * @param instituteId
	 * @return
	 */
	@GET
	@Path("class-students/{academic_session_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassStudents(@PathParam("academic_session_id") int academicSessionId,
			@QueryParam("institute_id") int instituteId, @QueryParam("standard_ids") String standardIdsCSV,
									 @QueryParam("student_status_str") String studentStatusStr) {
		List<Student> classStudentsInAcademicSession = new ArrayList<>();

		Set<UUID> standardIds = StringHelper.convertStringToSetUUID(standardIdsCSV);
		Set<StudentStatus> studentStatusSet = StudentStatus.getStudentStatusSet(studentStatusStr);
		if(CollectionUtils.isEmpty(studentStatusSet)) {
			studentStatusSet = new HashSet<>();
			studentStatusSet.add(StudentStatus.ENROLLED);
		}
		classStudentsInAcademicSession = studentManager.getStudentsByStandardIds(instituteId, academicSessionId, standardIds, studentStatusSet);

		return Response.status(Response.Status.OK.getStatusCode()).entity(classStudentsInAcademicSession).build();
	}

	@GET
	@Path("institute-students/{institute_id}/{academic_session_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getInstituteStudents(@PathParam("institute_id") int instituteId,
			@PathParam("academic_session_id") int academicSessionId) {
		final List<Student> studentsInAcademicSession = studentManager.getStudentsInAcademicSession(instituteId,
				academicSessionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentsInAcademicSession).build();
	}

	@GET
	@Path("/relieve-status/{student_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response studentRelieveStatus(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId) {
		final StudentRelieveStatus studentRelieveStatus = studentAdmissionManager.getStudentRelieveStatus(studentId,
				instituteId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentRelieveStatus).build();

	}

	@POST
	@Path("/relieve/{student_id}/{status_change_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response relieveStudent(@PathParam("student_id") UUID studentId,
			@PathParam("status_change_type") StudentStatus studentStatus, @QueryParam("institute_id") int instituteId,
			@QueryParam("user_id") UUID userId, StudentRelievePayload studentRelievePayload) {
		final boolean relieved = studentAdmissionManager.relieveStudent(instituteId, studentId, studentStatus,
				studentRelievePayload, userId);
		if (!relieved) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to relieve student."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("/enroll/{student_id}/{status_change_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response enrollStudent(@PathParam("student_id") UUID studentId,
								   @PathParam("status_change_type") StudentStatus studentStatus, @QueryParam("institute_id") int instituteId,
								   @QueryParam("user_id") UUID userId) {
		final boolean relieved = studentAdmissionManager.enrollStudent(instituteId, studentId, studentStatus, userId);
		if (!relieved) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to enroll student."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("/export-student-data")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response exportStudentData(@QueryParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") int academicSessionId) {

		final ReportOutput reportOutput = studentManager.exportStudentData(instituteId, academicSessionId);
		if (reportOutput == null) {
			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode())
				.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
				.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
	}

	@PUT
	@Path("/admit-student")
	@Produces(MediaType.APPLICATION_JSON)
	public Response admitStudent(@QueryParam("institute_id") int instituteId, EnrollStudentPayload enrollStudentPayload,
			@QueryParam("user_id") UUID userId) {
		final AdmitStudentResponse admittedStudent = studentAdmissionManager.admitStudent(instituteId,
				enrollStudentPayload, userId);
		if (admittedStudent == null || !admittedStudent.isSuccess()) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to admit student"));
		}

		RegeneratePasswordUserData<Student> regeneratePasswordUserData = null;
		if(admittedStudent.getStudentUserCreated() != null && admittedStudent.getStudentUserCreated()) {
			regeneratePasswordUserData = studentAdmissionManager.getResetPasswordUserDetails(instituteId, admittedStudent.getStudentId());
		}

		if(enrollStudentPayload.isSendNotification()) {
			final Student student = studentManager.getStudentByAcademicSessionStudentId(
					instituteId, admittedStudent.getAcademicSessionId(), admittedStudent.getStudentId());
			studentSMSHandler.sendAdmissionSMSAsync(instituteId, student, regeneratePasswordUserData, admittedStudent, userId);
		}

		/**
		 * only sending email when email is on from FE or user is created for student
		 */
		if(enrollStudentPayload.isSendEmail() &&
				admittedStudent.getStudentUserCreated() != null && admittedStudent.getStudentUserCreated()) {
			studentAdmissionEmailHandler.sendStudentAdmissionEmail(instituteId, regeneratePasswordUserData);
		}

		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("{student_id}/tc-details")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentTransferCertificateDetails(@PathParam("student_id") UUID studentId,
													@QueryParam("institute_id") int instituteId, @QueryParam("reset") boolean reset) {

		final StudentTransferCertificateDetails studentTransferCertificateDetails =
				studentAdmissionManager.getStudentTransferCertificateDetails(instituteId, studentId, reset);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentTransferCertificateDetails).build();
	}

	@POST
	@Path("{student_id}/pdf-tc")
	@Produces("application/pdf")
	public Response generateTransferCertificate(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId,
			StudentTransferCertificateDetails studentTransferCertificateDetails) {

		boolean studentTransferCertificateDetailsAdded = studentManager.updateStudentTransferCertificateDetails(instituteId, studentId, studentTransferCertificateDetails);

		if(!studentTransferCertificateDetailsAdded) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
					"Unable to add transfer certificate details."));
		}

		final DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(instituteId,
				studentId);

		if (documentOutput == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
					"Unable to generate transfer certificate."));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{student_id}/generate-pdf-tc")
	@Produces("application/pdf")
	public Response generatePdfTransferCertificate(@PathParam("student_id") UUID studentId,
												@QueryParam("institute_id") int instituteId) {
		final DocumentOutput documentOutput = transferCertificateHandler.generateTransferCertificate(instituteId,
				studentId);
		if (documentOutput == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
					"Unable to generate transfer certificate."));
		}
		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{student_id}/pdf-study-certificate")
	@Produces("application/pdf")
	public Response getStudyCertificatePDF(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("user_id") UUID userId) {

		final DocumentOutput documentOutput = studyCertificateDocumentHandler.generateStudyCertificate(instituteId,
				studentId, academicSessionId);

		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate study certificate"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{student_id}/pdf-bonafide-certificate")
	@Produces("application/pdf")
	public Response getbonafiedCertificatePDF(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("user_id") UUID userId) {

		final DocumentOutput documentOutput = bonafideCertificateDocumentHandler.generateBonafideCertificate(instituteId,
				studentId, academicSessionId);

		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate study certificate"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{student_id}/pdf-character-certificate")
	@Produces("application/pdf")
	public Response getCharacterCertificatePDF(@PathParam("student_id") UUID studentId,
											   @QueryParam("institute_id") int instituteId,
											   @QueryParam("academic_session_id") int academicSessionId,
										   @QueryParam("user_id") UUID userId) {

		final DocumentOutput documentOutput = characterCertificateDocumentHandler.generateCharacterCertificate(instituteId,
				studentId, academicSessionId);

		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate study certificate"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{student_id}/pdf-promotion-certificate")
	@Produces("application/pdf")
	public Response getPromotionCertificatePDF(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("user_id") UUID userId) {

		final DocumentOutput documentOutput = promotionCertificateDocumentHandler
				.generatePromotionCertificate(instituteId, studentId, academicSessionId);

		if (documentOutput == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
					"Unable to generate promotion certificate"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{student_id}/pdf-tuition-fees-certificate")
	@Produces("application/pdf")
	public Response getTuitionFeesCertificatePDF(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("user_id") UUID userId) {

		final DocumentOutput documentOutput = tuitionFeesCertificateDocumentHandler.generateTuitionFeesCertificate(instituteId,studentId, academicSessionId);

		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate Tuition Fees certificate"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{student_id}/pdf-books-receipt")
	@Produces("application/pdf")
	public Response getBookReceiptPDF(@PathParam("student_id") UUID studentId, @QueryParam("institute_id") int instituteId,
									  @QueryParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId) {

		final DocumentOutput documentOutput = bookReceiptDocumentHandler.generateBookReceipt(instituteId, studentId, academicSessionId);

		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate book receipt."));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("religion/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReligionDetails(@PathParam("institute_id") int instituteId) {
		final List<String> religionList = studentManager.getReligionDetails(instituteId);
		if (religionList.size() <= 0 || religionList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to get student"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(religionList).build();
	}

	@GET
	@Path("{student_id}/admission-form/pdf")
	@Produces("application/pdf")
	public Response getAdmissionFormPDF(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {

		final DocumentOutput documentOutput = admissionFormHandler.generateAdmissionForm(instituteId, studentId, academicSessionId);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate admission form"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("bulk-admission-form/pdf")
	@Produces("application/pdf")
	public Response getBulkAdmissionFormPDF(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
					@QueryParam("only_new_admission_students") boolean onlyNewAdmissionStudents) {

		FilterationCriteria filterationCriteria = new FilterationCriteria(null, null, null, null,
				null, null, null, null, onlyNewAdmissionStudents, null);
		final DocumentOutput documentOutput = admissionFormHandler.generateBulkAdmissionForm(instituteId, academicSessionId, filterationCriteria);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate admission form"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{student_id}/board-registration-form/pdf")
	@Produces("application/pdf")
	public Response getBoardRegistrationFormPDF(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId,@QueryParam("academic_session_id") int academicSessionId) {

		final DocumentOutput documentOutput = boardRegistrationFormHandler.generateBoardRegistrationForm(instituteId, academicSessionId, studentId);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate registration form"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{student_id}/birthday-certificate")
	@Produces("application/pdf")
	public Response getBirthdayCertificate(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId,@QueryParam("academic_session_id") int academicSessionId) {

		final DocumentOutput documentOutput = birthdayCertificateDocumentHandler.generateBirthdayCertificate(instituteId, academicSessionId, studentId);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate birthday certificate"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("static/admission-form/pdf")
	@Produces("application/pdf")
	public Response getStaticAdmissionFormPDF(@QueryParam("institute_id") int instituteId,
											  @QueryParam("academic_session_id") int academicSessionId) {

		final DocumentOutput documentOutput = admissionFormHandler.generateStaticAdmissionForm(instituteId, academicSessionId);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate admission form"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("{student_id}/identity-card/pdf")
	@Produces("application/pdf")
	public Response generateIdentityCard(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
										 @QueryParam("user_id") UUID userId) {
											
		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCard(instituteId,
				academicSessionId, studentId, userId);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate idenity cards"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("identity-cards/{standard_id}/pdf")
	@Produces("application/pdf")
	public Response generateIdentityCards(@PathParam("standard_id") UUID standardId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
										  @QueryParam("user_id") UUID userId, @QueryParam("section_ids") String sectionIdsStr) {

		Set<Integer> sectionIds = null;
		if(!StringUtils.isBlank(sectionIdsStr)) {
			sectionIds = new HashSet<>();
			final String[] sectionTokens = sectionIdsStr.split(",");
			for (final String sectionId : sectionTokens) {
				sectionIds.add(Integer.parseInt(sectionId));
			}
		}

		final DocumentOutput documentOutput = studentIdentityCardLambdaHandler.generateIdentityCards(instituteId,
				academicSessionId, standardId, sectionIds, userId);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate idenity cards"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@POST
	@Path("bulk-identity-cards/pdf")
	@Produces("application/pdf")
	public Response generateBulkIdentityCards(@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
										  @QueryParam("user_id") UUID userId, List<UUID> studentUUIDs) {
		if (CollectionUtils.isEmpty(studentUUIDs)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid student Ids"));
		}
		final DocumentOutput documentOutput = studentIdentityCardHandler.generateIdentityCards(instituteId,
				academicSessionId, studentUUIDs, userId);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate idenity cards"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	private List<UUID> getStudentUUIDsList(String studentIds) {
		if(StringUtils.isBlank(studentIds)) {
			return null;
		}
		List<UUID> studentUUIDs = new ArrayList<>();
		String [] studentUuids = studentIds.split(",");
		for(String str : studentUuids) {
			if(StringUtils.isBlank(str)) {
				continue;
			}
			studentUUIDs.add(UUID.fromString(str));
		}
		return CollectionUtils.isEmpty(studentUUIDs) ? null : studentUUIDs;
	}

	@GET
	@Path("{standard_id}/bulk-pdf-promotion-certificate")
	@Produces("application/pdf")
	public Response getStandardPromotionCertificatePDF(@PathParam("standard_id") UUID standardId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("user_id") UUID userId) {

		final DocumentOutput documentOutput = promotionCertificateDocumentHandler
				.getStandardPromotionCertificatePDF(instituteId, standardId, academicSessionId);

		if (documentOutput == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
					"Unable to generate promotion certificate"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}
	
	@POST
	@Path("/update-session-details")
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateStudentAcademicSessionDetails(@QueryParam("institute_id") int instituteId,
			@QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId, @QueryParam("from_script") boolean fromScript,
			StudentAcademicSessionPayload studentAcademicSessionPayload) {
		final boolean updated = studentManager.updateStudentAcademicSessionDetails(instituteId, academicSessionId,
				studentAcademicSessionPayload, userId, fromScript);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to update students bulk academic session details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
	
	@GET
	@Path("/promoting-students/{standard_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentsWithAllAcademicSessionDetailWithFilter(@PathParam("standard_id") UUID standardId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId, 
			@QueryParam("section_ids") String sectionIdsStr, @QueryParam("rte") String rteStr,
			@QueryParam("categorys") String categoryStr, @QueryParam("genders") String genderStr,
			@QueryParam("area_types") String areaTypeStr, @QueryParam("specially_abled") String speciallyAbledStr,
			@QueryParam("bpl") String bplStr) {
		
		Set<Integer> sectionIds = null;
		if(!StringUtils.isBlank(sectionIdsStr)) {
			sectionIds = new HashSet<>();
			final String[] sectionTokens = sectionIdsStr.split(",");
			for (final String sectionId : sectionTokens) {
				sectionIds.add(Integer.parseInt(sectionId));
			}
		}

		Boolean rte = null;
		if(!StringUtils.isBlank(rteStr)) {
			rte = Boolean.valueOf(rteStr);
		}
		
		Set<UserCategory> categorys = null;
		if(!StringUtils.isBlank(categoryStr)) {
			categorys = new HashSet<>();
			final String[] categoryTokens = categoryStr.split(",");
			for (final String category : categoryTokens) {
				categorys.add(UserCategory.getCategory(category));
			}
		}
		
		Set<Gender> genders = null;
		if(!StringUtils.isBlank(genderStr)) {
			genders = new HashSet<>();
			final String[] genderTokens = genderStr.split(",");
			for (final String gender : genderTokens) {
				genders.add(Gender.getGender(gender));
			}
		}
		
		Set<AreaType> areaTypes = null;
		if(!StringUtils.isBlank(areaTypeStr)) {
			areaTypes = new HashSet<>();
			final String[] areaTypeTokens = areaTypeStr.split(",");
			for (final String areaType : areaTypeTokens) {
				areaTypes.add(AreaType.getAreaType(areaType));
			}
		}

		Boolean speciallyAbled = null;
		if(!StringUtils.isBlank(speciallyAbledStr)) {
			speciallyAbled = Boolean.valueOf(speciallyAbledStr);
		}
		
		Boolean bpl = null;
		if(!StringUtils.isBlank(bplStr)) {
			bpl = Boolean.valueOf(bplStr);
		}
		
		final List<Student> studentList = studentManager.getStudentsWithAllAcademicSessionDetailWithFilter(
				instituteId, academicSessionId, standardId, sectionIds, rte, categorys, genders, areaTypes, speciallyAbled, bpl);
		
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentList).build();
	}
	
	@POST
	@Path("/promote-students")
	@Produces(MediaType.APPLICATION_JSON)
	public Response promoteStudents(@QueryParam("institute_id") int instituteId,
			@QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId, 
			StudentPromotionDetails studentPromotionDetails) {
		final boolean promoted = studentAdmissionManager.promoteStudents(instituteId, academicSessionId,
				studentPromotionDetails, userId);
		if (!promoted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to promote students."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("/carry-forward-transport")
	@Produces(MediaType.APPLICATION_JSON)
	public Response carryForwardTransport(@QueryParam("institute_id") int instituteId,
									@QueryParam("user_id") UUID userId,
									@QueryParam("next_academic_session_id") int nextAcademicSessionId,
									StudentPromotionDetails studentPromotionDetails) {
		final boolean transportAssigned = studentAdmissionManager.carryForwardTransport(instituteId,
				nextAcademicSessionId, userId, studentPromotionDetails);
		if (!transportAssigned) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to assign transport to students."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	/**
	 *
	 * @param instituteId
	 * @param userId
	 * @param nextAcademicSessionId
	 * @param studentPromotionDetails
	 * @return
	 *
	 * StudentPromotionDetails should have following values:
	 * currentSessionId, instituteId, isCarryForwardDiscount = true, standardId, PromotingStudentList,
	 * FeeStructureIdsList = null, isCarryForwardTransport = false
	 *
	 */
	@POST
	@Path("/carry-forward-discount-structure")
	@Produces(MediaType.APPLICATION_JSON)
	public Response carryForwardDiscountStructure(@QueryParam("institute_id") int instituteId,
									@QueryParam("user_id") UUID userId,
									@QueryParam("next_academic_session_id") int nextAcademicSessionId,
									StudentPromotionDetails studentPromotionDetails) {
		final boolean transportAssigned = studentAdmissionManager.carryForwardDiscountStructure(instituteId,
				nextAcademicSessionId, userId, studentPromotionDetails);
		if (!transportAssigned) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to assign transport to students."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
	
	@GET
	@Path("/student-standard-change/{student_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response checkStudentStandardChangeEligibility(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		
		final List<String> eligibleList = studentAdmissionManager.checkStudentStandardChangeEligibility(instituteId, 
				academicSessionId, studentId);
		
		return Response.status(Response.Status.OK.getStatusCode()).entity(eligibleList).build();
	}
	
	@POST
	@Path("/class-change")
	@Produces(MediaType.APPLICATION_JSON)
	public Response studentClassChange(@QueryParam("institute_id") int instituteId,
			@QueryParam("user_id") UUID userId, @QueryParam("academic_session_id") int academicSessionId, 
			StudentClassChangePayload studentClassChangePayload) {
		final boolean classChanged = studentAdmissionManager.studentClassChange(instituteId, academicSessionId,
				studentClassChangePayload, userId);
		if (!classChanged) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to promote students."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
	@GET
	@Path("enrollment/verfiy-details/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getVerificationForRestrictEnrollment(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final boolean restrictEnrollment = studentAdmissionManager
				.getVerificationForRestrictEnrollment(instituteId, academicSessionId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
	@GET
	@Path("enrollment/assign-details/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentEnrollmentAssignPayloadDetails(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final StudentEnrollmentAssignPayload studentEnrollmentAssignPayload = studentAdmissionManager
				.getStudentEnrollmentAssignPayloadDetails(instituteId, academicSessionId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentEnrollmentAssignPayload)
				.build();
	}

	public String getValidDocumentName(String documentName, StudentDocumentType studentDocumentType) {
		return StringUtils.isEmpty(documentName) ? studentDocumentType.getDisplayName().replace(" ", "_").toLowerCase() :
				documentName;
	}

	@POST
	@Path("siblings")
	@Produces(MediaType.APPLICATION_JSON)
	public Response addSiblings(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
									   StudentSiblingPayload studentSiblingPayload,
								@QueryParam("is_student_level_action") boolean isStudentLevelAction) {
		final boolean siblingsAdded = studentManager.addSiblings(instituteId, userId, studentSiblingPayload, isStudentLevelAction);
		if (!siblingsAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to add siblings details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("bulk-sibling-assignment")
	@Produces(MediaType.APPLICATION_JSON)
	public Response bulkSiblingAssignment(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
									 StudentSiblingListsPayload studentSiblingListsPayload,  @QueryParam("academic_session_id") int academicSessionId,
									 @QueryParam("skip_auth") boolean skipAuth) {
		final boolean siblingsAdded = studentManager.bulkSiblingAssignment(instituteId, userId, studentSiblingListsPayload, academicSessionId, skipAuth);
		if (!siblingsAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to add siblings details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	/**
	 * we have migrated, update sibling flow to add sibling and delete sibling
	 * as updating is causing issues while handling session,
	 * so for that case we have change the update flow by adding as flog,
	 * if the flag isDeleteFlow is true it will work as only delete flow
	 * ow it wil work as update flow.
	 * @param instituteId
	 * @param userId
	 * @param studentSiblingPayload
	 * @param isStudentLevelAction
	 * @return
	 */
	@PUT
	@Path("siblings-details")
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateSiblings(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
								StudentSiblingPayload studentSiblingPayload,
								   @QueryParam("is_student_level_action") boolean isStudentLevelAction) {
		final boolean siblingsUpdated = studentManager.updateSiblings(instituteId, userId, studentSiblingPayload,
				isStudentLevelAction);
		if (!siblingsUpdated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to add siblings details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@DELETE
	@Path("sibling-group/{sibling_group_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteSiblings(@PathParam("sibling_group_id") UUID siblingGroupId, @QueryParam("user_id") UUID userId,
								   @QueryParam("institute_id") int instituteId) {
		final boolean siblingsDeleted = studentManager.deleteSiblingsGroup(instituteId,
				userId, siblingGroupId);
		if (!siblingsDeleted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to add siblings details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("siblings/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentSiblingDetailList(@PathParam("institute_id") int instituteId,
												@QueryParam("search_text") String searchText) {
		final List<StudentSiblingDetails> studentSiblingDetailsList = studentManager
				.getStudentSiblingDetailsList(instituteId, searchText);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentSiblingDetailsList)
				.build();
	}

	@GET
	@Path("siblings-with-session/{institute_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentSiblingDetailWithSessionList(@PathParam("institute_id") int instituteId,
			@QueryParam("search_text") String searchText, @QueryParam("academic_session_id") int academicSessionId,
														   @QueryParam("status") String status) {
		final Set<StudentStatus> studentStatusList = StudentStatus.getStudentStatusSet(status);
		final List<StudentSiblingDetails> studentSiblingDetailsList = studentManager
				.getStudentSiblingDetailWithSessionList(instituteId, searchText, academicSessionId, studentStatusList);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentSiblingDetailsList)
				.build();
	}

	@GET
	@Path("sibling-details/{sibling_group_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentSiblingDetail(@PathParam("sibling_group_id") UUID siblingGroupId,
											@QueryParam("institute_id") int instituteId) {
		final StudentSiblingDetails studentSiblingDetails = studentManager
				.getStudentSiblingDetails(instituteId, siblingGroupId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentSiblingDetails)
				.build();
	}

	@GET
	@Path("sibling-details-with-session/{sibling_group_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentSiblingDetailWithSession(@PathParam("sibling_group_id") UUID siblingGroupId,
		@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final StudentSiblingDetails studentSiblingDetails = studentManager
				.getStudentSiblingDetailWithSession(instituteId, siblingGroupId, academicSessionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentSiblingDetails)
				.build();
	}

	@GET
	@Path("{student_id}/document/pdf/{document_name}")
	@Produces("application/pdf")
	public Response generateDynamicStudentDocument(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@PathParam("document_name") String documentName, @QueryParam("user_id") UUID userId) {

		final DocumentOutput documentOutput = dynamicDocumentHandler.generateDocument(instituteId,
				academicSessionId, studentId, userId, documentName);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate dynamic document cards"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("document/{standard_id}/pdf/{document_name}")
	@Produces("application/pdf")
	public Response generateDynamicStandardDocument(@PathParam("standard_id") UUID standardId,
		 	@PathParam("document_name") String documentName, @QueryParam("institute_id") int instituteId,
		 	@QueryParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId,
		 	@QueryParam("section_ids") String sectionIdsStr) {

		Set<Integer> sectionIds = null;
		if(!StringUtils.isBlank(sectionIdsStr)) {
			sectionIds = new HashSet<>();
			final String[] sectionTokens = sectionIdsStr.split(",");
			for (final String sectionId : sectionTokens) {
				sectionIds.add(Integer.parseInt(sectionId));
			}
		}

		final DocumentOutput documentOutput = dynamicDocumentHandler.generateDocuments(instituteId,
				academicSessionId, standardId, sectionIds, userId, documentName);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate dynamic document cards"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@POST
	@Path("bulk-document/pdf/{document_name}")
	@Produces("application/pdf")
	public Response generateBulkDynamicDocument(@QueryParam("institute_id") int instituteId,
			@PathParam("document_name") String documentName, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("user_id") UUID userId, List<UUID> studentUUIDs) {
		if (CollectionUtils.isEmpty(studentUUIDs)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid student Ids"));
		}
		final DocumentOutput documentOutput = dynamicDocumentHandler.generateDocuments(instituteId,
				academicSessionId, studentUUIDs, userId, documentName);
		if (documentOutput == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to generate dynamic document cards"));
		}

		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("student-admission-stats/{academic_session_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentAdmissionStats(@PathParam("academic_session_id") int academicSessionId,
											@QueryParam("institute_id") int instituteId) {
		final StudentAdmissionStats studentAdmissionStats = studentManager
				.getStudentAdmissionStats(instituteId, academicSessionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentAdmissionStats).build();
	}

	@POST
	@Path("/update-bulk-student-details")
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateBulkStudentDetails(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
							BulkStudentPayload bulkStudentPayload) {
		final boolean updated = studentManager.updateBulkStudentDetails(instituteId, userId, bulkStudentPayload);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to update students bulk basic details."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	/**
	 *
	 * @param instituteId
	 * @return
	 *
	 * To be used for one time migration of data from student level status to session level status
	 */
	@PUT
	@Path("/migrate-status-data")
	@Produces(MediaType.APPLICATION_JSON)
	public Response migrateStudentStatusData(@QueryParam("institute_id") int instituteId,
											 @QueryParam("student_id") UUID studentId,
											 @QueryParam("update") boolean update) {
		final List<StudentSessionStatusDetails> studentSessionStatusDetailsList = studentManager.migrateStudentStatusData(instituteId,
				studentId, update);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentSessionStatusDetailsList).build();
	}

	/**
	 * @return
	 *
	 * To be used for one time migration of data from student level status to session level status
	 */
	@PUT
	@Path("/migrate-all-institute-status-data")
	@Produces(MediaType.APPLICATION_JSON)
	public Response migrateAllInstituteStudentStatusData(@QueryParam("update") boolean update) {
		final TreeMap<Integer, List<StudentSessionStatusDetails>> allInstituteStudentStatusData = studentManager
				.migrateAllInstituteStudentStatusData(update);
		return Response.status(Response.Status.OK.getStatusCode()).entity(allInstituteStudentStatusData).build();
	}

	@GET
	@Path("student-sibling-details/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentSiblingDetailsByStudentId(@PathParam("student_id") UUID studentId, @QueryParam("institute_id") int instituteId) {
		final StudentSiblingDetails studentSiblingDetails = studentManager.getStudentSiblingDetailsByStudentId(instituteId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentSiblingDetails).build();
	}

	@GET
	@Path("student-sibling-details-with-session/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentSiblingDetailsWithSessionByStudentId(@PathParam("student_id") UUID studentId,
									   @QueryParam("institute_id") int instituteId,
									   @QueryParam("academic_session_id") int academicSessionId) {
		final StudentSiblingDetails studentSiblingDetails = studentManager.getStudentSiblingDetailsWithSessionByStudentId(instituteId, academicSessionId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentSiblingDetails).build();
	}

	@GET
	@Path("student-details-without-session/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentWithoutSession(@PathParam("student_id") UUID studentId) {
		final Student student = studentManager.getStudentWithoutSession(studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(student).build();
	}

	@POST
	@Path("auto-suggest-house")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response autoSuggestedHouse(@QueryParam("academic_session_id") int academicSessionId, @QueryParam("institute_id") int instituteId,
									   AutoSuggestHousePayload autoSuggestHousePayload) {
		final InstituteHousesWithCount instituteHousesWithCount = studentAdmissionManager.autoSuggestedHouse(
				instituteId, academicSessionId, autoSuggestHousePayload);
		return Response.status(Response.Status.OK.getStatusCode()).entity(instituteHousesWithCount).build();
	}

	@POST
	@Path("assign-institute-student-house")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response assignInstituteStudentHouse(@QueryParam("academic_session_id") int academicSessionId,
												@QueryParam("institute_id") int instituteId) {
		final boolean instituteStudentHouseAssigned = studentAdmissionManager.assignInstituteStudentHouse(instituteId,
				academicSessionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(instituteStudentHouseAssigned).build();
	}

	@POST
	@Path("biometric-ids")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response assignInstituteStudentHouse(@QueryParam("institute_id") int instituteId, Map<String, String> admissionNumberIdMap ) {
		final boolean success = studentManager.updateDeviceUserId(instituteId, admissionNumberIdMap);
		if(!success){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update students device user ids."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("student-dossier-details/{student_id}/{academic_session_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentDossierDetails(@PathParam("student_id") UUID studentId,
			@PathParam("academic_session_id") int academicSessionId, @QueryParam("institute_id") int instituteId, @QueryParam("limit") int limit, @QueryParam("offSet") int offSet) {
		final StudentDossierDetails studentDossierDetails = studentAdmissionManager.getStudentDossierDetails(instituteId, academicSessionId, studentId, limit, offSet);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentDossierDetails).build();
	}


	@DELETE
	@Path("soft-delete-student")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response softDeleteStudent(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, LinkedHashSet<String> admissionNumbers) {
		final StudentSoftDeleteResponse studentSoftDeleteResponse = studentAdmissionManager.softDeleteStudents(instituteId, userId, admissionNumbers);
		if (studentSoftDeleteResponse == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to soft delete students"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentSoftDeleteResponse).build();
	}

	// Student Information Management APIs

	@GET
	@Path("management/field-metadata")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentManagementFieldMetadata(@QueryParam("institute_id") int instituteId) {
		final StudentManagementInputFieldData studentManagementInputFieldData = studentManager.getStudentManagementFieldMetadata(instituteId);
		if (studentManagementInputFieldData == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to get student management fields"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentManagementInputFieldData).build();
	}

	/**
	 * Returns the list of all students in a particular class with section filter in given academic
	 * session
	 *
	 * @param academicSessionId
	 * @param standardId
	 * @param instituteId
	 * @return
	 */
	@GET
	@Path("management/{standard_id}/field-data")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassStudentsFieldData(@PathParam("standard_id") UUID standardId,
											  @QueryParam("institute_id") int instituteId,
											  @QueryParam("academic_session_id") int academicSessionId,
									 @QueryParam("section_ids") String sectionIdsStr, @QueryParam("field_list_csv") String fieldListCSV) {

		//passing studentExamResultDetailsMap as null here, as that we needed where we want to update/display data from exam module (for updating roll no & sections)
		final StudentManagementFieldDetails studentManagementFieldDetails = studentManager.getClassStudentsFieldData(instituteId, academicSessionId,
				standardId, sectionIdsStr, fieldListCSV, null);

		return Response.status(Response.Status.OK.getStatusCode()).entity(studentManagementFieldDetails).build();
	}

	@POST
	@Path("management/{standard_id}/field-data")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassStudentsFieldData(@PathParam("standard_id") UUID standardId,
											  @QueryParam("institute_id") int instituteId,
											  @QueryParam("academic_session_id") int academicSessionId,
											  @QueryParam("section_ids") String sectionIdsStr,
											  @QueryParam("user_id") UUID userId,
											  StudentManagementUpdateFieldPayload payload) {

		final boolean success = studentManager.updateClassStudentsFieldData(instituteId, academicSessionId,
				standardId, sectionIdsStr, userId, payload, true);

		if (!success) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update student details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("management/field-data")
	@Produces(MediaType.APPLICATION_JSON)
	public Response bulkHostelAssignment(@QueryParam("institute_id") int instituteId,
										 @QueryParam("academic_session_id") int academicSessionId,
										 @QueryParam("user_id") UUID userId,
										 StudentManagementUpdateFieldPayload payload) {

		final boolean success = studentManager.updateClassStudentsFieldData(instituteId, academicSessionId,
				null, null, userId, payload, false);

		if (!success) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update student details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@PUT
	@Path("management/admission-number-data")
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateStudentsAdmissionNumberData(@QueryParam("institute_id") int instituteId,
											  @QueryParam("academic_session_id") int academicSessionId,
											  @QueryParam("user_id") UUID userId,
											  StudentUpdateAdmissionNumberPayload payload) {

		final boolean success = studentManager.updateStudentAdmissionNumber(instituteId, academicSessionId,
				userId, payload);

		if (!success) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update student details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("/tags")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateStudentTag(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId, StudentTaggedPayload studentTaggedPayload) {
		final boolean updated = studentManager.updateStudentTag(instituteId, userId, studentTaggedPayload);
		if (!updated) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to update student tag."));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	// End of Student Information Management APIs
}