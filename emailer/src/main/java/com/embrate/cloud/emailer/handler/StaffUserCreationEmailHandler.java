/**
 * 
 */
package com.embrate.cloud.emailer.handler;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.emailer.content.builder.StaffUserCreationContentBuilder;
import com.embrate.cloud.emailer.service.IEmailService;
import com.lernen.cloud.core.api.email.EmailData;
import com.lernen.cloud.core.api.email.EmailDestination;
import com.lernen.cloud.core.api.email.SendEmailResponse;
import com.lernen.cloud.core.api.user.authentication.RegeneratePasswordUserData;

/**
 * <AUTHOR>
 *
 */
public class StaffUserCreationEmailHandler {
	private static final Logger logger = LogManager.getLogger(StaffUserCreationEmailHandler.class);

	private final static String FROM_ADDRESS = "<EMAIL>";
	private final static String FROM_NAME = "Embrate School Management";
	
	private final StaffUserCreationContentBuilder staffUserCreationContentBuilder;
	private final IEmailService emailService;

	public StaffUserCreationEmailHandler(StaffUserCreationContentBuilder staffUserCreationContentBuilder,
			IEmailService emailService) {
		this.staffUserCreationContentBuilder = staffUserCreationContentBuilder;
		this.emailService = emailService;
		
	}
	
	public boolean sendStaffUserCreationEmail(RegeneratePasswordUserData<Void> regeneratePasswordUserData) {

		if (regeneratePasswordUserData == null || regeneratePasswordUserData.getUser() == null
				|| StringUtils.isBlank(regeneratePasswordUserData.getNewPassword())) {
			logger.error("Invalid input to send user credentails");
			return false;
		}

		SendEmailResponse sendEmailResponse = emailService.sendEmail(new EmailData(FROM_ADDRESS,
				FROM_NAME, staffUserCreationContentBuilder.getEmailSubject(), 
				staffUserCreationContentBuilder.getTextContent(), 
				staffUserCreationContentBuilder.getHTMLContent(regeneratePasswordUserData),
				new EmailDestination(Arrays.asList(regeneratePasswordUserData.getUser().getEmail().trim()))));

		if (!sendEmailResponse.isSuccess()) {
			logger.error("Failed to generate password reset email for user {}, institute {}",
					regeneratePasswordUserData.getUser().getUuid(),
					regeneratePasswordUserData.getUser().getInstituteId());
			return false;
		}

		logger.info("Password reset email send successfully for user {}, institute {}, message id {}",
				regeneratePasswordUserData.getUser().getUuid(), regeneratePasswordUserData.getUser().getInstituteId(),
				sendEmailResponse.getMessageId());

		return true;

	}

}
