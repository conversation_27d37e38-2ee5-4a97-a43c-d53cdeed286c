package com.embrate.cloud.dao.tier.attendance.mappers;

import com.embrate.cloud.core.api.attendance.AttendanceInputType;
import com.embrate.cloud.core.api.attendance.AttendanceLog;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 * <AUTHOR>
 */
public class UserAttendanceLogRowMapper implements RowMapper<AttendanceLog> {

	static final String INSTITUTE_ID = "institute_id";
	static final String USER_ID = "user_id";
	static final String USER_TYPE = "user_type";
	static final String INPUT_TYPE = "input_type";
	static final String LOG_TIME = "log_time";
	static final String ATTENDANCE_TIME = "attendance_time";
	static final String ATTENDANCE_DATE = "attendance_date";
	static final String LOGGED_BY = "logged_by";
	static final String METADATA = "metadata";


	@Override
	public AttendanceLog mapRow(ResultSet rs, int rowNum) throws SQLException {

		final Type metaDataMap = new TypeToken<Map<String, Object>>() {
		}.getType();
		String metadataStr = rs.getString(METADATA);
		Map<String, String> metaData = null;
		if (StringUtils.isNotBlank(metadataStr)) {
			metaData = GSON.fromJson(metadataStr, metaDataMap);
		}

		UUID loggedBy = null;
		String loggedByStr = rs.getString(LOGGED_BY);
		if (StringUtils.isNotBlank(loggedByStr)) {
			loggedBy = UUID.fromString(loggedByStr);
		}

		return new AttendanceLog(null, null, UUID.fromString(rs.getString(USER_ID)),
				UserType.valueOf(rs.getString(USER_TYPE)), AttendanceInputType.valueOf(rs.getString(INPUT_TYPE)),
				null, rs.getTimestamp(LOG_TIME).getTime(), rs.getTimestamp(ATTENDANCE_TIME).getTime(),
				0l, loggedBy, metaData);
	}
}
