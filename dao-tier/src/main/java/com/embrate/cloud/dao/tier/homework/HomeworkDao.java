/**
 * 
 */
package com.embrate.cloud.dao.tier.homework;

import com.embrate.cloud.core.api.discussionboard.ChannelDetailsPayload;
import com.embrate.cloud.core.api.discussionboard.DiscussionBoardEntity;
import com.embrate.cloud.core.api.homework.*;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.dao.tier.discussionboard.DiscussionBoardDao;
import com.embrate.cloud.dao.tier.homework.mappers.*;
import com.embrate.cloud.dao.tier.push.notification.PushNotificationDao;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.DatabaseException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import com.lernen.cloud.dao.tier.user.mappers.UserRowMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class HomeworkDao {
	
	private static final Logger logger = LogManager.getLogger(HomeworkDao.class);

	private final JdbcTemplate jdbcTemplate;

	private final DiscussionBoardDao discussionBoardDao;
	
	private final TransactionTemplate transactionTemplate;

	private final PushNotificationDao pushNotificationDao;
	private static final Gson GSON = SharedConstants.GSON;
	
	private static final UserRowMapper USER_ROW_MAPPER = new UserRowMapper();
	private static final HomeworkDetailsRowMapper HOMEWORK_DETAILS_ROW_MAPPER = new HomeworkDetailsRowMapper();
	private static final HomeworkDetailsInnerQueryRowMapper HOMEWORK_DETAILS_INNER_QUERY_ROW_MAPPER = new HomeworkDetailsInnerQueryRowMapper();
	private static final StudentHomeworkDetailsRowMapper STUDENT_HOMEWORK_DETAILS_ROW_MAPPER = new StudentHomeworkDetailsRowMapper();
	private static final StandardHomeworkDetailsRowMapper STANDRAD_HOMEWORK_DETAILS_ROW_MAPPER = new StandardHomeworkDetailsRowMapper();
	private static final HomeworkSubmissionDetailsRowMapper HOMEWORK_SUBMISSION_DETAILS_ROW_MAPPER = new HomeworkSubmissionDetailsRowMapper();
	private static final StandardHomeworkSubmissionDetailsRowMapper STANDRAD_HOMEWORK_SUBMISSION_DETAILS_ROW_MAPPER = new StandardHomeworkSubmissionDetailsRowMapper();
	private static final StudentHomeworkViewSubmissionDetailsRowMapper STUDENT_HOMEWORK_VIEW_SUBMISSION_DETAILS_ROW_MAPPER = new StudentHomeworkViewSubmissionDetailsRowMapper();
	private static final HomeworkViewDetailsRowMapper HOMEWORK_VIEW_DETAILS_ROW_MAPPER = new HomeworkViewDetailsRowMapper();


    private static final String ADD_SAVED_HOMEWORK_DETAILS = "insert into homework_details(institute_id,academic_session_id, homework_id, standard_id, section_ids, course_id, "
            + " chapter, title,homework_type, allow_mobile_app_submissions, attach_discussion_forum, created_user_id, description, due_date, status,"
            + " faculty_user_id, faculty_name, allow_edit_after_submit, is_grade, max_marks, scheduled_timestamp) "
            + " values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

    private static final String ADD_BROADCASTED_HOMEWORK_DETAILS = "insert into homework_details(institute_id,academic_session_id, homework_id, standard_id, section_ids, course_id, "
            + " chapter, title,homework_type, allow_mobile_app_submissions, attach_discussion_forum, created_user_id, description, due_date, status,"
            + " faculty_user_id, faculty_name, allow_edit_after_submit, is_grade, max_marks, broadcasted_user_id, broadcasted_timestamp) "
            + " values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

    private static final String GET_HOMEWORK_BY_ID = "select * from homework_details "
            + " inner join standards on standards.standard_id = homework_details.standard_id "
            + " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = (select academic_session_id from academic_session where institute_id = ? order by end_year desc limit 1) "
            + " left join class_courses on class_courses.course_id = homework_details.course_id "
            + " where homework_details.homework_id = ? ";

    private static final String GET_HOMEWORK_BY_IDS = "select * from homework_details "
            + " inner join standards on standards.standard_id = homework_details.standard_id "
            + " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = (select academic_session_id from academic_session where institute_id = ? order by end_year desc limit 1) "
            + " left join class_courses on class_courses.course_id = homework_details.course_id "
            + " where homework_details.homework_id in %s ";

	private static final String GET_HOMEWORK_BY_STAFF = "select * from homework_details "
			+ " inner join standards on standards.standard_id = homework_details.standard_id "
			+ " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = ? "
			+ " left join class_courses on class_courses.course_id = homework_details.course_id "
			+ " where homework_details.institute_id = ? and homework_details.academic_session_id = ? %s %s %s %s %s  ";

    private static final String UPDATE_HOMEWORK_DOCUMENTS = " update homework_details set attachments = ? where homework_id = ? ";

    private static final String UPDATE_HOMEWORK_SUBMISSION_DOCUMENTS = " update homework_submission_details set attachments = ? where homework_submission_id = ? ";

    private static final String UPDATE_HOMEWORK_DETAILS = "update homework_details set chapter = ?, title = ?, description = ?,homework_type = ?, "
			+ " allow_mobile_app_submissions = ?, attach_discussion_forum = ?, due_date = ?, faculty_user_id = ?, faculty_name = ?, "
			+ " allow_edit_after_submit = ?, is_grade = ?, max_marks = ?, scheduled_timestamp = ? "
            + " where institute_id = ? and homework_id = ? ";

    private static final String UPDATE_HOMEWORK_SUBMISSION_DETAILS = "update homework_submission_details set description = ?, submission_timestamp = ? "
            + " where homework_submission_id = ? ";

    private static final String UPDATE_HOMEWORK_SUBMISSION_STUDENT_STATUS = "update homework_submission_details set status = ?, teacher_status_user_id = ?,teachers_status_timestamp = ?, remarks = ? "
            + " where homework_submission_id = ? and student_id = ? ";

    private static final String DELETE_HOMEWORK_DETAILS_BY_HOMEWORK_ID = " delete from homework_details where institute_id = ? and homework_id = ? ";

    private static final String DELETE_HOMEWORK_SUBMISSION_DETAILS_BY_HOMEWORK_SUBMISSION_ID = " delete from homework_submission_details where homework_submission_id = ? ";
	private static final String GET_HOMEWORK_DETAILS_BY_FILTER = "select * from standards "
			+ " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = ? "
			+ " left join homework_details on homework_details.standard_id = standards.standard_id and homework_details.academic_session_id = ? %s "
			+ " left join class_courses on class_courses.standard_id =  standards.standard_id and class_courses.academic_session_id = ? "
			+ " and (homework_details.course_id = class_courses.course_id) "
			+ " where standards.institute_id = ?  %s %s";

	private static final String GET_STANDARD_SECTION_ID_BY_FILTER = " and standard_section_mapping.section_id = ? ";

    private static final String DATE_CLAUSE = " and homework_details.created_timestamp >= ? and homework_details.created_timestamp <= ?";

	private static final String DATE_CLAUSE_FILTER = " and ((homework_details.status = 'SAVED'  and homework_details.created_timestamp >= ? and homework_details.created_timestamp <= ?) or (homework_details.status = 'BROADCASTED' and homework_details.broadcasted_timestamp >= ? and homework_details.broadcasted_timestamp <=  ?)) ";
    private static final String COURSE_CLAUSE = " and class_courses.course_id = ? ";

    private static final String STANDARD_CLAUSE = " and standards.standard_id = ? ";

    private static final String GET_HOMEWORK_SUBMISSION_BY_ID = "select * from homework_submission_details "
            + " left join homework_details on homework_details.homework_id = homework_submission_details.homework_id "
            + " left join students on students.student_id =  homework_submission_details.student_id "
            + " left join student_academic_session_details on students.student_id = student_academic_session_details.student_id and student_academic_session_details.academic_session_id = homework_details.academic_session_id "
            + " left join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id "
            + " left join standards on student_academic_session_details.standard_id = standards.standard_id "
            + " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and student_academic_session_details.section_id = standard_section_mapping.section_id "
            + " left join class_courses on class_courses.standard_id =  standards.standard_id and class_courses.academic_session_id = student_academic_session_details.academic_session_id and class_courses.course_id = homework_details.course_id "
            + " where homework_submission_details.homework_submission_id = ? ";

    private static final String GET_HOMEWORK_SUBMISSION_BY_HOMEWORK_ID = "select * from homework_submission_details "
            + " inner join homework_details on homework_details.homework_id = homework_submission_details.homework_id "
            + " inner join students on students.student_id =  homework_submission_details.student_id "
            + " inner join student_academic_session_details on students.student_id = student_academic_session_details.student_id and student_academic_session_details.academic_session_id = ? "
            + " inner join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id "
            + " inner join standards on student_academic_session_details.standard_id = standards.standard_id "
            + " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and student_academic_session_details.section_id = standard_section_mapping.section_id "
            + " left join class_courses on class_courses.standard_id =  homework_details.standard_id and class_courses.academic_session_id = student_academic_session_details.academic_session_id and class_courses.course_id = homework_details.course_id "
            + " where homework_submission_details.homework_id = ? ";
	private static final String GET_STUDENT_HOMEWORK_VIEW_DETAILS = "select * from students "
			+ " inner join student_academic_session_details on students.student_id = student_academic_session_details.student_id and student_academic_session_details.academic_session_id = ? and student_academic_session_details.standard_id = ? "
			+ " inner join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id "
			+ " inner join standards on student_academic_session_details.standard_id = standards.standard_id "
			+ " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id "
			+ " and student_academic_session_details.section_id = standard_section_mapping.section_id "
			+ " inner join homework_details on student_academic_session_details.standard_id = homework_details.standard_id and homework_details.homework_id = ? "
			+ " left join homework_view_details on students.student_id = homework_view_details.student_id and homework_details.homework_id = homework_view_details.homework_id "
			+ " left join homework_submission_details on homework_submission_details.student_id = students.student_id and homework_submission_details.homework_id = homework_details.homework_id "
			+ " left join class_courses on class_courses.standard_id = homework_details.standard_id and class_courses.academic_session_id = student_academic_session_details.academic_session_id "
			+ " and class_courses.course_id = homework_details.course_id where students.institute_id = ? "
			+ " and students.final_status = 'ENROLLED' and student_academic_session_details.session_status = 'ENROLLED' ";

	private static final String GET_SECTION_ID_BY_FILTER = " and student_academic_session_details.section_id = ? ";

    private static final String GET_HOMEWORK_SUBMISSION_DETAILS_BY_FILTER = "select * from standards  "
            + " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = ? "
            + " left join class_courses on class_courses.standard_id =  standards.standard_id and class_courses.academic_session_id = ? "
            + " left join homework_details on homework_details.standard_id = standards.standard_id and  homework_details.course_id = class_courses.course_id "
            + " left join homework_submission_details on homework_submission_details.homework_id = homework_details.homework_id "
            + " where standards.institute_id = ? %s %s ";

    private static final String ADD_HOMEWORK_SUBMISSION_DETAILS = "insert into homework_submission_details(homework_submission_id, homework_id, student_id,submitted_by, description, "
            + " status) values(?, ?, ?, ?, ?, ?) ";
	private static final String GET_STUDENT_HOMEWORK_DETAILS = "select * from students "
			+ " join (select student_academic_session_details.* from student_academic_session_details  join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id where student_academic_session_details.student_id = ? order by academic_session.end_year desc limit 1)  as student_academic_session_details on students.student_id = student_academic_session_details.student_id "
			+ " left join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id "
			+ " left join standards on student_academic_session_details.standard_id = standards.standard_id "
			+ " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and student_academic_session_details.section_id = standard_section_mapping.section_id "
			+ " inner join homework_details on standards.standard_id = homework_details.standard_id and student_academic_session_details.academic_session_id = homework_details.academic_session_id "
			+ " left join class_courses on class_courses.course_id = homework_details.course_id and class_courses.academic_session_id = student_academic_session_details.academic_session_id "
			+ " left join (select * from homework_submission_details inner join (select student_id as si, homework_id as hi, MAX(submission_timestamp) as st from homework_submission_details where homework_submission_details.student_id = ? group by student_id, homework_id) as a on a.si = homework_submission_details.student_id and a.hi = homework_submission_details.homework_id and a.st = homework_submission_details.submission_timestamp where homework_submission_details.student_id = ?) as homework_submission_details on homework_submission_details.homework_id = homework_details.homework_id and homework_submission_details.student_id = students.student_id "
			+ " left join student_course_assignment on class_courses.course_id = student_course_assignment.course_id and students.student_id = student_course_assignment.student_id "
			+ " where students.institute_id = ? and students.student_id = ? and "
			+ " ((class_courses.mandatory = 1 or student_course_assignment.course_id is not null) or class_courses.course_id is null) and "
			+ " homework_details.status = 'BROADCASTED'";

    private static final String HOMEWORK_COURSE_CLAUSE = " and homework_details.course_id = ? ";

	private static final String COURSE_ID_CLAUSE = " and homework_details.course_id in ";

	private static final String HOMEWORK_STANDARD_CLAUSE = " and homework_details.standard_id = ? ";

	private static final String STAFF_CLAUSE = " and homework_details.faculty_user_id in ";

	private static final String HOMEWORK_TYPE_CLAUSE = " and homework_details.homework_type in ";

    private static final String HOMEWORK_ID_CLAUSE = " and homework_details.homework_id = ?";

    private static final String ORDER_BY_LIMIT_SUBMISSION_TIMESTAMP = " order by submission_timestamp desc limit 1 ";

    private static final String UPDATE_HOMEWORK_STATUS = "update homework_details set status = ?, updated_user_id = ?, broadcasted_timestamp = ?, broadcasted_user_id = ? "
            + " where institute_id = ? and homework_id in %s";

    private static final String GET_HOMEWORK_USERS = "select distinct users.* from homework_details "
            + "	left join class_courses on homework_details.course_id = class_courses.course_id "
            + " join standards on homework_details.standard_id = standards.standard_id "
            + " join student_academic_session_details on student_academic_session_details.academic_session_id = homework_details.academic_session_id and student_academic_session_details.standard_id = homework_details.standard_id "
            + " join students on student_academic_session_details.student_id = students.student_id %s "
            + " join users on students.student_id = users.user_id "
            + " where students.institute_id = ? and student_academic_session_details.session_status = 'ENROLLED' ";

    private static final String SECTION_IDS = " and student_academic_session_details.section_id in ";

    private static final String LIST_HOMEWORK_ID = " and homework_details.homework_id in %s ";

    private static final String SINGLE_HOMEWORK_ID = " and homework_details.homework_id = ? ";

    private static final String UPDATE_FACULTY_HOMEWORK_SUBMISSION_STATUS = "update homework_submission_details " +
            " set status = ?, teacher_status_user_id = ?, teachers_status_timestamp = ?, result = ?, remarks = ? "
            + " where homework_submission_id = ? and homework_id = ? and student_id = ?";

//	private static final String ADD_STUDENT_HOMEWORK_VIEW_DETAILS = "insert into homework_view_details(homework_id,student_id) values(?,?)";
	private static final String ADD_STUDENT_HOMEWORK_VIEW_DETAILS = "insert into homework_view_details(homework_id, student_id) " +
			"values(?, ?) ON DUPLICATE KEY UPDATE viewed_on = viewed_on";
    private static final String GET_STUDENT_HOMEWORK_VIEW_DETAILS_BY_STUDENT_ID = "select * from homework_view_details where homework_id = ? and student_id = ? ";

	private static final String GET_ALL_INSTITUTE_HOMEWORK = "select * from homework_details "
			+ " inner join standards on standards.standard_id = homework_details.standard_id "
			+ " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = ? "
			+ " left join class_courses on class_courses.course_id = homework_details.course_id "
			+ " where homework_details.institute_id = ? and homework_details.academic_session_id = ? ";
	private static final String GET_ALL_INSTITUTE_HOMEWORK_LIMIT_OFFSET_BY_STATUS = "select * from "
			+ " (select * from homework_details where status = ? and institute_id = ? and academic_session_id = ? %s order by created_timestamp desc limit ? offset ?) hd "
			+ " inner join standards on standards.standard_id = hd.standard_id "
			+ " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = ? "
			+ " left join class_courses on class_courses.course_id = hd.course_id ";

	private static final String DELETE_USER_HOMEWORK_VIEW_DETAILS = "delete from homework_view_details "
			+ " where homework_id = ? ";
	private static final String DELETE_HOMEWORK_SUBMISSION_DETAILS = "delete from homework_submission_details "
			+ " where homework_id = ? ";

//	select * from (select * from homework_details where status = 'BROADCASTED' and institute_id = 10230 and academic_session_id = 136 limit 80 offset 0) hd inner join standards on standards.standard_id = hd.standard_id  left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = 136 left join class_courses on class_courses.course_id = hd.course_id;
	
	public HomeworkDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate,
			DiscussionBoardDao discussionBoardDao, PushNotificationDao pushNotificationDao) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
		this.discussionBoardDao = discussionBoardDao;
		this.pushNotificationDao = pushNotificationDao;
	}

	public UUID addHomework(int instituteId, UUID userId, HomeworkPayload homeworkPaylaod, boolean saveOnly) {
		try {
//			final UUID homeworkId = transactionTemplate.execute(new TransactionCallback<UUID>() {
//				@Override
//				public UUID doInTransaction(TransactionStatus status) {
					final UUID homeworkId = addHomeworkDetails(homeworkPaylaod, saveOnly);
					if (homeworkId == null) {
						throw new RuntimeException("Unable to create homework");
					}
					boolean result = discussionBoardDao.createChannel(
							new ChannelDetailsPayload(instituteId, null, DiscussionBoardEntity.HOMEWORK,
									homeworkId.toString(), null, null, null, UserType.SYSTEM));

					if (!result) {
						throw new RuntimeException("Error while creating channel. Please try again.");
					}
					
					return homeworkId;
//				}
//			});
//			return homeworkId;
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (final Exception e) {
			logger.error("Unable to add homework for institute {}", instituteId, e);
		}
		return null;
	}
	
	private UUID addHomeworkDetails(HomeworkPayload homeworkPaylaod, boolean saveOnly) {
		try {
			UUID homeworkId = UUID.randomUUID();
			Boolean result = true;
			if (saveOnly) {
				result = jdbcTemplate.update(ADD_SAVED_HOMEWORK_DETAILS, homeworkPaylaod.getInstituteId(),homeworkPaylaod.getAcademicSessionId(),
						homeworkId.toString(), homeworkPaylaod.getStandardId().toString(),
						CollectionUtils.isEmpty(homeworkPaylaod.getSectionIdList()) ? null :
								GSON.toJson(homeworkPaylaod.getSectionIdList()),homeworkPaylaod.getCourseId() == null ? null :
						homeworkPaylaod.getCourseId().toString(), homeworkPaylaod.getChapter(),
						homeworkPaylaod.getTitle(),homeworkPaylaod.getHomeworkType().name() ,
						homeworkPaylaod.isAllowMobileAppSubmissions(), homeworkPaylaod.isAttachDiscussionForum(),
						homeworkPaylaod.getCreatedUserId().toString(),
						homeworkPaylaod.getDescription(), (homeworkPaylaod.getDueDate() != null)
						&& (homeworkPaylaod.getDueDate() > 0) ? new Timestamp(homeworkPaylaod.getDueDate() * 1000l) : null,
						HomeworkStatus.SAVED.name(), homeworkPaylaod.getFacultyUserId() == null ? 
						null : homeworkPaylaod.getFacultyUserId().toString(), homeworkPaylaod.getFacultyName(),
						homeworkPaylaod.isAllowEditAfterSubmit(), homeworkPaylaod.isGrade(), homeworkPaylaod.getMaxMarks(), 
						(homeworkPaylaod.getScheduledTimestamp() != null) && (homeworkPaylaod.getScheduledTimestamp() > 0)
						? new Timestamp(homeworkPaylaod.getScheduledTimestamp() * 1000l) : null) == 1;
			} else {
				result = jdbcTemplate.update(ADD_BROADCASTED_HOMEWORK_DETAILS, homeworkPaylaod.getInstituteId(),homeworkPaylaod.getAcademicSessionId(),
						homeworkId.toString(), homeworkPaylaod.getStandardId().toString(),
						CollectionUtils.isEmpty(homeworkPaylaod.getSectionIdList()) ? null :
								GSON.toJson(homeworkPaylaod.getSectionIdList()),
						homeworkPaylaod.getCourseId() == null ? null :
								homeworkPaylaod.getCourseId().toString(), homeworkPaylaod.getChapter(),
						homeworkPaylaod.getTitle(),homeworkPaylaod.getHomeworkType().name(),
						homeworkPaylaod.isAllowMobileAppSubmissions(), homeworkPaylaod.isAttachDiscussionForum(),
						homeworkPaylaod.getCreatedUserId().toString(),
						homeworkPaylaod.getDescription(), (homeworkPaylaod.getDueDate() != null)
						&& (homeworkPaylaod.getDueDate() > 0) ? new Timestamp(homeworkPaylaod.getDueDate() * 1000l) : null,
						HomeworkStatus.BROADCASTED.name(), homeworkPaylaod.getFacultyUserId() == null ? 
						null : homeworkPaylaod.getFacultyUserId().toString(), homeworkPaylaod.getFacultyName(),
						homeworkPaylaod.isAllowEditAfterSubmit(), homeworkPaylaod.isGrade(), homeworkPaylaod.getMaxMarks(), 
						homeworkPaylaod.getBroadcastedUserId().toString(), new Timestamp(System.currentTimeMillis())) == 1;
			}
			if (!result) {
				throw new RuntimeException("Error while adding homework details. Please try again.");
			}


			return homeworkId;
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (final Exception e) {
			logger.error("Unable to add homework for institute {}", homeworkPaylaod.getInstituteId(), e);
			throw e;
		}
	}

	public HomeworkDetails getHomeworkDetails(int instituteId, UUID homeworkId) {
		try {
			final Object[] args = { instituteId, homeworkId.toString() };
			return HomeworkDetailsRowMapper.getHomeworkResponse(
					jdbcTemplate.query(GET_HOMEWORK_BY_ID, args, HOMEWORK_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while fetching homework details for homework id {}", homeworkId, e);
		}
		return null;
	}

	public boolean updateDocuments(UUID homeworkId, List<Document<HomeworkDocumentType>> homeworkAttachments) {
		try {
			final Object[] args = { GSON.toJson(homeworkAttachments), homeworkId.toString() };
			return jdbcTemplate.update(UPDATE_HOMEWORK_DOCUMENTS, args) == 1;
		} catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, null, null);
			logger.error("Error while updating homework documents for homework id {}", homeworkId);
		} catch (final Exception e) {
			logger.error("Exception while updating homework documents for homework id {}", homeworkId, e);
		}
		return false;
	}

	public UUID updateHomework(int instituteId, HomeworkPayload updateHomeworkPaylaod) {
		try {
			boolean result = jdbcTemplate.update(UPDATE_HOMEWORK_DETAILS, updateHomeworkPaylaod.getChapter(),
					updateHomeworkPaylaod.getTitle(), updateHomeworkPaylaod.getDescription(),updateHomeworkPaylaod.getHomeworkType().name(),
					updateHomeworkPaylaod.isAllowMobileAppSubmissions(), updateHomeworkPaylaod.isAttachDiscussionForum(),
					(updateHomeworkPaylaod.getDueDate() != null) && (updateHomeworkPaylaod.getDueDate() > 0)
					? new Timestamp(updateHomeworkPaylaod.getDueDate() * 1000l) : null,
					updateHomeworkPaylaod.getFacultyUserId() == null ? null : 
					updateHomeworkPaylaod.getFacultyUserId().toString(),
					updateHomeworkPaylaod.getFacultyName(), updateHomeworkPaylaod.isAllowEditAfterSubmit(),
					updateHomeworkPaylaod.isGrade(), updateHomeworkPaylaod.getMaxMarks(), 
					(updateHomeworkPaylaod.getScheduledTimestamp() != null) && (updateHomeworkPaylaod.getScheduledTimestamp() > 0)
					? new Timestamp(updateHomeworkPaylaod.getScheduledTimestamp() * 1000l) : null,
							updateHomeworkPaylaod.getInstituteId(), updateHomeworkPaylaod.getHomeworkId().toString()) == 1;

			if(result) {
				return updateHomeworkPaylaod.getHomeworkId();
			}
			return null;
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Exception while updating homework details for institute {}, payload {}",
					updateHomeworkPaylaod.getInstituteId(), updateHomeworkPaylaod, e);
		}
		return null;
	}

	public boolean deleteHomeworkDetails(int instituteId, UUID userId, UUID homeworkId) {
		try {
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					boolean homeworkSubmissionDeleted = deleteHomeworkSubmissionDetails(homeworkId);
					if(!homeworkSubmissionDeleted) {
						logger.error("Unable to delete homework submission details for institute {}, homework {}",
								instituteId, homeworkId);
						throw new RuntimeException("Unable to delete homework submission details.");
					}
					boolean homeworkViewDetails = deleteUserHomeworkViewDetails(homeworkId);
					if(!homeworkViewDetails) {
						logger.error("Unable to delete user homework view details for institute {}, homework {}",
								instituteId, homeworkId);
						throw new RuntimeException("Unable to delete user homework view details.");
					}
					boolean homeworkMetadataDeleted = jdbcTemplate.update(DELETE_HOMEWORK_DETAILS_BY_HOMEWORK_ID,
							instituteId, homeworkId.toString()) == 1;
					if (!homeworkMetadataDeleted) {
						throw new EmbrateRunTimeException("Unable to delete lecture");
					}
					boolean channelDeleted = discussionBoardDao.deleteChannelByEntityNonAtomic(instituteId, userId,
							DiscussionBoardEntity.HOMEWORK, homeworkId.toString());

					if (!channelDeleted) {
						throw new EmbrateRunTimeException("Unable to delete lecture channel");
					}

					boolean bellNotificationDeleted = pushNotificationDao.
							deleteBellNotificationByEntityDetails(instituteId, homeworkId, Arrays.asList(
									NotificationEntity.HOMEWORK, NotificationEntity.HOMEWORK_DISCUSSION, NotificationEntity.HOMEWORK_MARKING, NotificationEntity.ADMIN_HOMEWORK));
					if(!bellNotificationDeleted) {
						logger.error("Unable to delete homework bell notification for institute {}, homework {}",
								instituteId, homeworkId);
						throw new RuntimeException("Unable to delete homework bell notification.");
					}
					return true;
				}
			});
			return success;
		} catch (final Exception e) {
			logger.error("Exception while deleting homework details for institute {}, homeworkId {}", instituteId,
					homeworkId, e);
		}

		return false;
	}

	private boolean deleteUserHomeworkViewDetails(UUID homeworkId) {
		try {
			return jdbcTemplate.update(DELETE_USER_HOMEWORK_VIEW_DETAILS, homeworkId.toString()) >= 0;
		} catch (final Exception e) {
			logger.error("Exception while deleting homework view details for homeworkId {}",
					homeworkId, e);
		}
		return false;
	}

	private boolean deleteHomeworkSubmissionDetails(UUID homeworkId) {
		try {
			return jdbcTemplate.update(DELETE_HOMEWORK_SUBMISSION_DETAILS, homeworkId.toString()) >= 0;
		} catch (final Exception e) {
			logger.error("Exception while deleting homework submission details for homeworkId {}",
					homeworkId, e);
		}
		return false;
	}

	public StandardHomeworkDetails getHomeworkDetailsByFilter(int instituteId, int academicSessionId, UUID standardId,
			Integer date, UUID courseId) {
		try {
			String query = GET_HOMEWORK_DETAILS_BY_FILTER;
			final List<Object> args = new ArrayList<>();

			args.add(academicSessionId);
			args.add(academicSessionId);

			String dateFilter = "";
			if (date != null && date > 0) {
				dateFilter = DATE_CLAUSE;
				Long dateStart = DateUtils.getDayStart(date * 1000l, DateUtils.DEFAULT_TIMEZONE);
				Long dateEnd = DateUtils.getDayEnd(date * 1000l, DateUtils.DEFAULT_TIMEZONE);
				args.add(new Timestamp(dateStart));
				args.add(new Timestamp(dateEnd));
			}

			args.add(academicSessionId);
			args.add(instituteId);
			
			String standardIdFilter = "";
			if (standardId != null) {
				standardIdFilter = STANDARD_CLAUSE;
				args.add(standardId.toString());
			}

			String courseIdFilter = "";
			if (courseId != null) {
				courseIdFilter = COURSE_CLAUSE;
				args.add(courseId.toString());
			}

			return StandardHomeworkDetailsRowMapper
					.getHomeworkDetailsByStandard(jdbcTemplate.query(String.format(query, dateFilter, standardIdFilter, courseIdFilter),
							args.toArray(), STANDRAD_HOMEWORK_DETAILS_ROW_MAPPER));

		} catch (final Exception e) {
			logger.error(
					"Exception while getting homework details for instituteId {}, academicSessionId {}, standardId {}",
					instituteId, academicSessionId, standardId, e);
		}
		return null;
	}

	public HomeworkSubmissionDetails getHomeworkSubmissionDetails(UUID homeworkSubmissionId) {
		try {
			final Object[] args = { homeworkSubmissionId.toString() };
			return HomeworkSubmissionDetailsRowMapper.getHomeworkSubmissionResponse(
					jdbcTemplate.query(GET_HOMEWORK_SUBMISSION_BY_ID, args, HOMEWORK_SUBMISSION_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while fetching homework submission for homeworkSubmissionId {}", homeworkSubmissionId, e);
		}
		return null;
	}

	public List<HomeworkDetails>  getHomeworkDetailsByFilters(int instituteId, int academicSessionId, UUID standardId,
															 Set<Integer> sectionIdSet, Integer startDate, Integer endDate, Set<UUID>  courseIdSet, Set<UUID> staffIdSet, Set<HomeworkType>  homeworkTypeSet){
		try {
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(academicSessionId);

			StringBuilder dateInQuery= new StringBuilder();
			if (startDate != null && startDate > 0 && endDate != null && endDate > 0) {
				dateInQuery.append(DATE_CLAUSE_FILTER);
				long dateStart = DateUtils.getDayStart(startDate * 1000l, DateUtils.DEFAULT_TIMEZONE);
				long dateEnd = DateUtils.getDayEnd(endDate * 1000l, DateUtils.DEFAULT_TIMEZONE);
				args.add(new Timestamp(dateStart));
				args.add(new Timestamp(dateEnd));
				args.add(new Timestamp(dateStart));
				args.add(new Timestamp(dateEnd));
			}

			StringBuilder standardInQuery = new StringBuilder();
			if (standardId != null) {
				standardInQuery.append(HOMEWORK_STANDARD_CLAUSE);
				args.add(standardId.toString());
			}

			StringBuilder courseInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(courseIdSet)) {
				courseInQuery.append(COURSE_ID_CLAUSE);
				courseInQuery.append(" (");
				String courseDelimiter = "";
				for (UUID courseId : courseIdSet) {
					if(courseId == null) {
						continue;
					}
					args.add(courseId.toString());
					courseInQuery.append(courseDelimiter).append(" ? ");
					courseDelimiter = ",";
				}
				courseInQuery.append(") ");
			}

			StringBuilder staffInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(staffIdSet)) {
				staffInQuery.append(STAFF_CLAUSE);
				staffInQuery.append(" (");
				String staffDelimiter = "";
				for (UUID staffId : staffIdSet) {
					if(staffId == null) {
						continue;
					}
					args.add(staffId.toString());
					staffInQuery.append(staffDelimiter).append(" ? ");
					staffDelimiter = ",";
				}
				staffInQuery.append(") ");
			}

			StringBuilder homeworkInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(homeworkTypeSet)) {
				homeworkInQuery.append(HOMEWORK_TYPE_CLAUSE);
				homeworkInQuery.append(" (");
				String homeworkDelimiter = "";
				for (HomeworkType homeworkType : homeworkTypeSet) {
					if(homeworkType == null) {
						continue;
					}
					args.add(homeworkType.name());
					homeworkInQuery.append(homeworkDelimiter).append(" ? ");
					homeworkDelimiter = ",";
				}
				homeworkInQuery.append(") ");
			}
			return HomeworkDetailsRowMapper.getHomeworkResponseList(
					jdbcTemplate.query(String.format(GET_HOMEWORK_BY_STAFF, dateInQuery, standardInQuery, courseInQuery, staffInQuery, homeworkInQuery ),
							args.toArray(), HOMEWORK_DETAILS_ROW_MAPPER));

		} catch (final Exception e) {
			logger.error("Exception while fetching homework details for instituteId {}, academicSessionId {}, standardId {}, sectionIdSet {}, startDate {}, endDate {}, courseIdSet {}, staffIdSet {}, homeworkTypeSet {}",
					instituteId, academicSessionId, standardId, sectionIdSet, startDate, endDate, courseIdSet, staffIdSet, homeworkTypeSet, e);
		}
		return null;

	}
	public UUID homeworkSubmission(int instituteId, HomeworkSubmissionPayload homeworkSubmissionPaylaod) {
		try {
			UUID homeworkSubmissionId = UUID.randomUUID();
			return jdbcTemplate.update(ADD_HOMEWORK_SUBMISSION_DETAILS, homeworkSubmissionId.toString(),
					homeworkSubmissionPaylaod.getHomeworkId().toString(), homeworkSubmissionPaylaod.getStudentId().toString(),homeworkSubmissionPaylaod.getSubmittedBy().toString(),
					homeworkSubmissionPaylaod.getDescription(), homeworkSubmissionPaylaod.getHomeworkSubmissionStatus().name()) == 1? homeworkSubmissionId:null;

		} catch (final Exception e) {
			logger.error("Exception while submit homework submission for instituteId {}",instituteId, e);
		}
		return null;
	}

	public boolean addStudentHomeworkViewDetails(int instituteId,UUID homeworkId,UUID studentId){
		try {
			return jdbcTemplate.update(ADD_STUDENT_HOMEWORK_VIEW_DETAILS,homeworkId.toString(),studentId.toString()) >= 0;
		}
		catch (final Exception e) {
			logger.error("Exception while adding student homework submission for instituteId {}",instituteId, e);
		}
		return false;
	}

	public boolean addStudentHomeworkViewDetails(int instituteId, Set<UUID> homeworkIds,UUID studentId){
		try {
			List<Object []> args = new ArrayList<>();
			for(UUID homeworkId : homeworkIds){
				args.add(new Object [] {homeworkId.toString(),studentId.toString()});
			}
			return jdbcTemplate.batchUpdate(ADD_STUDENT_HOMEWORK_VIEW_DETAILS, args).length >= 0;
		}
		catch (final Exception e) {
			logger.error("Exception while adding student homework submission view for instituteId {}, homeworkIds {}, studentId {}",instituteId, homeworkIds, studentId, e);
		}
		return false;
	}

	public HomeworkViewDetails getStudentHomeWorkViewDetails(int instituteId,UUID homeworkId,UUID studentId){
		try {
			final Object[] args = {homeworkId.toString(),studentId.toString()};
			return jdbcTemplate.queryForObject(GET_STUDENT_HOMEWORK_VIEW_DETAILS_BY_STUDENT_ID, args, HOMEWORK_VIEW_DETAILS_ROW_MAPPER);
		}
		catch (final Exception e) {
			logger.error("Exception while getting student homework view details for instituteId {}",instituteId, e);
		}
		return null;
	}

	public boolean bulkHomeworkStatusUpdate(int instituteId, BulkHomeworkStatusUpdatePayload bulkHomeworkStatusUpdatePayload){
		try{
			final List<Object[]> updateArgs = new ArrayList<>();
			final List<Object[]> addArgs = new ArrayList<>();
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					int count = 0;
					int count1 = 0;
					for(StudentHomeworkSubmissionPayload studentHomeworkSubmissionPayload : bulkHomeworkStatusUpdatePayload.getStudentHomeworkSubmissionPayloadList()){
						if(studentHomeworkSubmissionPayload.getHomeworkSubmissionId() == null){
							final List<Object> args = new ArrayList<>();
							UUID homeworkSubmissionId = UUID.randomUUID();
							args.add(homeworkSubmissionId.toString());
							args.add(bulkHomeworkStatusUpdatePayload.getHomeworkId().toString());
							args.add(studentHomeworkSubmissionPayload.getStudentId().toString());
							args.add(bulkHomeworkStatusUpdatePayload.getSubmittedBy().toString());
							args.add(null);
							args.add(HomeworkSubmissionStatus.SUBMITTED.name());
							count1++;
							studentHomeworkSubmissionPayload.setHomeworkSubmissionId(homeworkSubmissionId);
							addArgs.add(args.toArray());
					}
						final List<Object> args = new ArrayList<>();
						args.add(bulkHomeworkStatusUpdatePayload.getHomeworkSubmissionStatus().name());
						args.add(bulkHomeworkStatusUpdatePayload.getSubmittedBy().toString());
						args.add(new Timestamp(System.currentTimeMillis()));
						args.add(bulkHomeworkStatusUpdatePayload.getRemarks()==null?null:bulkHomeworkStatusUpdatePayload.getRemarks());
						args.add(studentHomeworkSubmissionPayload.getHomeworkSubmissionId().toString());
						args.add(studentHomeworkSubmissionPayload.getStudentId().toString());
						count++;
						updateArgs.add(args.toArray());
					}
					final int[] rows1 = jdbcTemplate.batchUpdate(ADD_HOMEWORK_SUBMISSION_DETAILS,addArgs);
					if(rows1.length != count1){
						throw new RuntimeException("Unable to add bulk student submission details");
					}

					final int[] rows2 = jdbcTemplate.batchUpdate(UPDATE_HOMEWORK_SUBMISSION_STUDENT_STATUS,updateArgs);
					return rows2.length == count;
				}
			});
			return success;
		}
		catch (final Exception e){
			logger.error("Exception while submitting bulk students homework instituteId {}",instituteId, e);
		}
		return false;
	}

	public boolean addBulkHomeworkSubmissionDetails(int instituteId, BulkHomeworkStatusUpdatePayload bulkHomeworkStatusUpdatePayload){
		try{
			final List<Object[]> addArgs = new ArrayList<>();
			int count = 0;
			for(StudentHomeworkSubmissionPayload studentHomeworkSubmissionPayload : bulkHomeworkStatusUpdatePayload.getStudentHomeworkSubmissionPayloadList()){
				UUID homeworkSubmissionId = UUID.randomUUID();
				final List<Object> args = new ArrayList<>();
				args.add(homeworkSubmissionId.toString());
				args.add(bulkHomeworkStatusUpdatePayload.getHomeworkId().toString());
				args.add(studentHomeworkSubmissionPayload.getStudentId().toString());
				args.add(bulkHomeworkStatusUpdatePayload.getSubmittedBy().toString());
				args.add(null);
				args.add(HomeworkSubmissionStatus.SUBMITTED.name());
				count++;
				addArgs.add(args.toArray());
			}

			final int[] rows = jdbcTemplate.batchUpdate(ADD_HOMEWORK_SUBMISSION_DETAILS,addArgs);
			return rows.length == count;
		}
		catch (final Exception e){
			logger.error("Exception while adding the bulk students homework submit status instituteId {}",instituteId, e);
		}
		return false;
	}

	public boolean updateHomeworkSubmissionDocuments(UUID homeworkSubmissionId,
			List<Document<HomeworkDocumentType>> homeworkSubmissionAttachments) {
		try {
			final Object[] args = { GSON.toJson(homeworkSubmissionAttachments), homeworkSubmissionId.toString() };
			return jdbcTemplate.update(UPDATE_HOMEWORK_SUBMISSION_DOCUMENTS, args) == 1;
		} catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, null, null);
			logger.error("Error while updating homework submission documents {}", homeworkSubmissionId);
		} catch (final Exception e) {
			logger.error("Exception while updating homework submission documents {}", homeworkSubmissionId, e);
		}
		return false;
	}

	public UUID updateSubmittedHomework(int instituteId, HomeworkSubmissionPayload updatehomeworkSubmissionPaylaod) {
		try {
			boolean result = jdbcTemplate.update(UPDATE_HOMEWORK_SUBMISSION_DETAILS, 
					updatehomeworkSubmissionPaylaod.getDescription(), new Timestamp(System.currentTimeMillis()),
					updatehomeworkSubmissionPaylaod.getHomeworkSubmissionId().toString()) == 1;
			if(result) {
				return updatehomeworkSubmissionPaylaod.getHomeworkSubmissionId();
			}
		} catch (final Exception e) {
			logger.error("Exception while updating submitted homework for institute {}, payload {}",
					instituteId, updatehomeworkSubmissionPaylaod, e);
		}
		return null;
	}

	public boolean deleteHomeworkSubmittedDetails(int instituteId, UUID userId, UUID homeworkSubmissionId) {
		try {
			return jdbcTemplate.update(DELETE_HOMEWORK_SUBMISSION_DETAILS_BY_HOMEWORK_SUBMISSION_ID,
					instituteId, homeworkSubmissionId.toString()) == 1;
		} catch (final Exception e) {
			logger.error("Exception while deleting homework submission details for institute {}, lectureId {}", instituteId,
					homeworkSubmissionId, e);
		}
		return false;
	}

	@Deprecated
//	public StandardHomeworkSubmissionDetails getHomeworkSubmissionDetailsByFilter(int instituteId,
//			int academicSessionId, UUID standardId, UUID courseId) {
//		try {
//			String query = GET_HOMEWORK_SUBMISSION_DETAILS_BY_FILTER;
//			final List<Object> args = new ArrayList<>();
//
//			args.add(academicSessionId);
//			args.add(academicSessionId);
//
//			args.add(instituteId);
//
//			String standardIdFilter = "";
//			if (standardId != null) {
//				standardIdFilter = STANDARD_CLAUSE;
//				args.add(standardId.toString());
//			}
//
//			String courseIdFilter = "";
//			if (courseId != null) {
//				courseIdFilter = COURSE_CLAUSE;
//				args.add(courseId.toString());
//			}
//
//			return StandardHomeworkSubmissionDetailsRowMapper
//					.getHomeworkSubmissionDetailsByStandard(jdbcTemplate.query(String.format(query, standardIdFilter, courseIdFilter),
//							args.toArray(), STANDRAD_HOMEWORK_SUBMISSION_DETAILS_ROW_MAPPER));
//
//		} catch (final Exception e) {
//			logger.error(
//					"Exception while getting homework submission details for instituteId {}, academicSessionId {}, standardId {}",
//					instituteId, academicSessionId, standardId, e);
//		}
//		return null;
//	}

	public StudentHomeworkDetails getStudentHomeworkDetails(int instituteId, UUID studentId, UUID courseId, Integer limit, Integer offSet) {
		try {
			String query = GET_STUDENT_HOMEWORK_DETAILS + " %s ";
			
			final List<Object> args = new ArrayList<>();
			args.add(studentId.toString());
			args.add(studentId.toString());
			args.add(studentId.toString());
			args.add(instituteId);
			args.add(studentId.toString());
			
			String courseIdFilter = "";
			if (courseId != null) {
				courseIdFilter = HOMEWORK_COURSE_CLAUSE;
				args.add(courseId.toString());
			}
			if (limit != null && offSet != null) {
				query += " LIMIT ? OFFSET ?";
				args.add(limit);   
				args.add(offSet);  
			}
	

			return StudentHomeworkDetailsRowMapper.getStudentHomeworkDetails(
					jdbcTemplate.query(String.format(query, courseIdFilter), args.toArray(), STUDENT_HOMEWORK_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while getting lecture details for instituteId {}, lectureId {}", instituteId,
					studentId, e);
		}
		return null;
	}

	public List<HomeworkSubmissionDetails> getHomeworkSubmissionDetailsByHomeworkId(int instituteId, int academicSessionId,
			UUID homeworkId) {
		try {
			final Object[] args = { academicSessionId, homeworkId.toString() };
			return HomeworkSubmissionDetailsRowMapper.getHomeworkSubmissionDetailsByHomeworkId(
					jdbcTemplate.query(GET_HOMEWORK_SUBMISSION_BY_HOMEWORK_ID, args, HOMEWORK_SUBMISSION_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while getting homework submission details for instituteId {}, homeworkId {}", instituteId,
					homeworkId, e);
		}
		return null;
	}

	public List<StudentHomeworkViewSubmissionDetails> getStudentHomeworkViewSubmissionDetailsList(int instituteId, int academicSessionId,
																						UUID homeworkId,UUID standardId) {
		try {
			final Object[] args = {  academicSessionId, standardId.toString(), homeworkId.toString(), instituteId };
			return jdbcTemplate.query(GET_STUDENT_HOMEWORK_VIEW_DETAILS, args, STUDENT_HOMEWORK_VIEW_SUBMISSION_DETAILS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting all homework details for instituteId {}, homeworkId {}", instituteId,
					homeworkId, e);
		}
		return null;
	}

	public boolean updateHomeworkStatus(int instituteId, UUID userId, List<UUID> homeworkList,
			HomeworkStatus homeworkStatus) {
		try {

			final StringBuilder inQuery = new StringBuilder();
			final List<Object> args = new ArrayList<>();
			args.add(homeworkStatus.name());
			args.add(userId.toString());
			args.add(new Timestamp(System.currentTimeMillis()));
			args.add(userId.toString());
			args.add(instituteId);

			inQuery.append("(");
			boolean first = true;
			for (final UUID homeworkId : homeworkList) {
				args.add(homeworkId.toString());
				if (first) {
					inQuery.append("?");
					first = false;
					continue;
				}
				inQuery.append(", ?");
			}
			inQuery.append(")");

			return jdbcTemplate.update(String.format(UPDATE_HOMEWORK_STATUS, inQuery.toString()), args.toArray()) >= 1;
		} catch (final Exception e) {
			logger.error("Exception while updating homework status for institute {} and homeworkStatus {}", instituteId,
					homeworkStatus, e);
		}
		return false;
	}

	public List<User> getHomeworkUsers(int instituteId, HashSet<UUID> homeworkIds, Set<Integer> sectionIdSet) {
		try {
			List<Object> args = new ArrayList<>();

			StringBuilder sectionInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(sectionIdSet)) {
				sectionInQuery.append(SECTION_IDS);
				sectionInQuery.append(" (");
				String sectionDelimiter = "";
				for (Integer sectionId : sectionIdSet) {
					if(sectionId == null) {
						continue;
					}
					args.add(sectionId);
					sectionInQuery.append(sectionDelimiter + " ?");
					sectionDelimiter = ",";
				}
				sectionInQuery.append(") ");
			}

			args.add(instituteId);

			StringBuilder homeworkInQuery = new StringBuilder();
			homeworkInQuery.append(" (");
			String homeworkDelimiter = "";
			for (UUID homeworkId : homeworkIds) {
				args.add(homeworkId.toString());
				homeworkInQuery.append(homeworkDelimiter + " ?");
				homeworkDelimiter = ",";
			}
			homeworkInQuery.append(") ");

			return jdbcTemplate.query(String.format(GET_HOMEWORK_USERS + LIST_HOMEWORK_ID, sectionInQuery.toString(),
							homeworkInQuery.toString()), args.toArray(), USER_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting users for instituteId {}, lectureIds {}", instituteId, homeworkIds, e);
		}
		return null;
	}
	
	public List<User> getHomeworkUsers(int instituteId, UUID homeworkId, Set<Integer> sectionIdSet) {
		try {
			final List<Object> args = new ArrayList<>();

			StringBuilder sectionInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(sectionIdSet)) {
				sectionInQuery.append(SECTION_IDS);
				sectionInQuery.append(" (");
				String sectionDelimiter = "";
				for (Integer sectionId : sectionIdSet) {
					if(sectionId == null) {
						continue;
					}
					args.add(sectionId);
					sectionInQuery.append(sectionDelimiter + " ?");
					sectionDelimiter = ",";
				}
				sectionInQuery.append(") ");
			}

			args.add(instituteId);
			args.add(homeworkId.toString());

			return jdbcTemplate.query(String.format(GET_HOMEWORK_USERS + SINGLE_HOMEWORK_ID, sectionInQuery.toString()),
					args.toArray(), USER_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting users for instituteId {}, homeworkId {}", instituteId, homeworkId, e);
		}
		return null;
	}

	public List<HomeworkDetails> getHomeworkDetailList(int instituteId, Set<UUID> homeworkIdSet) {
		try {
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			StringBuilder homeworkInQuery = new StringBuilder();
			homeworkInQuery.append(" (");
			String homeworkDelimiter = "";
			for (UUID homeworkId : homeworkIdSet) {
				args.add(homeworkId.toString());
				homeworkInQuery.append(homeworkDelimiter + " ?");
				homeworkDelimiter = ",";
			}
			homeworkInQuery.append(") ");

			return HomeworkDetailsRowMapper.getHomeworkResponseList(
					jdbcTemplate.query(String.format(GET_HOMEWORK_BY_IDS, homeworkInQuery.toString()),
							args.toArray(), HOMEWORK_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while fetching homework details for homework id {}", homeworkIdSet, e);
		}
		return null;
	}

	public StudentHomeworkAndSubmissionDetails getStudentHomeworkDetailsByHomeworkId(int instituteId, UUID studentId,
			UUID homeworkId) {
		try {
			final Object[] args = { studentId.toString(), studentId.toString(), studentId.toString(), instituteId, studentId.toString(), homeworkId.toString() };

			return StudentHomeworkDetailsRowMapper.getStudentHomeworkDetailsByHomeworkId(
					jdbcTemplate.query(GET_STUDENT_HOMEWORK_DETAILS + HOMEWORK_ID_CLAUSE + ORDER_BY_LIMIT_SUBMISSION_TIMESTAMP, args, STUDENT_HOMEWORK_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while getting student submission details for instituteId {}, studentId {}", instituteId,
					studentId, e);
		}
		return null;
	}

	public boolean updateFacultyHomeworkMarkingPayload(int instituteId, FacultyHomeworkMarkingPayload
			facultyHomeworkMarkingPayload) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(facultyHomeworkMarkingPayload.getHomeworkSubmissionStatus().name());
			args.add(facultyHomeworkMarkingPayload.getFacultyId().toString());
			args.add(new Timestamp(System.currentTimeMillis()));
			args.add(facultyHomeworkMarkingPayload.getResult());
			args.add(facultyHomeworkMarkingPayload.getRemarks());

			args.add(facultyHomeworkMarkingPayload.getHomeworkSubmissionId().toString());
			args.add(facultyHomeworkMarkingPayload.getHomeworkId().toString());
			args.add(facultyHomeworkMarkingPayload.getStudentId().toString());

			return jdbcTemplate.update(UPDATE_FACULTY_HOMEWORK_SUBMISSION_STATUS, args.toArray()) >= 1;
		} catch (final Exception e) {
			logger.error("Exception while updating homework status for institute {}", instituteId, e);
		}
		return false;
	}

	public List<HomeworkDetails> getInstituteHomeworkDetails(int instituteId, int academicSessionId) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(academicSessionId);
			return HomeworkDetailsRowMapper.getHomeworkResponseList(
							jdbcTemplate.query(GET_ALL_INSTITUTE_HOMEWORK, args.toArray(), HOMEWORK_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while getting homework details for instituteId {}", instituteId, e);
		}
		return null;
	}

	public List<HomeworkDetails> getInstituteHomeworkDetails(int instituteId, int academicSessionId,
															 int limit, int offset, HomeworkStatus homeworkStatus,
															 UUID filterStaffId) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(homeworkStatus.name());
			args.add(instituteId);
			args.add(academicSessionId);
			String filterStaffIdQuery = "";
			if(filterStaffId != null) {
				filterStaffIdQuery = " and homework_details.faculty_user_id = ? ";
				args.add(filterStaffId.toString());
			}
			args.add(limit);
			args.add(offset);
			args.add(academicSessionId);
			return HomeworkDetailsRowMapper.getHomeworkResponseList(
					jdbcTemplate.query(String.format(GET_ALL_INSTITUTE_HOMEWORK_LIMIT_OFFSET_BY_STATUS, filterStaffIdQuery), args.toArray(),
							HOMEWORK_DETAILS_INNER_QUERY_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while getting homework details for instituteId {}", instituteId, e);
		}
		return null;
	}
}
