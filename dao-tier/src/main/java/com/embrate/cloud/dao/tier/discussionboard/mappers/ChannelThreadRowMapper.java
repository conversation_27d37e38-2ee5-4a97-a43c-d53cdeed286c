package com.embrate.cloud.dao.tier.discussionboard.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import com.embrate.cloud.core.api.discussionboard.ChannelDetails;
import com.embrate.cloud.core.api.discussionboard.ChannelDetailsPayload;
import com.embrate.cloud.core.api.discussionboard.ChannelMetaData;
import com.embrate.cloud.core.api.discussionboard.ChannelThreadRow;
import com.embrate.cloud.core.api.discussionboard.DiscussionBoardEntity;
import com.embrate.cloud.core.api.discussionboard.DiscussionBoardUserDetails;
import com.embrate.cloud.core.api.discussionboard.ThreadDetailsPayload;
import com.embrate.cloud.core.api.discussionboard.ThreadMetaData;
import com.embrate.cloud.core.api.homework.HomeworkDetails;
import com.embrate.cloud.core.api.homework.HomeworkPayload;
import com.embrate.cloud.core.api.lecture.LectureDetailsPayload;
import com.embrate.cloud.dao.tier.homework.mappers.HomeworkDetailsPayloadRowMapper;
import com.embrate.cloud.dao.tier.homework.mappers.HomeworkDetailsRowMapper;
import com.embrate.cloud.dao.tier.lecture.mappers.LectureDetailsPayloadRowMapper;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.dao.tier.user.mappers.UserRowMapper;

public class ChannelThreadRowMapper implements RowMapper<ChannelThreadRow> {

	private static final UserRowMapper USER_ROW_MAPPER = new UserRowMapper();
	private static final ThreadDetailsPayloadRowMapper THREAD_DETAILS_PAYLOAD_ROW_MAPPER = new ThreadDetailsPayloadRowMapper();
	private static final ChannelDetailsPayloadRowMapper CHANNEL_DETAILS_PAYLOAD_ROW_MAPPER = new ChannelDetailsPayloadRowMapper();
	private static final LectureDetailsPayloadRowMapper LECTURE_DETAILS_PAYLOAD_ROW_MAPPER = new LectureDetailsPayloadRowMapper();
	private static final HomeworkDetailsPayloadRowMapper HOMEWORK_DETAILS_PAYLOAD_ROW_MAPPER = new HomeworkDetailsPayloadRowMapper();
	
	@Override
	public ChannelThreadRow mapRow(ResultSet rs, int rowNum) throws SQLException {

		ThreadDetailsPayload threadDetailsPayload = THREAD_DETAILS_PAYLOAD_ROW_MAPPER.mapRow(rs, rowNum);
		ChannelDetailsPayload channelDetailsPayload = CHANNEL_DETAILS_PAYLOAD_ROW_MAPPER.mapRow(rs, rowNum);
		
		if(channelDetailsPayload.getDiscussionBoardEntity() == DiscussionBoardEntity.ONLINE_LECTURE) {
			LectureDetailsPayload lectureDetailsPayload = LECTURE_DETAILS_PAYLOAD_ROW_MAPPER.mapRow(rs, rowNum);
			channelDetailsPayload.setChannelName(lectureDetailsPayload.getTitle());
			channelDetailsPayload.setDescription(lectureDetailsPayload.getDescription());
		}
		else if (channelDetailsPayload.getDiscussionBoardEntity() == DiscussionBoardEntity.HOMEWORK) {
			HomeworkPayload homeworkPayload = HOMEWORK_DETAILS_PAYLOAD_ROW_MAPPER.mapRow(rs, rowNum);
			channelDetailsPayload.setChannelName(homeworkPayload.getTitle());
			channelDetailsPayload.setDescription(homeworkPayload.getDescription());
		}

		User user = USER_ROW_MAPPER.mapRow(rs, rowNum);

		return new ChannelThreadRow(channelDetailsPayload, threadDetailsPayload, user);
	}

	/**
	 * Assuming single channel details
	 * 
	 * @param channelThreadRowList
	 * @return
	 */
	public static ChannelDetails getChannelDetails(List<ChannelThreadRow> channelThreadRowList) {
		if (CollectionUtils.isEmpty(channelThreadRowList)) {
			return null;
		}
		int instituteId = channelThreadRowList.get(0).getChannelDetailsPayload().getInstituteId();

		ChannelDetailsPayload channelDetailsPayload = channelThreadRowList.get(0).getChannelDetailsPayload();

		ChannelMetaData channelMetaData = new ChannelMetaData(channelDetailsPayload.getChannelId(),
				channelDetailsPayload.getDiscussionBoardEntity(), channelDetailsPayload.getEntityId(),
				channelDetailsPayload.getChannelName(), channelDetailsPayload.getDescription());

		List<ThreadMetaData> threadMetaDataList = new ArrayList<ThreadMetaData>();

		for (ChannelThreadRow channelThreadRow : channelThreadRowList) {
			if (channelThreadRow.getThreadDetailsPayload() == null) {
				continue;
			}
			ThreadDetailsPayload threadDetailsPayload = channelThreadRow.getThreadDetailsPayload();

			DiscussionBoardUserDetails discussionBoardUserDetails = new DiscussionBoardUserDetails(
					channelThreadRow.getUser().getUuid(), channelThreadRow.getUser().getUserInstituteId(),
					channelThreadRow.getUser().getUserType(), channelThreadRow.getUser().getFullName(),
					channelThreadRow.getUser().getGender());

			threadMetaDataList.add(new ThreadMetaData(threadDetailsPayload.getThreadId(),
					threadDetailsPayload.getName(), threadDetailsPayload.getDescription(),
					threadDetailsPayload.getAttachments(), threadDetailsPayload.getCreatedTimestamp(),
					threadDetailsPayload.getUpdatedTimestamp(), discussionBoardUserDetails));

		}
		return new ChannelDetails(instituteId, channelMetaData, threadMetaDataList);
	}

}
