package com.embrate.cloud.dao.tier.inventory.v2.mappers;

import com.embrate.cloud.core.api.inventory.v2.ProductBatchData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class ProductBatchRowMapper implements <PERSON>Mapper<ProductBatchData> {

    private static final String SKU_ID = "product_batch_details.sku_id";
    private static final String BATCH_ID = "product_batch_details.batch_id";
    private static final String BATCH_NAME = "product_batch_details.batch_name";
    private static final String CREATED_BY = "product_batch_details.created_by";
    private static final String CREATED_AT = "product_batch_details.created_at";
    private static final String TOTAL_QUANTITY = "product_batch_details.total_quantity";
    private static final String INITIAL_QUANTITY = "product_batch_details.initial_quantity";
    private static final String INITIAL_QUANTITY_AS_OF = "product_batch_details.initial_quantity_as_of";
    private static final String SELLING_PRICE = "product_batch_details.selling_price";
    private static final String DISCOUNT = "product_batch_details.discount";
    private static final String DISC_IN_PERCENT = "product_batch_details.disc_in_percent";
    private static final String PURCHASE_PRICE = "product_batch_details.purchase_price";
    private static final String MRP = "product_batch_details.mrp";
    private static final String DESCRIPTION = "product_batch_details.description";
    private static final String IN_USE = "product_batch_details.in_use";
    private static final String DEFAULT_BATCH = "product_batch_details.default_batch";
    private static final String ACTIVE = "product_batch_details.active";

    public ProductBatchData mapRow(ResultSet rs, int rowNum) throws SQLException {

        String skuIdStr = rs.getString(SKU_ID);
        if (StringUtils.isBlank(skuIdStr)) {
            return null;
        }

        Timestamp initialQuantityAsOfTimestamp = rs.getTimestamp(INITIAL_QUANTITY_AS_OF);
        Integer initialQuantityAsOf = null;
        if (initialQuantityAsOfTimestamp != null) {
            initialQuantityAsOf = (int) (initialQuantityAsOfTimestamp.getTime() / 1000l);
        }

        Double sellingPrice = null;
        double sellingPriceValue = rs.getDouble(SELLING_PRICE);
        if (!rs.wasNull()) {
            sellingPrice = sellingPriceValue;
        }

        Double purchasePrice = null;
        double purchasePriceValue = rs.getDouble(PURCHASE_PRICE);
        if (!rs.wasNull()) {
            purchasePrice = purchasePriceValue;
        }

        Double mrp = null;
        double mrpValue = rs.getDouble(MRP);
        if (!rs.wasNull()) {
            mrp = mrpValue;
        }
        return new ProductBatchData(UUID.fromString(rs.getString(BATCH_ID)), rs.getString(BATCH_NAME), rs.getString(DESCRIPTION),
                rs.getDouble(TOTAL_QUANTITY), rs.getDouble(INITIAL_QUANTITY), initialQuantityAsOf, sellingPrice, rs.getDouble(DISCOUNT),
                rs.getBoolean(DISC_IN_PERCENT), purchasePrice, mrp, rs.getBoolean(IN_USE), rs.getBoolean(DEFAULT_BATCH), rs.getBoolean(ACTIVE));

    }

}
