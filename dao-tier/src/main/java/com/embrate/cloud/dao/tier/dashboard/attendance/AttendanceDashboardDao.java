package com.embrate.cloud.dao.tier.dashboard.attendance;

import com.embrate.cloud.core.api.dashboards.admission.StudentAdmTCCount;
import com.embrate.cloud.core.api.dashboards.admission.StudentCountSummary;
import com.embrate.cloud.core.api.dashboards.attendance.StudentAttendanceCount;
import com.embrate.cloud.dao.tier.dashboard.attendance.mappers.StudentAttendanceCountRowMapper;
import com.embrate.cloud.dao.tier.utils.QueryUtils;
import com.lernen.cloud.core.api.student.StudentSessionSummary;
import com.lernen.cloud.dao.tier.student.mappers.StudentAdmTCCountRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentCountSummaryRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentSessionSummaryRowMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Date;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * DAO class for student session summary queries
 *
 * <AUTHOR>
 */
public class AttendanceDashboardDao {

	private static final Logger logger = LogManager.getLogger(AttendanceDashboardDao.class);

	private static final StudentSessionSummaryRowMapper STUDENT_SESSION_SUMMARY_ROW_MAPPER = new StudentSessionSummaryRowMapper();
	private static final StudentCountSummaryRowMapper STUDENT_COUNT_SUMMARY_ROW_MAPPER = new StudentCountSummaryRowMapper();
	private static final StudentAdmTCCountRowMapper STUDENT_ADM_TC_COUNT_ROW_MAPPER = new StudentAdmTCCountRowMapper();
	private static final StudentAttendanceCountRowMapper STUDENT_ATTENDANCE_COUNT_ROW_MAPPER = new StudentAttendanceCountRowMapper();

	private static final String GET_STUDENT_ATTENDANCE_COUNT_BY_DATE = "select r.institute_id, r.academic_session_id, " +
			" t.name, r.attendance_status, count(student_id) count" +
			" from student_attendance_register r join attendance_types t on r.institute_id = t.institute_id and " +
			" r.academic_session_id = t.academic_session_id and r.attendance_type = t.attendance_type_id " +
			" where r.institute_id in %s and r.academic_session_id in %s " +
			" and attendance_date = ? group by 1, 2, 3, 4";


	private final JdbcTemplate jdbcTemplate;

	public AttendanceDashboardDao(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	public List<StudentAttendanceCount> getStudentAttendanceCountByDate(List<Integer> instituteIds,
																	   Set<Integer> academicSessionIds, int attendanceDate) {
		if (CollectionUtils.isEmpty(instituteIds) || CollectionUtils.isEmpty(academicSessionIds)) {
			logger.warn("Institute IDs or Academic Session IDs are empty");
			return null;
		}

		if (attendanceDate <= 0) {
			logger.warn("Attendance date is invalid");
			return null;
		}

		try {
			List<Object> params = new ArrayList<>();
			String query = String.format(GET_STUDENT_ATTENDANCE_COUNT_BY_DATE, QueryUtils.getListClause(instituteIds).getQueryClause(),
					QueryUtils.getListClause(academicSessionIds).getQueryClause());
			params.addAll(QueryUtils.getListClause(instituteIds).getArgs());
			params.addAll(QueryUtils.getListClause(academicSessionIds).getArgs());
			params.add(attendanceDate);
			logger.debug("Executing parameterized query: {} with {} parameters", query, params.size());
			return jdbcTemplate.query(query, params.toArray(), STUDENT_ATTENDANCE_COUNT_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting student attendance count by date for institutes {} and sessions {}",
					instituteIds, academicSessionIds, e);
		}
		return null;
	}

}
