/**
 *
 */
package com.embrate.cloud.dao.tier.hpc.mapper;

import com.embrate.cloud.core.api.hpc.layout.HPCForm;
import com.google.gson.Gson;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
public class HPCFormRowMapper implements RowMapper<HPCForm> {

	private static final String HPC_STRUCTURE = "form";
	private static final Gson GSON = new Gson();

	@Override
	public HPCForm mapRow(ResultSet rs, int rowNum) throws SQLException {
		return GSON.fromJson(rs.getString(HPC_STRUCTURE),
				HPCForm.class);
	}

}