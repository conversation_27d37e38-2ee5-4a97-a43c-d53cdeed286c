package com.embrate.cloud.dao.tier.dashboard.attendance.mappers;

import com.embrate.cloud.core.api.dashboards.attendance.StudentAttendanceCount;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for StudentAttendanceCount
 * Maps database results from attendance count queries to StudentAttendanceCount POJO
 * 
 * <AUTHOR>
 */
public class StudentAttendanceCountRowMapper implements RowMapper<StudentAttendanceCount> {

    protected static final String INSTITUTE_ID = "institute_id";
    protected static final String ACADEMIC_SESSION_ID = "academic_session_id";
    protected static final String ATTENDANCE_TYPE_NAME = "name";
    protected static final String ATTENDANCE_STATUS = "attendance_status";
    protected static final String COUNT = "count";

    @Override
    public StudentAttendanceCount mapRow(ResultSet rs, int rowNum) throws SQLException {
        int instituteId = rs.getInt(INSTITUTE_ID);
        int academicSessionId = rs.getInt(ACADEMIC_SESSION_ID);
        String attendanceTypeName = rs.getString(ATTENDANCE_TYPE_NAME);
        String attendanceStatus = rs.getString(ATTENDANCE_STATUS);
        long count = rs.getLong(COUNT);

        return new StudentAttendanceCount(instituteId, academicSessionId, attendanceTypeName, 
                                        attendanceStatus, count);
    }
}
