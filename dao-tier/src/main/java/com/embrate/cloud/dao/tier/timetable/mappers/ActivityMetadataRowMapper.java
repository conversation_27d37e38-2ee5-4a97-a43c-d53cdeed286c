package com.embrate.cloud.dao.tier.timetable.mappers;

//import com.embrate.cloud.core.api.timetable.ActivityEntity;
//import com.embrate.cloud.core.api.timetable.ActivityMetadata;
//import com.google.gson.Gson;
//import com.google.gson.reflect.TypeToken;
//import com.lernen.cloud.core.api.institute.Time;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.jdbc.core.RowMapper;
//import java.util.UUID;

import com.embrate.cloud.core.api.timetable.ActivityEntity;
import com.embrate.cloud.core.api.timetable.ActivityMetadata;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

public class ActivityMetadataRowMapper implements RowMapper<ActivityMetadata> {

    public static final String INSTITUTE_ID = "activities_details.institute_id";
    public static final String ACADEMIC_SESSION_ID = "activities_details.academic_session_id";
    public static final String ACTIVITY_ID = "activities_details.activity_id";
    public static final String ACTIVITY_NAME = "activities_details.activity_name";
    public static final String ENTITY_ID = "activities_details.entity_id";
    public static final String ENTITY_NAME = "activities_details.entity_name";

    @Override
    public ActivityMetadata mapRow(ResultSet rs, int rowNum) throws SQLException {
        if(rs.getString(ACTIVITY_ID) == null)  {
            return null;
        }
        return new ActivityMetadata(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
                UUID.fromString(rs.getString(ACTIVITY_ID)),
                rs.getString(ACTIVITY_NAME), rs.getString(ENTITY_ID),
                ActivityEntity.valueOf(rs.getString(ENTITY_NAME)));
    }
}
