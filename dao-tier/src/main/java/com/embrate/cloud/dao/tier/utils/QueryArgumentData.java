package com.embrate.cloud.dao.tier.utils;

import java.util.List;

/**
 * <AUTHOR>
 */

public class QueryArgumentData {

    private final String queryClause;

    private List<Object> args;

    public QueryArgumentData(String queryClause, List<Object> args) {
        this.queryClause = queryClause;
        this.args = args;
    }

    public String getQueryClause() {
        return queryClause;
    }

    public List<Object> getArgs() {
        return args;
    }

    @Override
    public String toString() {
        return "QueryArgumentData{" +
                "queryClause='" + queryClause + '\'' +
                ", args=" + args +
                '}';
    }
}
