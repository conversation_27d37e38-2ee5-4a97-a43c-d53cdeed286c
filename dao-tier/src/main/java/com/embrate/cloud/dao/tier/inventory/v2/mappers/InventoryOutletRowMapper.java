package com.embrate.cloud.dao.tier.inventory.v2.mappers;

import com.embrate.cloud.core.api.inventory.v2.outlet.InventoryOutlet;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Set;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

public class InventoryOutletRowMapper implements RowMapper<InventoryOutlet> {

    private static final String OUTLET_ID = "outlet_id";
    private static final String NAME = "name";
    private static final String ORGANISATION_ID = "organisation_id";
    private static final String INSTITUTE_SCOPE = "institute_scope";
    private static final String DESCRIPTION = "description";

    public InventoryOutlet mapRow(ResultSet rs, int rowNum) throws SQLException {
        String organisationStr = rs.getString(ORGANISATION_ID);
        UUID organisationId = null;
        if (StringUtils.isNotBlank(organisationStr)) {
            organisationId = UUID.fromString(organisationStr);
        }

        final String instituteScopeJson = rs.getString(INSTITUTE_SCOPE);
        final Set<Integer> instituteScope = StringUtils.isBlank(instituteScopeJson) ? null
                : GSON.fromJson(instituteScopeJson, new TypeToken<Set<Integer>>() {
        }.getType());


        return new InventoryOutlet(UUID.fromString(rs.getString(OUTLET_ID)), rs.getString(NAME), organisationId, instituteScope, rs.getString(DESCRIPTION));
    }
}