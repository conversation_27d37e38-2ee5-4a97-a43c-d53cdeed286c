package com.embrate.cloud.dao.tier.service.payment.gateway;

import com.embrate.cloud.core.api.service.payment.gateway.PGTransactionType;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayServiceProvider;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayTransactionData;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayTransactionStatus;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantDetails;
import com.embrate.cloud.dao.tier.service.payment.gateway.mappers.PaymentGatewayMerchantDetailsRowMapper;
import com.embrate.cloud.dao.tier.service.payment.gateway.mappers.PaymentGatewayTransactionsRowMapper;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

/**
 * <AUTHOR>
 */

public class PaymentGatewayTransactionsDao {

    private static final Logger logger = LogManager.getLogger(PaymentGatewayTransactionsDao.class);
    private static final PaymentGatewayTransactionsRowMapper PAYMENT_GATEWAY_TRANSACTIONS_ROW_MAPPER = new PaymentGatewayTransactionsRowMapper();
    private static final String GET_TRANSACTION = "select * from payment_gateway_transactions where transaction_id = ? %s";
    private static final String GET_TRANSACTION_BY_TOKEN = "select * from payment_gateway_transactions where token = ? %s";

    private static final String GET_USER_TRANSACTIONS = "select * from payment_gateway_transactions where user_id = ? and institute_id = ? %s " +
            "order by added_at desc";

    private static final String ADD_TRANSACTION = "insert into payment_gateway_transactions(transaction_id, institute_id, payment_gateway, merchant_id, " +
            "user_id, user_type, transaction_type, stage, txn_amount, txn_currency, wallet_amount, instant_discount, fine_amount, status, token, metadata, description) " +
            "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

    private static final String UPDATE_TRANSACTION = "update payment_gateway_transactions set status = ?, error_code = ?, error_reason = ?, metadata = ?, lock_acquired = ? where transaction_id = ?";
    private static final String UPDATE_TRANSACTION_LOCK = "update payment_gateway_transactions set lock_acquired = ? where transaction_id = ?";

    private final JdbcTemplate jdbcTemplate;

    public PaymentGatewayTransactionsDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public PaymentGatewayTransactionData getTransaction(UUID transactionId, DBLockMode dbLockMode){
        if (dbLockMode == null) {
            dbLockMode = DBLockMode.NONE;
        }

        try{
            return jdbcTemplate.queryForObject(String.format(GET_TRANSACTION, dbLockMode.getCommand()), new Object[]{transactionId.toString()}, PAYMENT_GATEWAY_TRANSACTIONS_ROW_MAPPER);
        }catch (Exception e){
            logger.error("Error while getting transaction for id {}", transactionId, e);
        }
        return null;
    }

    public PaymentGatewayTransactionData getTransactionByToken(String token, DBLockMode dbLockMode){
        if (dbLockMode == null) {
            dbLockMode = DBLockMode.NONE;
        }

        try{
            return jdbcTemplate.queryForObject(String.format(GET_TRANSACTION_BY_TOKEN, dbLockMode.getCommand()), new Object[]{token}, PAYMENT_GATEWAY_TRANSACTIONS_ROW_MAPPER);
        }catch (Exception e){
            logger.error("Error while getting token for id {}", token, e);
        }
        return null;
    }

    public List<PaymentGatewayTransactionData> getTransactions(int instituteId, UUID userId, PGTransactionType transactionType, boolean includeCancelledTransaction){
        List<Object> args = new ArrayList<>();
        args.add(userId.toString());
        args.add(instituteId);
        String transactionTypeFilter = "";
        if (transactionType != null) {
            transactionTypeFilter = " and transaction_type = ? ";
            args.add(transactionType.name());
        }
        if(!includeCancelledTransaction){
            transactionTypeFilter += " and status != 'CANCELLED' ";
        }

        try{
            return jdbcTemplate.query(String.format(GET_USER_TRANSACTIONS, transactionTypeFilter), args.toArray(), PAYMENT_GATEWAY_TRANSACTIONS_ROW_MAPPER);
        }catch (Exception e){
            logger.error("Error while getting transactions for instituteId {}, user {}, transactionType {}", instituteId, userId, transactionType, e);
        }
        return null;
    }

    public boolean addTransaction(PaymentGatewayTransactionData paymentGatewayTransactionData){
        try{
            int row = jdbcTemplate.update(ADD_TRANSACTION, paymentGatewayTransactionData.getTransactionId().toString(), paymentGatewayTransactionData.getInstituteId(), paymentGatewayTransactionData.getServiceProvider().name(), paymentGatewayTransactionData.getMerchantId().toString(),
            paymentGatewayTransactionData.getUserId().toString(), paymentGatewayTransactionData.getUserType().name(), paymentGatewayTransactionData.getTransactionType().name(),
                    paymentGatewayTransactionData.getPaymentGatewayStage().name(), paymentGatewayTransactionData.getPaymentGatewayAmount(),
                    paymentGatewayTransactionData.getCurrency().name(), paymentGatewayTransactionData.getWalletAmount(), paymentGatewayTransactionData.getInstantDiscountAmount(),
                    paymentGatewayTransactionData.getFineAmount(), paymentGatewayTransactionData.getTransactionStatus().name(),
                    paymentGatewayTransactionData.getTransactionToken(), paymentGatewayTransactionData.getMetadata() == null ? null : SharedConstants.GSON.toJson(paymentGatewayTransactionData.getMetadata()), paymentGatewayTransactionData.getDescription());
            return row == 1;
        }catch (Exception e){
            logger.error("Error while adding transaction {}", paymentGatewayTransactionData, e);
        }
        return false;
    }

    /**
     * Since we are directly updating the metadata in this method. Pls ensure that this method is read in transaction where old values are being read and
     * written back in this metadata map. If this is not ensured, it will lead to data loss
     *
     * @param transactionId
     * @param status
     * @param errorCode
     * @param errorMessage
     * @param metadata
     * @return
     */
    public boolean updateTransaction(UUID transactionId, PaymentGatewayTransactionStatus status, int errorCode, String errorMessage, Map<String, Object> metadata){
        try{
            return jdbcTemplate.update(UPDATE_TRANSACTION, status.name(), errorCode, errorMessage, metadata == null ? null : SharedConstants.GSON.toJson(metadata), false, transactionId.toString()) == 1;
        }catch (Exception e){
            logger.error("Error while updating transaction {}, for status {}, errorCode {}, errorMessage {}, metadata {}", transactionId, status, errorCode, errorMessage, metadata, e);
        }
        return false;
    }

    public boolean updateTransactionLock(UUID transactionId, boolean lockAcquired){
        try{
            return jdbcTemplate.update(UPDATE_TRANSACTION_LOCK, lockAcquired, transactionId.toString()) == 1;
        }catch (Exception e){
            logger.error("Error while updating transaction lock {}, for status", transactionId, lockAcquired, e);
        }
        return false;
    }

}
