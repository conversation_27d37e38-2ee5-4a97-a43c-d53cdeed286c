package com.lernen.cloud.dao.tier.fees.discount.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.fees.DiscountAmountFeeHead;

public class DiscountAmountFeeHeadRowMapper implements RowMapper<DiscountAmountFeeHead> {

	private static final String FEE_HEAD_ID = "fee_head_id";
	private static final String IS_PERCENT = "is_percent";
	private static final String AMOUNT = "amount";

	@Override
	public DiscountAmountFeeHead mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new DiscountAmountFeeHead(rs.getInt(FEE_HEAD_ID), rs.getBoolean(IS_PERCENT), rs.getDouble(AMOUNT));
	}
}
