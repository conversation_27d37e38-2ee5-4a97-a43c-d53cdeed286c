/**
 * 
 */
package com.lernen.cloud.dao.tier.user.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.user.ResetPasswordEmailPayload;
import com.lernen.cloud.core.api.user.ResetPasswordRequestStatus;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.utils.EmailUtils;

/**
 * <AUTHOR>
 *
 */
public class ResetPasswordEmailRowMapper implements RowMapper<ResetPasswordEmailPayload<Void>> {

	private static final UserRowMapper USER_ROW_MAPPER = new UserRowMapper();
	
	private static final String INSTITUTE_ID = "reset_password_details.institute_id";
	private static final String REQUEST_ID = "reset_password_details.request_id";
	private static final String DELIVERY_MODE = "reset_password_details.delivery_mode";
	private static final String TOKEN_ID = "reset_password_details.token_id";
	private static final String EXPIRY_TIME = "reset_password_details.expiry_time";
	private static final String STATUS = "reset_password_details.status";
	
	@Override
	public ResetPasswordEmailPayload<Void> mapRow(ResultSet rs, int rowNum) throws SQLException {
		User user = USER_ROW_MAPPER.mapRow(rs, rowNum);
		if(user == null) {
			return null;
		}
		final Timestamp expiryDate = rs.getTimestamp(EXPIRY_TIME);
		final Integer expiryDateTime = expiryDate == null ? null
				: (int) (expiryDate.getTime() / 1000l);
		
		return new ResetPasswordEmailPayload<Void>(rs.getInt(INSTITUTE_ID),
				UUID.fromString(rs.getString(REQUEST_ID)),
				DeliveryMode.getDeliveryMode(rs.getString(DELIVERY_MODE)),
				UUID.fromString(rs.getString(TOKEN_ID)), user, null, 
				EmailUtils.maskEmailId(user.getEmail()), expiryDateTime,
				ResetPasswordRequestStatus.getResetPasswordRequestStatus(rs.getString(STATUS)));
	}

	public static ResetPasswordEmailPayload<Void> getResetPasswordEmailPayloadObject(
			List<ResetPasswordEmailPayload<Void>> resetPasswordEmailPayloadList) {
		if(CollectionUtils.isEmpty(resetPasswordEmailPayloadList)) {
			return null;
		}
		ResetPasswordEmailPayload<Void> resetPasswordEmailPayload = resetPasswordEmailPayloadList.get(0);
		for(ResetPasswordEmailPayload<Void> resetPasswordEmailPayloadRow : resetPasswordEmailPayloadList) {
			if(resetPasswordEmailPayloadRow.getExpiryTimestamp() > resetPasswordEmailPayload.getExpiryTimestamp()) {
				resetPasswordEmailPayload = resetPasswordEmailPayloadRow;
			}
		}
		return resetPasswordEmailPayload;
	}
}