package com.lernen.cloud.dao.tier.attendance;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.lernen.cloud.core.api.attendance.*;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.dao.tier.attendance.mappers.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import com.lernen.cloud.core.api.user.Module;

import java.sql.Timestamp;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */
public class AttendanceDao {
	private static final Logger logger = LogManager.getLogger(AttendanceDao.class);

	private static final HolidayCalendarRowMapper HOLIDAY_CALENDAR_ROW_MAPPER = new HolidayCalendarRowMapper();
	private static final StudentAttendanceRowMapper STUDENT_ATTENDANCE_ROW_MAPPER = new StudentAttendanceRowMapper();
	private static final StudentAttendanceRawRecordRowMapper STUDENT_ATTENDANCE_RAW_RECORD_ROW_MAPPER = new StudentAttendanceRawRecordRowMapper();
	private static final AttendanceTypeRowMapper ATTENDANCE_TYPE_ROW_MAPPER = new AttendanceTypeRowMapper();

	private static final AttendanceTypeStandardDetailsRowMapper ATTENDANCE_TYPE_STANDARD_DETAILS_ROW_MAPPER = new AttendanceTypeStandardDetailsRowMapper();
	private static final StudentAttendanceWithSessionDetailsRowMapper STUDENT_ATTENDANCE_WITH_SESSION_ROW_MAPPER = new StudentAttendanceWithSessionDetailsRowMapper();
	private static final StudentAttendanceRegisterPayloadRowMapper STUDENT_ATTENDANCE_REGISTER_PAYLOAD_ROW_MAPPER = new StudentAttendanceRegisterPayloadRowMapper();
	

	private static final String ADD_HOLIDAYS = "insert into holiday_calendar(institute_id, academic_session_id, holiday_type, entity_id, entity_name, holiday_date, description) "
			+ "values(?, ?, ?, ?, ?, ?, ?)";

	private static final String GET_HOLIDAY_LIST = "select * from holiday_calendar where institute_id = ? and academic_session_id = ?";

	private static final String ADD_STUDENT_ATTENDANCE = "insert into student_attendance_register(institute_id, academic_session_id, attendance_date, attendance_type, student_id, attendance_status, remarks, created_by, updated_by)"
			+ "values(?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String DELETE_STUDENT_ATTENDANCE = "delete from student_attendance_register where institute_id = ? and "
			+ "academic_session_id = ? and student_id = ? and attendance_type = ? and attendance_date = ?";

	private final String GET_STUDENT_ATTENDANCE = "select * from student_attendance_register where institute_id = ? and " +
			" academic_session_id = ? and student_id = ? and attendance_date = ?";

	private final String GET_MULTIPLE_STUDENT_ATTENDANCE = "select * from student_attendance_register where institute_id = ? and " +
			" academic_session_id = ? and attendance_date = ? and student_id in %s";

	private final String GET_INSTITUTE_ATTENDANCE = "select * from student_attendance_register where institute_id = ? and " +
			" academic_session_id = ? and attendance_date = ?";

	private final String GET_STUDENT_ATTENDANCE_DETAILS = "select students.*, register.* from students join student_academic_session_details on "
			+ "students.student_id = student_academic_session_details.student_id left join (select student_attendance_register.*, "
			+ "attendance_types.attendance_type_id, attendance_types.name, attendance_types.description from student_attendance_register "
			+ "join attendance_types on student_attendance_register.institute_id = attendance_types.institute_id and "
			+ "student_attendance_register.academic_session_id = attendance_types.academic_session_id and "
			+ "student_attendance_register.attendance_type = attendance_types.attendance_type_id where attendance_types.attendance_type_id = ? "
			+ "and student_attendance_register.attendance_date = ? ) as register on register.academic_session_id = "
			+ "student_academic_session_details.academic_session_id and register.student_id = student_academic_session_details.student_id "
			+ "where student_academic_session_details.session_status = 'ENROLLED' and students.institute_id = ? and "
			+ "student_academic_session_details.academic_session_id = ? and student_academic_session_details.standard_id = ? %s";
	
	private final String GET_STUDENT_ATTENDANCE_MULTIPLE_TYPE = "select * from students join student_academic_session_details on "
			+ "students.student_id = student_academic_session_details.student_id "
			+ " join academic_session on academic_session.academic_session_id = student_academic_session_details.academic_session_id "
			+ " join standards on standards.standard_id = student_academic_session_details.standard_id "
			+ " left join standard_section_mapping on standards.standard_id = standard_section_mapping.standard_id and student_academic_session_details.section_id = standard_section_mapping.section_id"
			+ " left join (select student_attendance_register.*, "
			+ "attendance_types.attendance_type_id, attendance_types.name, attendance_types.description from student_attendance_register "
			+ "join attendance_types on student_attendance_register.institute_id = attendance_types.institute_id and "
			+ "student_attendance_register.academic_session_id = attendance_types.academic_session_id and "
			+ "student_attendance_register.attendance_type = attendance_types.attendance_type_id where attendance_types.attendance_type_id in %s "
			+ "and student_attendance_register.attendance_date = ? ) as register on register.academic_session_id = "
			+ "student_academic_session_details.academic_session_id and register.student_id = student_academic_session_details.student_id "
			+ "where student_academic_session_details.session_status = 'ENROLLED' and students.institute_id = ? and "
			+ "student_academic_session_details.academic_session_id = ? and student_academic_session_details.standard_id = ? %s";

	private final String GET_STUDENT_ATTENDANCE_DETAILS_WITH_SESSION = "select * from students join student_academic_session_details on "
			+ " students.student_id = student_academic_session_details.student_id "
			+ " join academic_session on academic_session.academic_session_id = student_academic_session_details.academic_session_id "
			+ " join standards on standards.standard_id = student_academic_session_details.standard_id "
			+ " left join standard_section_mapping on standards.standard_id = standard_section_mapping.standard_id and student_academic_session_details.section_id = standard_section_mapping.section_id"
			+ " left join (select student_attendance_register.*, "
			+ "attendance_types.attendance_type_id, attendance_types.name, attendance_types.description from student_attendance_register "
			+ "join attendance_types on student_attendance_register.institute_id = attendance_types.institute_id and "
			+ "student_attendance_register.academic_session_id = attendance_types.academic_session_id and "
			+ "student_attendance_register.attendance_type = attendance_types.attendance_type_id where attendance_types.attendance_type_id = ? "
			+ "and student_attendance_register.attendance_date = ? ) as register on register.academic_session_id = "
			+ "student_academic_session_details.academic_session_id and register.student_id = student_academic_session_details.student_id "
			+ "where student_academic_session_details.session_status = 'ENROLLED' and students.institute_id = ? and "
			+ "student_academic_session_details.academic_session_id = ? and student_academic_session_details.standard_id = ? %s ";
	
//	private final String GET_STUDENT_ATTENDANCE_DETAILS_REPORT = "select * from students join student_academic_session_details on "
//			+ " students.student_id = student_academic_session_details.student_id "
//			+ " join academic_session on academic_session.academic_session_id = student_academic_session_details.academic_session_id "
//			+ " join standards on standards.standard_id = student_academic_session_details.standard_id "
//			+ " left join standard_section_mapping on standards.standard_id = standard_section_mapping.standard_id and student_academic_session_details.section_id = standard_section_mapping.section_id"
//			+ " left join (select student_attendance_register.*, "
//			+ "attendance_types.attendance_type_id, attendance_types.name, attendance_types.description from student_attendance_register "
//			+ "join attendance_types on student_attendance_register.institute_id = attendance_types.institute_id and "
//			+ "student_attendance_register.academic_session_id = attendance_types.academic_session_id and "
//			+ "student_attendance_register.attendance_type = attendance_types.attendance_type_id where attendance_types.attendance_type_id = ? "
//			+ "and student_attendance_register.attendance_date = ? ) as register on register.academic_session_id = "
//			+ "student_academic_session_details.academic_session_id and register.student_id = student_academic_session_details.student_id "
//			+ "where student_academic_session_details.session_status = 'ENROLLED' and students.institute_id = ? and "
//			+ "student_academic_session_details.academic_session_id = ? and student_academic_session_details.standard_id = ? %s ";

	private final String GET_STUDENT_ATTENDANCE_DETAILS_REPORT = "select * from students " 
			+ " join student_academic_session_details on  students.student_id = student_academic_session_details.student_id " 
			+ " join academic_session on academic_session.academic_session_id = student_academic_session_details.academic_session_id " 
			+ " join standards on standards.standard_id = student_academic_session_details.standard_id " 
			+ " left join standard_section_mapping on standards.standard_id = standard_section_mapping.standard_id "
			+ " and student_academic_session_details.section_id = standard_section_mapping.section_id " 
	
			+ " left join (select student_attendance_register.*, attendance_types.attendance_type_id, "
			+ " attendance_types.name, attendance_types.description from student_attendance_register "
			+ " join attendance_types on student_attendance_register.institute_id = attendance_types.institute_id "
			+ " and student_attendance_register.academic_session_id = attendance_types.academic_session_id "
			+ " and student_attendance_register.attendance_type = attendance_types.attendance_type_id "
			+ " where %s %s %s %s %s ) "
			+ " as register on register.academic_session_id = student_academic_session_details.academic_session_id "
			+ " and register.student_id = student_academic_session_details.student_id " 
			
			+ " where student_academic_session_details.session_status = 'ENROLLED' "
			+ " and students.institute_id = ? " 
			+ " and student_academic_session_details.academic_session_id = ? " 
			+ " %s %s ";

	private final String ATTENDANCE_HEAD_TYPE_FILTER = " and attendance_types.attendance_head_type in  ";
	
	private static final String ATTENDACE_TYPE_FILTER = " and attendance_types.attendance_type_id in ";
	
	private static final String ATTENDACE_STATUS_FILTER = " and student_attendance_register.attendance_status in ";
	
	private static final String STANDARD_FILTER =  " and student_academic_session_details.standard_id in  ";
	
	private static final String START_DATE_FILTER =  " student_attendance_register.attendance_date >= ? ";
	
	private static final String END_DATE_FILTER = " and student_attendance_register.attendance_date <= ? ";
	
	private static final String GET_INSTITUTE_ATTENDANCE_TYPES = "select attendance_types.*, standard_attendance_type.* from attendance_types "
			+ " left join standard_attendance_type on attendance_types.attendance_type_id = standard_attendance_type.attendance_type_id " +
			" where attendance_types.institute_id = ? and academic_session_id = ? %s";
	
	private static final String GET_ATTENDANCE_REGISTER_BY_STUDENT_AND_SESSION = " select student_attendance_register.* " +
			" from student_attendance_register " +
			" join attendance_types ON student_attendance_register.attendance_type = attendance_types.attendance_type_id " +
			" where student_attendance_register.institute_id = ? " +
			" and student_attendance_register.academic_session_id = ? ";

	private static final String STUDENT_ID_IN_CLAUSE = " and student_id in %s ";

	private static final String ATTENDANCE_TYPE_ID_IN_CLAUSE = " and attendance_type in %s ";

	private static final String START_DATE_WHERE_CLAUSE = " and attendance_date >= ? ";

	private static final String END_DATE_WHERE_CLAUSE = " and attendance_date <= ? ";

	private static final String ADD_STUDENT_ATTENDANCE_TYPE = "insert into attendance_types(institute_id, academic_session_id, name, description, metadata, attendance_head_type) " +
			"values(?, ?, ?, ?, ?, ?)";

	private static final String ADD_STANDARD_ATTENDANCE_TYPE_MAPPING = "insert into standard_attendance_type(institute_id, attendance_type_id, standard_id, metadata) " +
			"values(?, ?, ?, ?)";

	private static final String GET_STUDENT_ATTENDANCE_TYPE_NAMES = " select * from attendance_types " +
			             " where institute_id = ? and academic_session_id = ? and name = ? ";

	private static final String UPDATE_STUDENT_ATTENDANCE_TYPE = "update attendance_types set " +
			"name = ?, description = ?, metadata = ? where institute_id = ? and academic_session_id = ? and attendance_type_id = ? ";

	private static final String DELETE_STUDENT_ATTENDANCE_TYPE = "delete from attendance_types " +
			"where institute_id = ? and academic_session_id = ? and attendance_type_id = ? ";

	private static final String DELETE_STANDARD_ATTENDANCE_TYPE_MAPPING = "delete from standard_attendance_type " +
			"where institute_id = ? and attendance_type_id = ? ";

	private final JdbcTemplate jdbcTemplate;
	private final TransactionTemplate transactionTemplate;

	public AttendanceDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
	}

	public List<AttendanceType> getInstituteAttendanceTypes(int instituteId, int academicSessionId,
															List<Integer> attendanceType){
		return getInstituteAttendanceTypes(instituteId, academicSessionId, attendanceType, new HashSet<>(Arrays.asList(Module.ATTENDANCE)));
	}

	public List<AttendanceType> getInstituteAttendanceTypes(int instituteId, int academicSessionId,
			List<Integer> attendanceType, Set<Module> moduleSet) {
		try {
			final StringBuilder inQuery = new StringBuilder();
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(academicSessionId);
			if(!CollectionUtils.isEmpty(attendanceType)) {
				inQuery.append("and attendance_types.attendance_type_id in  ");
				inQuery.append("(");
				boolean first = true;
				for (final int type : attendanceType) {
					args.add(type);
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
			}

			if(!CollectionUtils.isEmpty(moduleSet)){
				inQuery.append("and attendance_types.attendance_head_type in ");
				inQuery.append("(");
				boolean first = true;
				for(Module moduleName : moduleSet){
					args.add(moduleName.name());
					if(first){
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
			}
			return AttendanceTypeStandardDetailsRowMapper.getAttendanceTypes(jdbcTemplate.query(String.format(GET_INSTITUTE_ATTENDANCE_TYPES, inQuery),
					args.toArray(), ATTENDANCE_TYPE_STANDARD_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error occurred while fetching attendance type for institute {}, session {}", instituteId,
					academicSessionId);
		}
		return null;
	}

	public boolean addHolidays(HolidayCalendar holidayCalendar) {
		try {
			final Boolean addedHolidays = transactionTemplate.execute(new TransactionCallback<Boolean>() {

				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					final boolean addedHolidayDetails = addHolidayDetails(holidayCalendar);
					return addedHolidayDetails;
				}
			});
			return addedHolidays;
		} catch (final Exception e) {
			logger.error("Unable to execute transaction");
		}
		return false;
	}

	protected boolean addHolidayDetails(HolidayCalendar holidayCalendar) {
		final List<Object[]> batchInsertArgs = new ArrayList<>();

		for (final EntityHolidayDates entityHolidayDates : holidayCalendar.getEntityHolidayDates()) {
			final List<Object> args = new ArrayList<>();
			args.add(holidayCalendar.getInstituteId());
			args.add(holidayCalendar.getAcademicSessionId());
			args.add(holidayCalendar.getHolidayType().name());
			args.add(entityHolidayDates.getEntityId());
			args.add(entityHolidayDates.getEntityName());
			args.add(entityHolidayDates.getHolidayDateTS() > 0
					? new Timestamp(entityHolidayDates.getHolidayDateTS() * 1000l) : null);
			args.add(entityHolidayDates.getDescription());
			batchInsertArgs.add(args.toArray());

		}
		try {
			final int[] rows = jdbcTemplate.batchUpdate(ADD_HOLIDAYS, batchInsertArgs);
			if (rows.length != holidayCalendar.getEntityHolidayDates().size()) {
				return false;
			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			
		}
		return false;
	}

	public List<HolidaysResponse> getHolidayList(int instituteId, int academicSessionid) {
		try {
			final Object[] args = { instituteId, academicSessionid };
			return jdbcTemplate.query(GET_HOLIDAY_LIST, args, HOLIDAY_CALENDAR_ROW_MAPPER);
		} catch (final DataAccessException dataAccessException) {
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			
		}

		return null;
	}

	public List<StudentAttendancePayload> saveAttendance(AttendanceRegister attendanceRegister) {
		if ((attendanceRegister == null) || CollectionUtils.isEmpty(attendanceRegister.getStudentAttendances())) {
			logger.error("Invalid attendance register {}. Skipping.", attendanceRegister);
			return null;
		}
		try {
			return transactionTemplate.execute(new TransactionCallback<List<StudentAttendancePayload>>() {

				@Override
				public List<StudentAttendancePayload> doInTransaction(TransactionStatus status) {
					List<StudentAttendancePayload> updatedRecords = saveAttendanceDetailsWithoutTransaction(attendanceRegister);
					if (updatedRecords == null) {
						throw new EmbrateRunTimeException("Unable to update attendance");
					}
					return updatedRecords;
				}
			});
		} catch (final Exception e) {
			logger.error("Unable to update attendance records {}", attendanceRegister, e);
		}
		return null;
	}

	private Timestamp getAttendanceDayStartTimestamp(int attendanceDateTS){
		return new Timestamp(DateUtils.getDayStart(attendanceDateTS, DateUtils.DEFAULT_TIMEZONE) * 1000l);
	}

	/**
	 * Must always be called within transactionß
	 * @param attendanceRegister
	 * @return
	 */
	public List<StudentAttendancePayload> saveAttendanceDetailsWithoutTransaction(AttendanceRegister attendanceRegister) {
		final List<Object[]> deleteArgs = new ArrayList<>();
		final List<Object[]> addNewRecordsArgs = new ArrayList<>();
		final List<Object> selectArgs = new ArrayList<>();

		StringBuilder selectQueryBuilder = new StringBuilder();
		String delimiter = "";
		selectQueryBuilder.append("(");
		Timestamp attendanceDayTs = getAttendanceDayStartTimestamp(attendanceRegister.getAttendanceDateTS());
		selectArgs.add(attendanceRegister.getInstituteId());
		selectArgs.add(attendanceRegister.getAcademicSessionId());
		selectArgs.add(attendanceDayTs);
		Map<UUID, Map<Integer, StudentAttendancePayload>> userAttendanceTypeMap = new HashMap<>();
		for (final StudentAttendancePayload studentAttendancePayload : attendanceRegister.getStudentAttendances()) {
			selectQueryBuilder.append(delimiter).append(" ? ");
			delimiter = ",";
			selectArgs.add(studentAttendancePayload.getStudentId().toString());
			deleteArgs.add(new Object[] { attendanceRegister.getInstituteId(),
					attendanceRegister.getAcademicSessionId(), studentAttendancePayload.getStudentId().toString(),
					studentAttendancePayload.getAttendanceType(), attendanceDayTs });

			if(!userAttendanceTypeMap.containsKey(studentAttendancePayload.getStudentId())){
				userAttendanceTypeMap.put(studentAttendancePayload.getStudentId(), new HashMap<>());
			}

			userAttendanceTypeMap.get(studentAttendancePayload.getStudentId()).put(studentAttendancePayload.getAttendanceType(), studentAttendancePayload);

			if (studentAttendancePayload.getAttendanceStatus() == null) {
				continue;
			}

			final List<Object> args = new ArrayList<>();
			args.add(attendanceRegister.getInstituteId());
			args.add(attendanceRegister.getAcademicSessionId());
			args.add(attendanceDayTs);
			args.add(studentAttendancePayload.getAttendanceType());
			args.add(studentAttendancePayload.getStudentId().toString());
			args.add(studentAttendancePayload.getAttendanceStatus().name());
			args.add(studentAttendancePayload.getRemarks());
			args.add(attendanceRegister.getCreatedBy() == null ? null : attendanceRegister.getCreatedBy().toString());
			args.add(attendanceRegister.getUpdatedBy() == null ? null : attendanceRegister.getUpdatedBy().toString());

			addNewRecordsArgs.add(args.toArray());
		}
		selectQueryBuilder.append(")");

		List<AttendanceRawRecord> existingAttendanceList = jdbcTemplate.query(String.format(GET_MULTIPLE_STUDENT_ATTENDANCE, selectQueryBuilder), selectArgs.toArray(), STUDENT_ATTENDANCE_RAW_RECORD_ROW_MAPPER);
		Map<UUID, Map<Integer, AttendanceRawRecord>> existingAttendanceTypeMap = new HashMap<>();
		for(AttendanceRawRecord attendanceRawRecord : existingAttendanceList){
			UUID studentId = attendanceRawRecord.getStudentId();
			int attendanceType = attendanceRawRecord.getAttendanceTypeId();
			if(!existingAttendanceTypeMap.containsKey(studentId)){
				existingAttendanceTypeMap.put(studentId, new HashMap<>());
			}

			existingAttendanceTypeMap.get(studentId).put(attendanceType, attendanceRawRecord);
		}

		Map<UUID, List<StudentAttendancePayload>> studentUpdateAttendanceMap = new HashMap<>();
		for (final StudentAttendancePayload studentAttendancePayload : attendanceRegister.getStudentAttendances()) {
			UUID studentId = studentAttendancePayload.getStudentId();
			int attendanceType = studentAttendancePayload.getAttendanceType();
			// Case of new attendance marking
			if(!existingAttendanceTypeMap.containsKey(studentId) ||
					!existingAttendanceTypeMap.get(studentId).containsKey(attendanceType) ||
					existingAttendanceTypeMap.get(studentId).get(attendanceType).getAttendanceStatus() != studentAttendancePayload.getAttendanceStatus()) {
				if(!studentUpdateAttendanceMap.containsKey(studentId)){
					studentUpdateAttendanceMap.put(studentId, new ArrayList<>());
				}
				studentUpdateAttendanceMap.get(studentId).add(studentAttendancePayload);
			}
		}

		jdbcTemplate.batchUpdate(DELETE_STUDENT_ATTENDANCE, deleteArgs);
		final int[] rows = jdbcTemplate.batchUpdate(ADD_STUDENT_ATTENDANCE, addNewRecordsArgs);
		if (rows.length != addNewRecordsArgs.size()) {
			return null;
		}
		for (final int rowCount : rows) {
			if (rowCount != 1) {
				return null;
			}
		}

		return Lists.newArrayList(Iterables.concat(studentUpdateAttendanceMap.values()));
	}

	public List<AttendanceRawRecord> getStudentAttendance(int instituteId, int academicSessionId, UUID studentId, int attendanceDate) {
		try {
			return jdbcTemplate.query(GET_STUDENT_ATTENDANCE, new Object[]{instituteId, academicSessionId, studentId.toString(),
					getAttendanceDayStartTimestamp(attendanceDate)}, STUDENT_ATTENDANCE_RAW_RECORD_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching attendance data for institute {}, session {}, studentId {}, date {}",
					instituteId, academicSessionId, studentId, attendanceDate, e);
		}
		return null;
	}

	public List<AttendanceRawRecord> getInstituteAttendance(int instituteId, int academicSessionId, int attendanceDate) {
		try {
			return jdbcTemplate.query(GET_INSTITUTE_ATTENDANCE, new Object[]{instituteId, academicSessionId,
					getAttendanceDayStartTimestamp(attendanceDate)}, STUDENT_ATTENDANCE_RAW_RECORD_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching attendance data for institute {}, session {}, date {}",
					instituteId, academicSessionId, attendanceDate, e);
		}
		return null;
	}


	public List<StudentAttendanceRecord> getAttendance(int instituteId, int academicSessionId, UUID standardId,
			Integer sectionId, int attendanceType, int attendanceDate) {
		try {
			if (sectionId == null) {
				final Object[] args = { attendanceType, getAttendanceDayStartTimestamp(attendanceDate), instituteId,
						academicSessionId, standardId.toString()};
				return jdbcTemplate.query(String.format(GET_STUDENT_ATTENDANCE_DETAILS, ""), args,
						STUDENT_ATTENDANCE_ROW_MAPPER);
			}
			final Object[] args = { attendanceType,getAttendanceDayStartTimestamp(attendanceDate), instituteId,
					academicSessionId, standardId.toString(), sectionId };
			return jdbcTemplate.query(
					String.format(GET_STUDENT_ATTENDANCE_DETAILS,
							" and student_academic_session_details.section_id = ? "),
					args, STUDENT_ATTENDANCE_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching attendance data for institute {}, session {}, stasndard {}, section {}, date {}",
					instituteId, academicSessionId, standardId, sectionId, attendanceDate, e);
		}

		return null;
	}
	
	public List<StudentAttendanceRecords> getAttendance(int instituteId, int academicSessionId, UUID standardId,
			Integer sectionId, List<Integer> attendanceType, int attendanceDate) {
		try {
			
			final StringBuilder inQuery = new StringBuilder();
			final List<Object> args = new ArrayList<>();

			inQuery.append("(");
			boolean first = true;
			for (final int type : attendanceType) {
				args.add(type);
				if (first) {
					inQuery.append("?");
					first = false;
					continue;
				}
				inQuery.append(", ?");
			}
			inQuery.append(")");
			
			args.add(new Timestamp(attendanceDate * 1000l));
			args.add(instituteId);
			args.add(academicSessionId);
			args.add(standardId.toString());
			String sectionCaluse = "";
			if (sectionId != null) {
				sectionCaluse  = " and student_academic_session_details.section_id = ? ";
				args.add(sectionId);
			}			
			return StudentAttendanceRowMapper.getStudentAttendanceDetails(jdbcTemplate.query(String.format( 
					GET_STUDENT_ATTENDANCE_MULTIPLE_TYPE, inQuery, sectionCaluse),
					args.toArray(), STUDENT_ATTENDANCE_WITH_SESSION_ROW_MAPPER));

		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching attendance data for institute {}, session {}, statndard {}, section {}, date {}",
					instituteId, academicSessionId, standardId, sectionId, attendanceDate, e);
		}

		return null;
	}

	
	public List<StudentAttendanceRecord> getAttendanceWithSessionDetails(int instituteId, int academicSessionid, UUID standardId,
			Integer sectionId, int attendanceType, int attendanceDate) {
		try {
			if (sectionId == null || sectionId <= 0) {
				final Object[] args = { attendanceType, getAttendanceDayStartTimestamp(attendanceDate), instituteId,
						academicSessionid, standardId.toString() };
				return jdbcTemplate.query(String.format(GET_STUDENT_ATTENDANCE_DETAILS_WITH_SESSION, ""), args,
						STUDENT_ATTENDANCE_WITH_SESSION_ROW_MAPPER);
			}
			final Object[] args = { attendanceType, getAttendanceDayStartTimestamp(attendanceDate), instituteId,
					academicSessionid, standardId.toString(), sectionId };
					
			return jdbcTemplate.query(
					String.format(GET_STUDENT_ATTENDANCE_DETAILS_WITH_SESSION,
							" and student_academic_session_details.section_id = ? "),
					args, STUDENT_ATTENDANCE_WITH_SESSION_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching attendance data for institute {}, session {}, statndard {}, section {}, date {}",
					instituteId, academicSessionid, standardId, sectionId, attendanceDate, e);
		}

		return null;
	}
	
	public List<StudentAttendanceRegisterPayload> getStudentAttendancePayload(int instituteId, int academicSessionId,
						Set<UUID> studentIdSet, Integer startDate, Integer endDate, Set<Integer> attendanceTypeIds, Set<Module> moduleSet) {
		try {
			final StringBuilder studentInQuery = new StringBuilder();
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(academicSessionId);
			String query = GET_ATTENDANCE_REGISTER_BY_STUDENT_AND_SESSION;
			if(!CollectionUtils.isEmpty(studentIdSet)) {
				query += STUDENT_ID_IN_CLAUSE;
				studentInQuery.append("(");
				boolean first = true;
				for (final UUID studentId : studentIdSet) {
					args.add(studentId.toString());
					if (first) {
						studentInQuery.append("?");
						first = false;
						continue;
					}
					studentInQuery.append(", ?");
				}
				studentInQuery.append(")");
				query = String.format(query, studentInQuery);
			}
			final StringBuilder attendanceTypeInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(attendanceTypeIds)) {
				query += ATTENDANCE_TYPE_ID_IN_CLAUSE;
				attendanceTypeInQuery.append("(");
				boolean first = true;
				for (final Integer attendanceTypeId : attendanceTypeIds) {
					args.add(attendanceTypeId);
					if (first) {
						attendanceTypeInQuery.append("?");
						first = false;
						continue;
					}
					attendanceTypeInQuery.append(", ?");
				}
				attendanceTypeInQuery.append(")");
				query = String.format(query, attendanceTypeInQuery);
			}
			if(startDate != null && startDate > 0) {
				query += START_DATE_WHERE_CLAUSE;
				args.add(new Timestamp(startDate * 1000l));
			}
			if(endDate != null && endDate > 0) {
				query += END_DATE_WHERE_CLAUSE;
				args.add(new Timestamp(endDate * 1000l));
			}

			final StringBuilder inQueryAttendanceHead = new StringBuilder();
            if(!CollectionUtils.isEmpty(moduleSet)){
				query += ATTENDANCE_HEAD_TYPE_FILTER;
				query += " %s ";
				inQueryAttendanceHead.append("(");
				boolean firstSection = true;
				for (Module module : moduleSet) {
					args.add(module.name());
					if (firstSection) {
						inQueryAttendanceHead.append("?");
						firstSection = false;
						continue;
					}
					inQueryAttendanceHead.append(", ?");
				}
				inQueryAttendanceHead.append(")");
				query = String.format(query, inQueryAttendanceHead);
			}
			return jdbcTemplate.query(query, args.toArray(), STUDENT_ATTENDANCE_REGISTER_PAYLOAD_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching attendance data for institute {}, session {}, studentId {}",
					instituteId, academicSessionId, studentIdSet, e);
		}

		return null;
	}
	
	public Map<Integer, List<StudentAttendanceRecords>> getAttendanceWithSessionDetail(int instituteId, int academicSessionId,
			List<UUID> standardIdList, List<Integer> sectionIdList, List<AttendanceStatus> attendanceStatusList, 
			List<Integer> attendanceType, Integer start, Integer end, Set<Module> moduleSet) {
		
		try {

			final List<Object> args = new ArrayList<>();
			
			String startDateFilter = "";
			if (start != null && start > 0) {
				startDateFilter = START_DATE_FILTER;
				args.add(new Timestamp(start * 1000l));
			}
			
			String endDateFilter = "";
			if (end != null && end > 0) {
				endDateFilter = END_DATE_FILTER;
				args.add(new Timestamp(end * 1000l));
			}
			
			final StringBuilder inQueryType = new StringBuilder();
			inQueryType.append("");
			if(!CollectionUtils.isEmpty(attendanceType)) {
				inQueryType.append(ATTENDACE_TYPE_FILTER);
				inQueryType.append("(");
				boolean firstType = true;
				for (final int type : attendanceType) {
					args.add(type);
					if (firstType) {
						inQueryType.append("?");
						firstType = false;
						continue;
					}
					inQueryType.append(", ?");
				}
				inQueryType.append(")");
			}
			
			final StringBuilder inQueryStatus = new StringBuilder();
			inQueryStatus.append("");
			if(!CollectionUtils.isEmpty(attendanceStatusList)) {
				inQueryStatus.append(ATTENDACE_STATUS_FILTER);
				inQueryStatus.append("(");
				boolean firstStatus = true;
				for (final AttendanceStatus attendanceStatus : attendanceStatusList) {
					args.add(attendanceStatus.name());
					if (firstStatus) {
						inQueryStatus.append("?");
						firstStatus = false;
						continue;
					}
					inQueryStatus.append(", ?");
				}
				inQueryStatus.append(")");
			}

			final StringBuilder inQueryAttendanceHead = new StringBuilder();
			if(!CollectionUtils.isEmpty(moduleSet)){
				inQueryAttendanceHead.append(ATTENDANCE_HEAD_TYPE_FILTER);
				inQueryAttendanceHead.append("(");
				boolean firstSection = true;
				for (Module module : moduleSet) {
					args.add(module.name());
					if (firstSection) {
						inQueryAttendanceHead.append("?");
						firstSection = false;
						continue;
					}
					inQueryAttendanceHead.append(", ?");
				}
				inQueryAttendanceHead.append(")");
			}
			
			args.add(instituteId);
			args.add(academicSessionId);
			
			final StringBuilder inQueryStandard = new StringBuilder();
			inQueryStandard.append("");
			if(!CollectionUtils.isEmpty(standardIdList)) {
				inQueryStandard.append(STANDARD_FILTER);
				inQueryStandard.append("(");
				boolean firstStandard = true;
				for (final UUID standardId : standardIdList) {
					args.add(standardId.toString());
					if (firstStandard) {
						inQueryStandard.append("?");
						firstStandard = false;
						continue;
					}
					inQueryStandard.append(", ?");
				}
				inQueryStandard.append(")");
			}
			
			final StringBuilder inQuerySection = new StringBuilder();
			inQuerySection.append("");
			if (!CollectionUtils.isEmpty(sectionIdList)) {
				inQuerySection.append(" and student_academic_session_details.section_id in  ");
				inQuerySection.append("(");
				boolean firstSection = true;
				for (final int sectionId : sectionIdList) {
					args.add(sectionId);
					if (firstSection) {
						inQuerySection.append("?");
						firstSection = false;
						continue;
					}
					inQuerySection.append(", ?");
				}
				inQuerySection.append(")");
			}

			return StudentAttendanceWithSessionDetailsRowMapper.getStudentAttendanceDetails(jdbcTemplate.query(String.format( 
					GET_STUDENT_ATTENDANCE_DETAILS_REPORT, startDateFilter, endDateFilter, inQueryType.toString(), 
					inQueryStatus.toString(), inQueryAttendanceHead.toString(), inQueryStandard.toString(), inQuerySection.toString()),
					args.toArray(), STUDENT_ATTENDANCE_WITH_SESSION_ROW_MAPPER));			
		} catch (final Exception e) {
			logger.error(
					"Error occurred while fetching attendance data for institute {}, session {}, start {}, end {}",
					instituteId, academicSessionId, start, end, e);
		}

		return null;
	}

    public AttendanceType checkExistingNames(int instituteId, int academicSessionId, String name) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(academicSessionId);
			args.add(name);
			List<AttendanceType> attendanceTypes =  jdbcTemplate.query(GET_STUDENT_ATTENDANCE_TYPE_NAMES, args.toArray(), ATTENDANCE_TYPE_ROW_MAPPER);
			if(CollectionUtils.isEmpty(attendanceTypes)){
				return null;
			}
			return attendanceTypes.get(0);
		} catch (final Exception e) {
			logger.error("Error occurred while fetching attendance type");
		}

		return null;
    }

	public boolean addAttendanceType(AttendanceType attendanceType, Module moduleName) {
		try {
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					if(!addAttendanceTypeMetadata(attendanceType, moduleName)){
						logger.error("Unable to add attendance type metadata");
						throw new EmbrateRunTimeException("Unable to add attendance type metadata");
					}

					if(!CollectionUtils.isEmpty(attendanceType.getStandards()) && !addStandardAttendanceTypeMapping(attendanceType)){
						logger.error("Unable to add standard attendance type mapping");
						throw new EmbrateRunTimeException("Unable to add standard attendance type mapping");
					}
					return true;
				}
			});
			return success;
		} catch (final Exception e) {
			logger.error("Error while adding attendance type", e);
		}
		return false;
	}

	private boolean addAttendanceTypeMetadata(AttendanceType attendanceType, Module moduleName) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(attendanceType.getInstituteId());
			args.add(attendanceType.getAcademicSessionId());
			args.add(attendanceType.getName());
			args.add(attendanceType.getDescription());
			args.add(attendanceType.getMetadata() == null ? null : SharedConstants.GSON.toJson(attendanceType.getMetadata()));
			args.add(moduleName.name());
			return jdbcTemplate.update(ADD_STUDENT_ATTENDANCE_TYPE, args.toArray()) == 1;
		} catch (final Exception e) {
			logger.error("Error while adding attendance type", e);
		}
		return false;

	}

	private boolean addStandardAttendanceTypeMapping(AttendanceType attendanceType) {
		try {
			final List<Object []> args = new ArrayList<>();
			int requiredMapCount = 0;
			for(UUID standardId : attendanceType.getStandards()){
				if(standardId == null){
					continue;
				}
				args.add(new Object[]{attendanceType.getInstituteId(), attendanceType.getAttendanceTypeId(), standardId.toString()});
				requiredMapCount++;
			}
			return jdbcTemplate.batchUpdate(ADD_STANDARD_ATTENDANCE_TYPE_MAPPING, args).length == requiredMapCount;
		} catch (final Exception e) {
			logger.error("Error while adding standard attendance type", e);
		}
		return false;
	}


	public boolean updateAttendanceType(AttendanceType attendanceType) {
		try {
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					if(!updateAttendanceTypeMetadata(attendanceType)){
						logger.error("Unable to update attendance type metadata");
						throw new EmbrateRunTimeException("Unable to update attendance type metadata");
					}

					if(!deleteStandardAttendanceTypeMapping(attendanceType.getInstituteId(), attendanceType.getAttendanceTypeId())){
						logger.error("Unable to delete standard attendance type mapping");
						throw new EmbrateRunTimeException("Unable to delete standard attendance type mapping");
					}

					if(!CollectionUtils.isEmpty(attendanceType.getStandards()) && !addStandardAttendanceTypeMapping(attendanceType)){
						logger.error("Unable to add standard attendance type mapping");
						throw new EmbrateRunTimeException("Unable to add standard attendance type mapping");
					}

					return true;
				}
			});
			return success;
		} catch (final Exception e) {
			logger.error("Exception while updating Attendance Type", e);
		}
		return false;
	}

	private boolean updateAttendanceTypeMetadata(AttendanceType attendanceType) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(attendanceType.getName());
			args.add(attendanceType.getDescription());
			args.add(attendanceType.getMetadata() == null ? null : SharedConstants.GSON.toJson(attendanceType.getMetadata()));
			args.add(attendanceType.getInstituteId());
			args.add(attendanceType.getAcademicSessionId());
			args.add(attendanceType.getAttendanceTypeId());
			return jdbcTemplate.update(UPDATE_STUDENT_ATTENDANCE_TYPE, args.toArray()) == 1;
		} catch (final Exception e) {
			logger.error("Exception while updating Attendance Type", e);
		}
		return false;
	}

	public boolean deleteAttendanceType(int instituteId, int academicSessionId, int attendanceTypeId) {
		try {
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					if(!deleteStandardAttendanceTypeMapping(instituteId, attendanceTypeId)){
						logger.error("Unable to delete attendance type metadata");
						throw new EmbrateRunTimeException("Unable to delete attendance type metadata");
					}

					if(!deleteAttendanceTypeMetadata(instituteId, academicSessionId, attendanceTypeId)){
						logger.error("Unable to delete standard attendance type");
						throw new EmbrateRunTimeException("Unable to delete standard attendance type");
					}
					return true;
				}
			});
			return success;
		} catch (final Exception e) {
			logger.error("Exception while deleting attendance type" , e);
		}
		return false;
	}

	private boolean deleteAttendanceTypeMetadata(int instituteId, int academicSessionId, int attendanceTypeId) {
		try {
			return jdbcTemplate.update(DELETE_STUDENT_ATTENDANCE_TYPE, instituteId, academicSessionId, attendanceTypeId) == 1;
		} catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException while deleting attendance type" , dataAccessException);
		} catch (final Exception e) {
			logger.error("Exception while deleting attendance type" , e);
		}
		return false;
	}

	private boolean deleteStandardAttendanceTypeMapping(int instituteId, int attendanceTypeId) {
		try {
			return jdbcTemplate.update(DELETE_STANDARD_ATTENDANCE_TYPE_MAPPING, instituteId, attendanceTypeId) >= 0;
		} catch (final Exception e) {
			logger.error("Exception while deleting attendance type mapping" , e);
		}
		return false;
	}
}
