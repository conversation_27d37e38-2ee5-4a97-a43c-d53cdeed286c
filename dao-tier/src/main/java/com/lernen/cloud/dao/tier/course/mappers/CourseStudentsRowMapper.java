package com.lernen.cloud.dao.tier.course.mappers;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseStudentRow;
import com.lernen.cloud.core.api.course.CourseStudents;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

public class CourseStudentsRowMapper implements RowMapper<CourseStudentRow> {

	public static final String COURSE_ID = "course_id";
	private static final String STUDENT_ID = "student_id";

	private static final CourseRowMapper COURSE_ROW_MAPPER = new CourseRowMapper();

	@Override
	public CourseStudentRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		final Course course = COURSE_ROW_MAPPER.mapRow(rs, rowNum);
		return new CourseStudentRow(course,
				StringUtils.isBlank(rs.getString(STUDENT_ID)) ? null : UUID.fromString(rs.getString(STUDENT_ID)));
	}

	/**
	 * Assumes all rows belong to same course
	 *
	 * @param courseStudentRows
	 * @return
	 */
	public static CourseStudents getCourseStudents(List<CourseStudentRow> courseStudentRows) {
		if (CollectionUtils.isEmpty(courseStudentRows)) {
			return null;
		}

		final Set<UUID> studentIds = new HashSet<>();
		for (final CourseStudentRow courseStudentRow : courseStudentRows) {
			if (courseStudentRow.getStudentId() != null) {
				studentIds.add(courseStudentRow.getStudentId());
			}
		}

		return new CourseStudents(courseStudentRows.get(0).getCourse(), studentIds);
	}

	public static List<CourseStudents> getCourseStudentsList(List<CourseStudentRow> courseStudentRows) {
		if (CollectionUtils.isEmpty(courseStudentRows)) {
			return null;
		}

		Map<UUID, List<CourseStudentRow>> studentCoursesRowMapByCourseId = new HashMap<UUID, List<CourseStudentRow>>();
		final Set<UUID> studentIds = new HashSet<>();
		for (final CourseStudentRow courseStudentRow : courseStudentRows) {
			if(studentCoursesRowMapByCourseId.containsKey(courseStudentRow.getCourse().getCourseId())) {
				studentCoursesRowMapByCourseId.get(courseStudentRow.getCourse().getCourseId()).add(courseStudentRow);
			} else {
				List<CourseStudentRow> courseStudentsRowList = new ArrayList<CourseStudentRow>();
				courseStudentsRowList.add(courseStudentRow);
				studentCoursesRowMapByCourseId.put(courseStudentRow.getCourse().getCourseId(), courseStudentsRowList);
			}
		}

		Map<UUID, CourseStudents> courseStudentsMapByCourseId = new HashMap<UUID, CourseStudents>();
		for(Map.Entry<UUID, List<CourseStudentRow>> entry : studentCoursesRowMapByCourseId.entrySet()) {
			if(!courseStudentsMapByCourseId.containsKey(entry.getKey())) {
				courseStudentsMapByCourseId.put(entry.getKey(), getCourseStudents(entry.getValue()));
			}
		}

		return new ArrayList<CourseStudents>(courseStudentsMapByCourseId.values());
	}
}