package com.lernen.cloud.dao.tier.institute.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.embrate.cloud.core.api.institute.StandardMetadata;

public class StandardMetadataRowMapper implements RowMapper<StandardMetadata> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String STANDARD_ID = "standard_id";
	private static final String ACADEMIC_SESSION_ID = "academic_session_id";
	private static final String COSCHOLASTIC_GRADE_ENABLED = "coscholastic_grade_enabled";
	private static final String SCHOLASTIC_GRADE_ENABLED = "scholastic_grade_enabled";
	private static final String ROUND_EXAM_REPORT_MARKS = "round_exam_report_marks";

	@Override
	public StandardMetadata mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new StandardMetadata(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(STANDARD_ID)),
				rs.getInt(ACADEMIC_SESSION_ID), rs.getBoolean(COSCHOLASTIC_GRADE_ENABLED),
				rs.getBoolean(SCHOLASTIC_GRADE_ENABLED), rs.getBoolean(ROUND_EXAM_REPORT_MARKS));
	}

}
