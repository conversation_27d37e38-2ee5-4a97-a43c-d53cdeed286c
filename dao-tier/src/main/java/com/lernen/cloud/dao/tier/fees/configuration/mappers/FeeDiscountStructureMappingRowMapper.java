/**
 * 
 */
package com.lernen.cloud.dao.tier.fees.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.embrate.cloud.core.api.fee.discount.structure.FeeDiscountEntityStructure;
import com.embrate.cloud.core.api.fee.discount.structure.FeeHeadDiscountStructure;
import com.embrate.cloud.core.api.fee.discount.structure.FeeIdDiscountStructure;
import com.lernen.cloud.core.api.fees.FeeConfigurationBasicInfo;
import com.lernen.cloud.core.api.fees.FeeDiscountEntityStructureDetailsRow;
import com.lernen.cloud.core.api.fees.FeeEntity;
import com.lernen.cloud.core.api.fees.FeeHeadConfiguration;

/**
 * <AUTHOR>
 *
 */
public class FeeDiscountStructureMappingRowMapper implements RowMapper<FeeDiscountEntityStructureDetailsRow> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String ENTITY_ID = "entity_id";
	private static final String ENTITY_NAME = "entity_name";
	private static final String AMOUNT = "amount";
	private static final String IS_PERCENTAGE = "is_percent";

	private static final FeeHeadConfigurationRowMapper FEE_HEAD_CONFIGURATION_ROW_MAPPER = new FeeHeadConfigurationRowMapper();
	private static final FeeConfigurationBasicInfoRowMapper FEE_CONFIGURATION_BASIC_INFO_ROW_MAPPER = new FeeConfigurationBasicInfoRowMapper();

	@Override
	public FeeDiscountEntityStructureDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		final FeeConfigurationBasicInfo feeConfigurationBasicInfo = FEE_CONFIGURATION_BASIC_INFO_ROW_MAPPER.mapRow(rs,
				rowNum);
		final FeeHeadConfiguration feeHeadConfiguration = FEE_HEAD_CONFIGURATION_ROW_MAPPER.mapRow(rs, rowNum);

		return new FeeDiscountEntityStructureDetailsRow(rs.getInt(INSTITUTE_ID), feeConfigurationBasicInfo,
				rs.getString(ENTITY_ID), FeeEntity.valueOf(rs.getString(ENTITY_NAME)), feeHeadConfiguration,
				rs.getBoolean(IS_PERCENTAGE), rs.getDouble(AMOUNT));

	}

	/**
	 * This method assumes that all elements passed in list belong to same entity id
	 *
	 * @return
	 */
	public static FeeDiscountEntityStructure getFeeAssignmentForEntity(
			List<FeeDiscountEntityStructureDetailsRow> feeAssignmentDetailsRows) {
		if (CollectionUtils.isEmpty(feeAssignmentDetailsRows)) {
			return null;
		}
		final FeeDiscountEntityStructureDetailsRow firstRow = feeAssignmentDetailsRows.get(0);
		final List<FeeConfigurationBasicInfo> feeConfigurationBasicInfos = new ArrayList<>();
		for (final FeeDiscountEntityStructureDetailsRow feeAssignmentDetailsRow : feeAssignmentDetailsRows) {
			feeConfigurationBasicInfos.add(feeAssignmentDetailsRow.getFeeConfigurationBasicInfo());
		}
		final Map<UUID, FeeConfigurationBasicInfo> feeConfigurationResponseMap = getFeeConfigurationResponseMap(
				feeConfigurationBasicInfos);

		final Map<UUID, Set<FeeHeadDiscountStructure>> feeIdVsFeeHeadAmountSetMap = getFeeIdVsFeeHeadAmountSetMap(
				feeAssignmentDetailsRows);

		final List<FeeIdDiscountStructure> feeIdFeeHeadDetailsList = new ArrayList<>();
		for (final Entry<UUID, FeeConfigurationBasicInfo> feeConfigurationEntry : feeConfigurationResponseMap
				.entrySet()) {
			final UUID feeId = feeConfigurationEntry.getKey();
			// This case should not occur
			if (!feeIdVsFeeHeadAmountSetMap.containsKey(feeId)) {
				continue;
			}
			final Set<FeeHeadDiscountStructure> feeHeadAmountDetailsSet = feeIdVsFeeHeadAmountSetMap.get(feeId);
			feeIdFeeHeadDetailsList.add(new FeeIdDiscountStructure(feeConfigurationEntry.getValue(),
					new ArrayList<>(feeHeadAmountDetailsSet)));

		}

		return new FeeDiscountEntityStructure(firstRow.getEntityId(), firstRow.getFeeEntity(), feeIdFeeHeadDetailsList);
	}

	private static Map<UUID, FeeConfigurationBasicInfo> getFeeConfigurationResponseMap(
			List<FeeConfigurationBasicInfo> feeConfigurationBasicInfos) {
		final Map<UUID, FeeConfigurationBasicInfo> feeConfigurationMap = new HashMap<>();
		for (final FeeConfigurationBasicInfo feeConfigurationBasicInfo : feeConfigurationBasicInfos) {
			feeConfigurationMap.put(feeConfigurationBasicInfo.getFeeId(), feeConfigurationBasicInfo);
		}
		return feeConfigurationMap;
	}

	/**
	 * Fetches assignment for all sessions
	 *
	 * @param feeAssignmentDetailsRows
	 * @return
	 */
	public static Map<Integer, Map<String, FeeDiscountEntityStructure>> getAllSessionFeeAssignments(
			List<FeeDiscountEntityStructureDetailsRow> feeAssignmentDetailsRows) {
		if (CollectionUtils.isEmpty(feeAssignmentDetailsRows)) {
			return new HashMap<>();
		}
		final Map<Integer, Map<String, FeeDiscountEntityStructure>> allSessionEntityFeeAssignmentMap = new HashMap<>();
		final Map<Integer, List<FeeDiscountEntityStructureDetailsRow>> sessionRowsMap = new HashMap<>();

		for (final FeeDiscountEntityStructureDetailsRow feeAssignmentDetailsRow : feeAssignmentDetailsRows) {
			final Integer academicSessionId = feeAssignmentDetailsRow.getFeeConfigurationBasicInfo()
					.getAcademicSessionId();
			if (!sessionRowsMap.containsKey(academicSessionId)) {
				sessionRowsMap.put(academicSessionId, new ArrayList<>());
			}
			sessionRowsMap.get(academicSessionId).add(feeAssignmentDetailsRow);
		}

		for (final Entry<Integer, List<FeeDiscountEntityStructureDetailsRow>> entry : sessionRowsMap.entrySet()) {
			allSessionEntityFeeAssignmentMap.put(entry.getKey(), getFeeAssignments(entry.getValue()));
		}
		return allSessionEntityFeeAssignmentMap;
	}

	public static Map<String, FeeDiscountEntityStructure> getFeeAssignments(
			List<FeeDiscountEntityStructureDetailsRow> feeAssignmentDetailsRows) {
		if (CollectionUtils.isEmpty(feeAssignmentDetailsRows)) {
			return new HashMap<>();
		}
		final Map<String, FeeDiscountEntityStructure> entityFeeAssignments = new HashMap<>();
		final Map<String, List<FeeDiscountEntityStructureDetailsRow>> entityIdVsFeeAssignmentRowListMap = getEntityIdVsFeeConfigRowList(
				feeAssignmentDetailsRows);

		final Iterator<Map.Entry<String, List<FeeDiscountEntityStructureDetailsRow>>> entityIdVsFeeAssignmentRowListMapItr = entityIdVsFeeAssignmentRowListMap
				.entrySet().iterator();
		while (entityIdVsFeeAssignmentRowListMapItr.hasNext()) {
			final Map.Entry<String, List<FeeDiscountEntityStructureDetailsRow>> entry = entityIdVsFeeAssignmentRowListMapItr
					.next();
			final FeeDiscountEntityStructure entityFeeAssignment = getFeeAssignmentForEntity(entry.getValue());
			entityFeeAssignments.put(entry.getKey(), entityFeeAssignment);
		}

		return entityFeeAssignments;
	}

	private static Map<UUID, Set<FeeHeadDiscountStructure>> getFeeIdVsFeeHeadAmountSetMap(
			List<FeeDiscountEntityStructureDetailsRow> feeAssignmentDetailsRows) {
		final Map<UUID, Set<FeeHeadDiscountStructure>> feeIdVsFeeHeadAmountSetMap = new HashMap<>();

		final Map<UUID, Map<Integer, FeeHeadDiscountStructure>> feeIdFeeHeadAmountMap = new HashMap<>();
		for (final FeeDiscountEntityStructureDetailsRow feeAssignmentRow : feeAssignmentDetailsRows) {
			final UUID feeId = feeAssignmentRow.getFeeConfigurationBasicInfo().getFeeId();
			final int feeHeadId = feeAssignmentRow.getFeeHeadConfiguration().getFeeHeadId();

			if (!feeIdFeeHeadAmountMap.containsKey(feeId)) {
				feeIdFeeHeadAmountMap.put(feeId, new HashMap<>());
			}

			feeIdFeeHeadAmountMap.get(feeId).put(feeHeadId,
					new FeeHeadDiscountStructure(feeAssignmentRow.getFeeHeadConfiguration(),
							feeAssignmentRow.getIsPercentage(), feeAssignmentRow.getAmount()));

		}

		for (final Entry<UUID, Map<Integer, FeeHeadDiscountStructure>> entry : feeIdFeeHeadAmountMap.entrySet()) {
			feeIdVsFeeHeadAmountSetMap.put(entry.getKey(), new HashSet<>(entry.getValue().values()));
		}
		return feeIdVsFeeHeadAmountSetMap;

	}

	private static Map<String, List<FeeDiscountEntityStructureDetailsRow>> getEntityIdVsFeeConfigRowList(
			List<FeeDiscountEntityStructureDetailsRow> feeAssignmentDetailsRows) {
		final Map<String, List<FeeDiscountEntityStructureDetailsRow>> entityIdVsFeeAssignmentRowListMap = new HashMap<>();
		for (final FeeDiscountEntityStructureDetailsRow feeAssignmentRow : feeAssignmentDetailsRows) {
			if (entityIdVsFeeAssignmentRowListMap.containsKey(feeAssignmentRow.getEntityId())) {
				entityIdVsFeeAssignmentRowListMap.get(feeAssignmentRow.getEntityId()).add(feeAssignmentRow);
			} else {
				final List<FeeDiscountEntityStructureDetailsRow> entityFeeAssignmentRows = new ArrayList<>();
				entityFeeAssignmentRows.add(feeAssignmentRow);
				entityIdVsFeeAssignmentRowListMap.put(feeAssignmentRow.getEntityId(), entityFeeAssignmentRows);
			}
		}
		return entityIdVsFeeAssignmentRowListMap;
	}

}
