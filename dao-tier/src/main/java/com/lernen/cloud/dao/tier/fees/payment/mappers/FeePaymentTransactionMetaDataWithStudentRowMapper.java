package com.lernen.cloud.dao.tier.fees.payment.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionMetaData;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentDetailedRow;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentWithoutSessionRowMapper;

public class FeePaymentTransactionMetaDataWithStudentRowMapper implements RowMapper<FeePaymentTransactionMetaData> {

	private static final FeePaymentTransactionMetaDataRowMapper FEE_PAYMENT_TRANSACTION_META_DATA_ROW_MAPPER = new FeePaymentTransactionMetaDataRowMapper();
	private static final StudentWithoutSessionRowMapper STUDENT_WITHOUT_SESSION_ROW_MAPPER = new StudentWithoutSessionRowMapper();

	@Override
	public FeePaymentTransactionMetaData mapRow(ResultSet rs, int rowNum) throws SQLException {
		FeePaymentTransactionMetaData feePaymentTransactionMetaData = FEE_PAYMENT_TRANSACTION_META_DATA_ROW_MAPPER
				.mapRow(rs, rowNum);
		StudentDetailedRow studentDetailedRow = STUDENT_WITHOUT_SESSION_ROW_MAPPER.mapRow(rs, rowNum);

		final StudentLite student = Student.getStudentLite(StudentRowMapper.getStudent(studentDetailedRow));
		feePaymentTransactionMetaData.setStudent(student);
		
		return feePaymentTransactionMetaData;
	}
}
