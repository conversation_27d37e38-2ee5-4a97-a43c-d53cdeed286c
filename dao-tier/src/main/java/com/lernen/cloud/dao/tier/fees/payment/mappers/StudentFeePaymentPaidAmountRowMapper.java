package com.lernen.cloud.dao.tier.fees.payment.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import com.lernen.cloud.core.api.student.StudentStatus;
import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.fees.payment.StudentFeePaymentAggregatedPaidAmount;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentFeePaymentPaidAmountRowMapper implements RowMapper<StudentFeePaymentAggregatedPaidAmount> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String STUDENT_ID = "student_id";
	private static final String ADMISSION_NUMBER = "admission_number";
	private static final String STUDENT_NAME = "name";
	private static final String STANDARD_NAME = "standard_name";
	private static final String SECTION_NAME = "section_name";
	private static final String FEE_ID = "fee_id";
	// private static final String FEE_NAME = "fee_name";
	private static final String FEE_HEAD_ID = "fee_head_id";
	private static final String STATUS = "status";

	private static final String PAID_AMOUNT = "paid_amount";
	private static final String INSTANT_DISCOUNT_AMOUNT = "instant_discount_amount";
	private static final String ASSIGNED_DISCOUNT_AMOUNT = "assigned_discount_amount";
	private static final String PAID_FINE_AMOUNT = "paid_fine_amount";

	@Override
	public StudentFeePaymentAggregatedPaidAmount mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new StudentFeePaymentAggregatedPaidAmount(rs.getInt(INSTITUTE_ID),
				UUID.fromString(rs.getString(STUDENT_ID)), rs.getString(ADMISSION_NUMBER), rs.getString(STUDENT_NAME),
				rs.getString(STANDARD_NAME), rs.getString(SECTION_NAME), UUID.fromString(rs.getString(FEE_ID)), null,
				rs.getInt(FEE_HEAD_ID), rs.getDouble(PAID_AMOUNT), rs.getDouble(INSTANT_DISCOUNT_AMOUNT),
				rs.getDouble(ASSIGNED_DISCOUNT_AMOUNT), rs.getDouble(PAID_FINE_AMOUNT), StudentStatus.getStudentStatus(rs.getString(STATUS)));
	}

}
