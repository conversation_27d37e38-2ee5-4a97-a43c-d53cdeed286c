/**
 *
 */
package com.lernen.cloud.dao.tier.examination.mapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.examination.report.greensheet.GreenSheetClassStructure;
import com.lernen.cloud.core.api.examination.report.greensheet.GreenSheetCourseStructure;
import com.lernen.cloud.core.api.examination.report.greensheet.GreenSheetExamDimensionMapData;
import com.lernen.cloud.core.api.examination.report.greensheet.GreenSheetStructureData;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamGreenSheetStructureRowMapper
		implements RowMapper<GreenSheetClassStructure<GreenSheetExamDimensionMapData>> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String ACADEMIC_SESSION_ID = "academic_session_id";
	private static final String STANDARD_ID = "standard_id";
	private static final String GREEN_EXAM_STRUCTURE = "green_sheet_structure";
	private static final Gson GSON = new Gson();

	@Override
	public GreenSheetClassStructure<GreenSheetExamDimensionMapData> mapRow(ResultSet rs, int rowNum)
			throws SQLException {

		String examStructure = rs.getString(GREEN_EXAM_STRUCTURE);
		if (StringUtils.isBlank(examStructure)) {
			return null;
		}
		Type type = new TypeToken<GreenSheetStructureData<GreenSheetExamDimensionMapData>>() {
		}.getType();
		GreenSheetStructureData<GreenSheetExamDimensionMapData> greenSheetCourseStructures = GSON
				.fromJson(rs.getString(GREEN_EXAM_STRUCTURE), type);

		return new GreenSheetClassStructure<GreenSheetExamDimensionMapData>(rs.getInt(INSTITUTE_ID),
				rs.getInt(ACADEMIC_SESSION_ID), UUID.fromString(rs.getString(STANDARD_ID)), greenSheetCourseStructures);

	}

}