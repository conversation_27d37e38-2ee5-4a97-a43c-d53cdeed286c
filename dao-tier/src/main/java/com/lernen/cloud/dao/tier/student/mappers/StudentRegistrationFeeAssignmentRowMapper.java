package com.lernen.cloud.dao.tier.student.mappers;

import com.embrate.cloud.core.api.student.registration.StudentRegistrationFeeAssignmentRow;
import com.embrate.cloud.core.api.student.registration.StudentRegistrationPaymentData;
import com.embrate.cloud.core.api.student.registration.StudentRegistrationPaymentStatus;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.common.AreaType;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.fees.FeeHeadAmount;
import com.lernen.cloud.core.api.fees.FeeIdFeeHead;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.user.BloodGroup;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.utils.UUIDUtils;
import com.lernen.cloud.dao.tier.institute.mappers.AcademicSessionRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.units.qual.A;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
public class StudentRegistrationFeeAssignmentRowMapper implements RowMapper<StudentRegistrationFeeAssignmentRow> {
	private static final Logger logger = LogManager.getLogger(StudentRegistrationFeeAssignmentRowMapper.class);

	private static final String FEE_ID = "fee_id";
	private static final String FEE_HEAD_ID = "fee_head_id";
	private static final String AMOUNT = "amount";


	@Override
	public StudentRegistrationFeeAssignmentRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new StudentRegistrationFeeAssignmentRow(UUIDUtils.getUUID(rs.getString(FEE_ID)), rs.getInt(FEE_HEAD_ID), rs.getDouble(AMOUNT));
	}

	public static List<FeeIdFeeHead> getFeeAssignment(List<StudentRegistrationFeeAssignmentRow> rows) {
		if (CollectionUtils.isEmpty(rows)) {
			return new ArrayList<>();
		}
		Map<UUID, List<StudentRegistrationFeeAssignmentRow>> feeIdMap = new HashMap<>();
		for (StudentRegistrationFeeAssignmentRow row : rows) {
			UUID feeId = row.getFeeId();
			List<StudentRegistrationFeeAssignmentRow> feeHeadRows = feeIdMap.getOrDefault(feeId, new ArrayList<>());
			feeHeadRows.add(row);
			feeIdMap.put(feeId, feeHeadRows);
		}
		List<FeeIdFeeHead> feeIdFeeHeadList = new ArrayList<>();
		for (Map.Entry<UUID, List<StudentRegistrationFeeAssignmentRow>> entry : feeIdMap.entrySet()) {
			List<FeeHeadAmount> feeHeadAmountList = new ArrayList<>();
			for (StudentRegistrationFeeAssignmentRow row : entry.getValue()) {
				feeHeadAmountList.add(new FeeHeadAmount(row.getFeeHeadId(), row.getAmount()));
			}
			feeIdFeeHeadList.add(new FeeIdFeeHead(entry.getKey(), feeHeadAmountList));
		}
		return feeIdFeeHeadList;
	}
}
