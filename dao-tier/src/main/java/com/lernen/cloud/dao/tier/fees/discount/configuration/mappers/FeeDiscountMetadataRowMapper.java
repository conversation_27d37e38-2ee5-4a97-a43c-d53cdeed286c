package com.lernen.cloud.dao.tier.fees.discount.configuration.mappers;

import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.fee.discount.DiscountStructureType;
import com.lernen.cloud.core.api.fee.discount.FeeDiscountMetadata;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeDiscountMetadataRowMapper implements RowMapper<FeeDiscountMetadata> {

	private static final String INSTITUTE_ID = "fee_discount_structure_metadata.institute_id";
	public static final String ACADEMIC_SESSION_ID = "fee_discount_structure_metadata.academic_session_id";
	private static final String DISCOUNT_NAME = "fee_discount_structure_metadata.structure_name";
	private static final String DISCOUNT_ID = "fee_discount_structure_metadata.structure_id";
	private static final String DESCRIPTION = "fee_discount_structure_metadata.description";
	private static final String STRUCTURE_TYPE = "fee_discount_structure_metadata.structure_type";
	private static final String METADATA = "fee_discount_structure_metadata.metadata";

	@Override
	public FeeDiscountMetadata mapRow(ResultSet rs, int rowNum) throws SQLException {
		if(rs.getString(DISCOUNT_ID) == null) {
			return null;
		}

		final String metadataStr = rs.getString(METADATA);
		Map<String, String> metadata = null;
		if (!StringUtils.isBlank(metadataStr)) {
			final Type collectionType = new TypeToken<Map<String, String>>() {
			}.getType();
			metadata = GSON.fromJson(metadataStr, collectionType);
		}
		String discountName = rs.getString(DISCOUNT_NAME);
		DiscountStructureType structureType = DiscountStructureType.valueOf(rs.getString(STRUCTURE_TYPE));
		if (structureType == DiscountStructureType.SYSTEM){
			//TODO : Fix the title tag handling. Currently directly getting controlled from FE
			discountName = metadata.get("title");
		}
		return new FeeDiscountMetadata(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
				UUID.fromString(rs.getString(DISCOUNT_ID)),discountName, structureType
				, rs.getString(DESCRIPTION), metadata);
	}
}
