package com.lernen.cloud.dao.tier.student.mappers;

import com.embrate.cloud.core.api.dashboards.admission.StudentAdmTCCount;
import com.embrate.cloud.core.api.dashboards.admission.StudentCountSummary;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
public class StudentAdmTCCountRowMapper implements RowMapper<StudentAdmTCCount> {

	protected static final String INSTITUTE_ID = "institute_id";
	protected static final String NEW_ADM_STUDENT_COUNT = "new_admissions_count";
	protected static final String RELIEVE_STUDENT_COUNT = "relieved_students_count";

	@Override
	public StudentAdmTCCount mapRow(ResultSet rs, int rowNum) throws SQLException {
		int instituteId = rs.getInt(INSTITUTE_ID);
		int admCount = rs.getInt(NEW_ADM_STUDENT_COUNT);
		int reliveCount = rs.getInt(RELIEVE_STUDENT_COUNT);

		return new StudentAdmTCCount(instituteId, admCount, reliveCount);
	}
}
