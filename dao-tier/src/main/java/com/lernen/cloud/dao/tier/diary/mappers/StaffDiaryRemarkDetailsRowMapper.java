package com.lernen.cloud.dao.tier.diary.mappers;

import com.amazonaws.util.CollectionUtils;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.diary.*;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserLite;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import com.lernen.cloud.dao.tier.user.mappers.UserRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

public class StaffDiaryRemarkDetailsRowMapper implements RowMapper<StaffDiaryRemarkDetailsRow> {

    private static final UserRowMapper USER_ROW_MAPPER = new UserRowMapper();
    private static final DiaryRemarkCategoryRowMapper DIARY_REMARK_CATEGORY_ROW_MAPPER = new DiaryRemarkCategoryRowMapper();
    private static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();

    private static final String INSTITUTE_ID = "remark_details.institute_id";
    private static final String ACADEMIC_SESSION_ID = "remark_details.academic_session_id";
    private static final String DATE = "remark_details.date";
    private static final String REMARK_ID = "remark_details.remark_id";
    private static final String TITLE = "remark_details.title";
    private static final String DESCRIPTION = "remark_details.description";

    protected static final String ATTACHMENTS = "remark_details.attachments";

    protected static final String REMARK_USER_TYPE = "remark_details.remark_user_type";

    protected static final String CREATED_TIMESTAMP = "remark_details.created_at";
    protected static final String UPDATED_TIMESTAMP = "remark_details.updated_at";

    @Override
    public StaffDiaryRemarkDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        final User user = USER_ROW_MAPPER.mapRow(rs, rowNum);
        final UserLite userLite = User.getUserLite(user);
        final DiaryRemarkCategory diaryRemarkCategory = DIARY_REMARK_CATEGORY_ROW_MAPPER.mapRow(rs, rowNum);
        final Staff staff = STAFF_ROW_MAPPER.mapRow(rs, rowNum);

        final Timestamp remarkDate = rs.getTimestamp(DATE);
        final Integer remarkDateTime = remarkDate == null ? null
                : (int) (remarkDate.getTime() / 1000l);

        final Timestamp createdTimestamp = rs.getTimestamp(CREATED_TIMESTAMP);
        final Integer createdTimestampTime = createdTimestamp == null ? null
                : (int) (createdTimestamp.getTime() / 1000l);

        final Timestamp updatedTimestamp = rs.getTimestamp(UPDATED_TIMESTAMP);
        final Integer updatedTimestampTime = updatedTimestamp == null ? null
                : (int) (updatedTimestamp.getTime() / 1000l);

        StaffLite staffLite = Staff.getStaffLite(staff);

        int attachmentCount = 10;
        long attachmentSize = 1024 * 1024 * 5;
        String allowedMimeTypes = "image/*,application/pdf";

        final String documents = rs.getString(ATTACHMENTS);
        List<Document<DiaryRemarkDocumentType>> diaryRemarkAttachments = null;
        if (!StringUtils.isBlank(documents)) {
            final Type collectionType = new TypeToken<List<Document<DiaryRemarkDocumentType>>>() {
            }.getType();
            diaryRemarkAttachments = GSON.fromJson(documents, collectionType);
        }

        return new StaffDiaryRemarkDetailsRow(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID), remarkDateTime, UUID.fromString(rs.getString(REMARK_ID)),
                rs.getString(TITLE), rs.getString(DESCRIPTION), staffLite, RemarkUserType.getRemarkUserType(rs.getString(REMARK_USER_TYPE)),
                userLite, createdTimestampTime, updatedTimestampTime, diaryRemarkCategory, attachmentCount, attachmentSize, allowedMimeTypes, diaryRemarkAttachments);
    }

    public static List<StaffDiaryRemarkDetails> diaryRemarkStaffsDataList(List<StaffDiaryRemarkDetailsRow> diaryRemarkDetailsRows) {
        if(CollectionUtils.isNullOrEmpty(diaryRemarkDetailsRows)) {
            return null;
        }
        Map<UUID, List<StaffLite>> remarkStaffMap = new HashMap<>();
        Map<UUID, List<StaffLite>> remarkStaffIdMap = new HashMap<>();
        List<StaffDiaryRemarkDetails> diaryRemarkDetails = new ArrayList<>();
        for (StaffDiaryRemarkDetailsRow diaryRemarkDetailsRow : diaryRemarkDetailsRows) {
            StaffLite staffLite = diaryRemarkDetailsRow.getStaffLite();
            UUID staffId = staffLite.getStaffId();
            UUID remarkId = diaryRemarkDetailsRow.getRemarkId();
            if (remarkStaffMap.containsKey(remarkId)) {
                if (!remarkStaffIdMap.containsKey(staffId)) {
                    List<StaffLite> staffLites = remarkStaffMap.get(remarkId);
                    remarkStaffIdMap.put(staffId, staffLites);
                    staffLites.add(staffLite);
                }
            } else {
                remarkStaffIdMap = new HashMap<>();
                List<StaffLite> staffLites = new ArrayList<>();
                staffLites.add(staffLite);
                remarkStaffIdMap.put(staffId, staffLites);
                remarkStaffMap.put(remarkId, staffLites);
                diaryRemarkDetails.add(new StaffDiaryRemarkDetails(diaryRemarkDetailsRow.getInstituteId(), diaryRemarkDetailsRow.getAcademicSessionId(),diaryRemarkDetailsRow.getDate(), diaryRemarkDetailsRow.getRemarkId(),
                        diaryRemarkDetailsRow.getTitle(), diaryRemarkDetailsRow.getDescription(),
                        remarkStaffMap.get(remarkId), diaryRemarkDetailsRow.getRemarkUserType(), diaryRemarkDetailsRow.getCreatedByUser(),
                        diaryRemarkDetailsRow.getCreatedTimestamp(), diaryRemarkDetailsRow.getUpdatedTimestamp(), diaryRemarkDetailsRow.getDiaryRemarkCategory(),
                        diaryRemarkDetailsRow.getAttachmentCount(), diaryRemarkDetailsRow.getAttachmentSize(),
                        diaryRemarkDetailsRow.getAllowedMimeTypes(), diaryRemarkDetailsRow.getDiaryRemarkAttachments()));
            }
        }
        return diaryRemarkDetails;
    }

    public static List<StaffDiaryRemarkDetails> diaryRemarkSingleStaffsDataList(List<StaffDiaryRemarkDetailsRow> diaryRemarkDetailsRows){
        if(CollectionUtils.isNullOrEmpty(diaryRemarkDetailsRows)) {
            return null;
        }
        Map<UUID, List<StaffLite>> remarkStaffMap = new HashMap<>();
        Map<UUID, List<StaffLite>> remarkStaffIdMap = new HashMap<>();
        List<StaffDiaryRemarkDetails> diaryRemarkDetails = new ArrayList<>();
        for (StaffDiaryRemarkDetailsRow diaryRemarkDetailsRow : diaryRemarkDetailsRows) {
            StaffLite staffLite = diaryRemarkDetailsRow.getStaffLite();
            UUID staffId = staffLite.getStaffId();
            UUID remarkId = diaryRemarkDetailsRow.getRemarkId();
            if (remarkStaffMap.containsKey(remarkId)) {
                if (!remarkStaffIdMap.containsKey(staffId)) {
                    List<StaffLite> staffLites = remarkStaffMap.get(remarkId);
                    remarkStaffIdMap.put(staffId, staffLites);
                    staffLites.add(staffLite);
                }
            } else {
                List<StaffLite> staffLites = new ArrayList<>();
                staffLites.add(staffLite);
                remarkStaffIdMap.put(staffId, staffLites);
                remarkStaffMap.put(remarkId, staffLites);
                diaryRemarkDetails.add(new StaffDiaryRemarkDetails(diaryRemarkDetailsRow.getInstituteId(), diaryRemarkDetailsRow.getAcademicSessionId(),diaryRemarkDetailsRow.getDate(), diaryRemarkDetailsRow.getRemarkId(),
                        diaryRemarkDetailsRow.getTitle(), diaryRemarkDetailsRow.getDescription(),
                        remarkStaffIdMap.get(remarkId), diaryRemarkDetailsRow.getRemarkUserType(), diaryRemarkDetailsRow.getCreatedByUser(),
                        diaryRemarkDetailsRow.getCreatedTimestamp(), diaryRemarkDetailsRow.getUpdatedTimestamp(), diaryRemarkDetailsRow.getDiaryRemarkCategory(),
                        diaryRemarkDetailsRow.getAttachmentCount(), diaryRemarkDetailsRow.getAttachmentSize(),
                        diaryRemarkDetailsRow.getAllowedMimeTypes(), diaryRemarkDetailsRow.getDiaryRemarkAttachments()));
            }
        }
        return diaryRemarkDetails;
    }

    public static StaffDiaryRemarkDetails getDiaryRemarkDetailOfARemark(List<StaffDiaryRemarkDetailsRow> diaryRemarkDetailsRows) {
        if(CollectionUtils.isNullOrEmpty(diaryRemarkDetailsRows)) {
            return null;
        }
        Map<UUID, StaffLite> staffLiteMap = new HashMap<>();
        for (StaffDiaryRemarkDetailsRow diaryRemarkDetailsRow : diaryRemarkDetailsRows) {
            StaffLite staffLite = diaryRemarkDetailsRow.getStaffLite();
            UUID staffId = staffLite.getStaffId();
            if(!staffLiteMap.containsKey(staffId)) {
                staffLiteMap.put(staffId, staffLite);
            }
        }

        StaffDiaryRemarkDetailsRow diaryRemarkDetailsRow = diaryRemarkDetailsRows.get(0);
        return new StaffDiaryRemarkDetails(diaryRemarkDetailsRow.getInstituteId(), diaryRemarkDetailsRow.getAcademicSessionId(),
                diaryRemarkDetailsRow.getDate(), diaryRemarkDetailsRow.getRemarkId(), diaryRemarkDetailsRow.getTitle(),
                diaryRemarkDetailsRow.getDescription(), new ArrayList<>(staffLiteMap.values()), diaryRemarkDetailsRow.getRemarkUserType(),
                diaryRemarkDetailsRow.getCreatedByUser(), diaryRemarkDetailsRow.getCreatedTimestamp(), diaryRemarkDetailsRow.getUpdatedTimestamp(),
                diaryRemarkDetailsRow.getDiaryRemarkCategory(),
                diaryRemarkDetailsRow.getAttachmentCount(), diaryRemarkDetailsRow.getAttachmentSize(),
                diaryRemarkDetailsRow.getAllowedMimeTypes(), diaryRemarkDetailsRow.getDiaryRemarkAttachments());
    }
}
