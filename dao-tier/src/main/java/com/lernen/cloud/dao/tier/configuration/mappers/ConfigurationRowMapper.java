package com.lernen.cloud.dao.tier.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.configurations.Configuration;
import com.lernen.cloud.core.api.configurations.Entity;

public class ConfigurationRowMapper implements RowMapper<Configuration> {

	private static final String ENTITY_NAME = "entity";
	private static final String ENTITY_ID = "entity_id";
	private static final String CONFIG_TYPE = "config_type";
	private static final String CONFIG_KEY = "config_key";
	private static final String CONFIG_VALUE = "config_value";

	@Override
	public Configuration mapRow(ResultSet rs, int rowNum) throws SQLException {
		final String key = rs.getString(CONFIG_KEY);
		final String value = rs.getString(CONFIG_VALUE);
		final Map<String, String> configKeyValues = new HashMap<>();
		configKeyValues.put(key, value);

		return new Configuration(Entity.valueOf(rs.getString(ENTITY_NAME)), rs.getString(ENTITY_ID),
				rs.getString(CONFIG_TYPE), configKeyValues);
	}

	/**
	 * All the configuration must belong to same entity and config type
	 *
	 * @param configurations
	 * @return
	 */
	public static Configuration getConfiguration(List<Configuration> configurations) {
		if (CollectionUtils.isEmpty(configurations)) {
			return null;
		}
		final Configuration combinedConfiguration = new Configuration(configurations.get(0).getEntity(),
				configurations.get(0).getEntityId(), configurations.get(0).getConfigType(), new HashMap<>());
		for (final Configuration configuration : configurations) {
			combinedConfiguration.getKeyValues().putAll(configuration.getKeyValues());
		}

		return combinedConfiguration;
	}
}
