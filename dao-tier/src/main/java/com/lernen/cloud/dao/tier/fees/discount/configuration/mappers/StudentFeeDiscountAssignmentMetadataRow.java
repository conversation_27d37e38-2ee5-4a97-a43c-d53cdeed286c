package com.lernen.cloud.dao.tier.fees.discount.configuration.mappers;

import com.lernen.cloud.core.api.fee.discount.FeeDiscountMetadata;
import com.lernen.cloud.core.api.student.StudentLite;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentFeeDiscountAssignmentMetadataRow {

	private final StudentLite studentLite;

	private final FeeDiscountMetadata feeDiscountMetadata;

	public StudentFeeDiscountAssignmentMetadataRow(StudentLite studentLite, FeeDiscountMetadata feeDiscountMetadata) {
		this.studentLite = studentLite;
		this.feeDiscountMetadata = feeDiscountMetadata;
	}

	public StudentLite getStudentLite() {
		return studentLite;
	}

	public FeeDiscountMetadata getFeeDiscountMetadata() {
		return feeDiscountMetadata;
	}

	@Override
	public String toString() {
		return "StudentFeeDiscountAssignmentMetadataRow [studentLite=" + studentLite + ", feeDiscountMetadata="
				+ feeDiscountMetadata + "]";
	}

}
